# 🎥 فيديو تعليمي نصي: إنشاء قاعدة البيانات

## 📺 كأنك تشاهد فيديو خطوة بخطوة!

---

## 🎬 المشهد 1: الدخول إلى Firebase Console

### ⏰ الدقيقة 0:00 - 0:30
```
👨‍💻 المدرب: "مرحباً! اليوم سنتعلم كيفية إنشاء قاعدة بيانات Firebase لمشروع jorinvforex"

🖱️ [يفتح المتصفح]
🖱️ [يكتب في شريط العنوان]: https://console.firebase.google.com

👨‍💻 المدرب: "أولاً، ندخل إلى Firebase Console"

🖱️ [يضغط Enter]
📱 [تظهر صفحة Firebase Console]

👨‍💻 المدرب: "إذا لم تكن مسجل دخول، سجل دخول بحساب Google"
```

---

## 🎬 المشهد 2: اختيار المشروع

### ⏰ الدقيقة 0:30 - 1:00
```
👨‍💻 المدرب: "الآن نحتاج للدخول إلى مشروع jorinvforex"

🖱️ [يضغط على قائمة المشاريع في الأعلى]
📱 [تظهر قائمة المشاريع]

👨‍💻 المدرب: "نبحث عن مشروع jorinvforex ونضغط عليه"

🖱️ [يضغط على jorinvforex]
📱 [تفتح صفحة المشروع الرئيسية]

👨‍💻 المدرب: "ممتاز! الآن نحن داخل مشروع jorinvforex"
```

---

## 🎬 المشهد 3: إنشاء Firestore Database

### ⏰ الدقيقة 1:00 - 2:30
```
👨‍💻 المدرب: "الآن سننشئ قاعدة بيانات Firestore"

🖱️ [ينظر إلى القائمة الجانبية اليسرى]
👨‍💻 المدرب: "في القائمة الجانبية، نبحث عن 'Firestore Database'"

🖱️ [يضغط على Firestore Database]
📱 [تفتح صفحة Firestore فارغة]

👨‍💻 المدرب: "نرى صفحة تقول 'Get started with Cloud Firestore'"

🖱️ [يضغط على زر "Create database"]
📱 [تظهر نافذة اختيار نوع الأمان]

👨‍💻 المدرب: "هنا نختار 'Start in production mode' للأمان"

🖱️ [يختار "Start in production mode"]
🖱️ [يضغط "Next"]

📱 [تظهر صفحة اختيار المنطقة]

👨‍💻 المدرب: "الآن نختار المنطقة الجغرافية"
👨‍💻 المدرب: "للشرق الأوسط، أنصح بـ europe-west1 أو us-central1"

🖱️ [يختار us-central1]
🖱️ [يضغط "Done"]

📱 [تظهر رسالة "Creating your database..."]

👨‍💻 المدرب: "الآن ننتظر دقيقة أو دقيقتين حتى يتم إنشاء قاعدة البيانات"

⏳ [انتظار...]

📱 [تظهر واجهة Firestore فارغة مع "No documents yet"]

👨‍💻 المدرب: "ممتاز! تم إنشاء قاعدة البيانات بنجاح!"
```

---

## 🎬 المشهد 4: تفعيل Authentication

### ⏰ الدقيقة 2:30 - 3:30
```
👨‍💻 المدرب: "الآن نحتاج لتفعيل نظام المصادقة"

🖱️ [يضغط على "Authentication" في القائمة الجانبية]
📱 [تفتح صفحة Authentication]

👨‍💻 المدرب: "نرى صفحة تقول 'Get started with Firebase Authentication'"

🖱️ [يضغط على "Get started"]
📱 [تفتح صفحة Authentication مع تبويبات]

👨‍💻 المدرب: "نذهب إلى تبويب 'Sign-in method'"

🖱️ [يضغط على تبويب "Sign-in method"]
📱 [تظهر قائمة طرق تسجيل الدخول]

👨‍💻 المدرب: "نبحث عن 'Email/Password' ونضغط عليه"

🖱️ [يضغط على "Email/Password"]
📱 [تفتح نافذة إعدادات Email/Password]

👨‍💻 المدرب: "نفعل الخيار الأول 'Email/Password'"

🖱️ [يفعل المفتاح الأول]
🖱️ [يضغط "Save"]

📱 [تظهر رسالة نجاح]

👨‍💻 المدرب: "رائع! تم تفعيل تسجيل الدخول بالإيميل وكلمة المرور"
```

---

## 🎬 المشهد 5: تفعيل Storage

### ⏰ الدقيقة 3:30 - 4:30
```
👨‍💻 المدرب: "الآن نحتاج لتفعيل Storage لحفظ الصور"

🖱️ [يضغط على "Storage" في القائمة الجانبية]
📱 [تفتح صفحة Storage]

👨‍💻 المدرب: "نرى صفحة تقول 'Get started with Cloud Storage'"

🖱️ [يضغط على "Get started"]
📱 [تظهر نافذة اختيار قواعد الأمان]

👨‍💻 المدرب: "نختار 'Start in production mode' للأمان"

🖱️ [يختار "Start in production mode"]
🖱️ [يضغط "Next"]

📱 [تظهر صفحة اختيار المنطقة]

👨‍💻 المدرب: "نختار نفس المنطقة التي اخترناها لـ Firestore"

🖱️ [يختار us-central1]
🖱️ [يضغط "Done"]

📱 [تظهر رسالة "Setting up Cloud Storage..."]

⏳ [انتظار...]

📱 [تظهر واجهة Storage فارغة]

👨‍💻 المدرب: "ممتاز! تم تفعيل Storage بنجاح!"
```

---

## 🎬 المشهد 6: إنشاء Web App

### ⏰ الدقيقة 4:30 - 6:00
```
👨‍💻 المدرب: "الآن نحتاج لإنشاء Web App والحصول على مفاتيح API"

🖱️ [يضغط على أيقونة الترس ⚙️ في القائمة الجانبية]
🖱️ [يختار "Project settings"]
📱 [تفتح صفحة إعدادات المشروع]

👨‍💻 المدرب: "نتأكد أننا في تبويب 'General'"

📱 [يظهر تبويب General مفتوح]

👨‍💻 المدرب: "ننزل للأسفل حتى نجد قسم 'Your apps'"

🖱️ [ينزل للأسفل]
📱 [يظهر قسم "Your apps" فارغ]

👨‍💻 المدرب: "نضغط على أيقونة الويب </>"

🖱️ [يضغط على أيقونة </>]
📱 [تفتح نافذة إنشاء Web App]

👨‍💻 المدرب: "نكتب اسم التطبيق: 'منصة المحللين الماليين'"

⌨️ [يكتب: منصة المحللين الماليين]

👨‍💻 المدرب: "يمكننا تفعيل Firebase Hosting إذا أردنا"

🖱️ [يفعل خيار "Also set up Firebase Hosting" - اختياري]
🖱️ [يضغط "Register app"]

📱 [تظهر صفحة الإعدادات]

👨‍💻 المدرب: "الآن نرى إعدادات Firebase - هذا مهم جداً!"

📱 [تظهر الإعدادات]:
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "jorinvforex.firebaseapp.com",
  projectId: "jorinvforex",
  storageBucket: "jorinvforex.appspot.com",
  messagingSenderId: "862703765675",
  appId: "1:862703765675:web:XXXXXXXXXXXXXXXX"
};
```

👨‍💻 المدرب: "نحتاج لنسخ هذه الإعدادات - خاصة apiKey و appId"

🖱️ [يحدد النص وينسخه]

👨‍💻 المدرب: "احفظوا هذه الإعدادات في مكان آمن!"

🖱️ [يضغط "Continue to console"]
```

---

## 🎬 المشهد 7: التحقق من الإعداد

### ⏰ الدقيقة 6:00 - 7:00
```
👨‍💻 المدرب: "الآن دعونا نتحقق من أن كل شيء يعمل"

📱 [يعود إلى صفحة المشروع الرئيسية]

👨‍💻 المدرب: "نرى في لوحة التحكم:"

📱 [يظهر]:
✅ Authentication: Enabled
✅ Firestore Database: 1 database
✅ Storage: 1 bucket
✅ Web Apps: 1 app

👨‍💻 المدرب: "ممتاز! جميع الخدمات مفعلة بنجاح"

👨‍💻 المدرب: "الآن يمكننا استخدام هذه الإعدادات في الكود"
```

---

## 🎬 المشهد 8: الخطوات التالية

### ⏰ الدقيقة 7:00 - 8:00
```
👨‍💻 المدرب: "الآن بعد إنشاء قاعدة البيانات، الخطوات التالية هي:"

📝 [يعرض قائمة]:
1. ✅ إنشاء Firestore Database
2. ✅ تفعيل Authentication  
3. ✅ تفعيل Storage
4. ✅ إنشاء Web App والحصول على المفاتيح
5. ⏳ تحديث الإعدادات في الكود
6. ⏳ اختبار المنصة

👨‍💻 المدرب: "في الفيديو التالي سنتعلم كيفية تحديث الكود"

👨‍💻 المدرب: "أو يمكنكم فتح setup-project.html واتباع التعليمات"

👨‍💻 المدرب: "شكراً لكم ونراكم في الفيديو القادم!"
```

---

## 📋 ملخص سريع للخطوات:

### ✅ ما تم إنجازه:
1. **دخول Firebase Console** ✅
2. **اختيار مشروع jorinvforex** ✅  
3. **إنشاء Firestore Database** ✅
4. **تفعيل Authentication (Email/Password)** ✅
5. **تفعيل Storage** ✅
6. **إنشاء Web App والحصول على المفاتيح** ✅

### 🔄 الخطوات التالية:
1. **تحديث الإعدادات في الكود**
2. **اختبار المنصة**
3. **إنشاء مستخدم أدمن أول**

---

## 🎯 نصائح مهمة:

### ⚠️ احذر من:
- **لا تشارك مفاتيح API علناً**
- **احفظ الإعدادات في مكان آمن**
- **تأكد من اختيار نفس المنطقة لجميع الخدمات**

### ✅ تأكد من:
- **جميع الخدمات مفعلة (Authentication, Firestore, Storage)**
- **تم نسخ الإعدادات بشكل صحيح**
- **المشروع الصحيح محدد (jorinvforex)**

---

## 📞 تحتاج مساعدة؟

### 🔗 روابط مفيدة:
- [Firebase Console](https://console.firebase.google.com/u/0/project/jorinvforex)
- [مساعد إنشاء قاعدة البيانات](database-setup-helper.html)
- [صفحة الإعداد الشاملة](setup-project.html)

### 📧 للدعم:
- راجع ملف `CREATE_DATABASE_GUIDE.md`
- استخدم `database-setup-helper.html` للمساعدة التفاعلية
- تواصل مع فريق التطوير

---

**🎉 تهانينا! أكملت إنشاء قاعدة البيانات بنجاح!**

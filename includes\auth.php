<?php
/**
 * نظام المصادقة - منصة المحللين الماليين
 * jorinvforex Authentication System
 */

// منع الوصول المباشر
if (!defined('JORINVFOREX_APP')) {
    die('Access Denied');
}

require_once 'config/database.php';

/**
 * كلاس نظام المصادقة
 */
class Auth {
    
    private $db;
    private $sessionName = 'jorinvforex_session';
    
    public function __construct() {
        $this->db = getDB();
        
        // بدء الجلسة إذا لم تكن مبدوءة
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function login($email, $password, $rememberMe = false) {
        try {
            // التحقق من صحة البيانات
            if (empty($email) || empty($password)) {
                return ['success' => false, 'message' => 'يرجى إدخال البريد الإلكتروني وكلمة المرور'];
            }
            
            if (!$this->db->validateEmail($email)) {
                return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
            }
            
            // البحث عن المستخدم
            $query = "SELECT * FROM users WHERE email = ? AND is_active = 1";
            $user = $this->db->selectOne($query, [$email]);
            
            if (!$user) {
                return ['success' => false, 'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'];
            }
            
            // التحقق من كلمة المرور
            if (!$this->db->verifyPassword($password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'];
            }
            
            // إنشاء الجلسة
            $this->createSession($user, $rememberMe);
            
            // تحديث آخر تسجيل دخول
            $this->updateLastLogin($user['id']);
            
            // تسجيل النشاط
            logActivity($user['id'], 'login');
            
            return [
                'success' => true, 
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => $this->getUserData($user),
                'redirect' => $this->getRedirectUrl($user['role'])
            ];
            
        } catch (Exception $e) {
            error_log("Login Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في تسجيل الدخول'];
        }
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateRegistrationData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم وجود المستخدم
            $existingUser = $this->db->selectOne("SELECT id FROM users WHERE email = ?", [$data['email']]);
            if ($existingUser) {
                return ['success' => false, 'message' => 'البريد الإلكتروني مستخدم بالفعل'];
            }
            
            // تشفير كلمة المرور
            $passwordHash = $this->db->hashPassword($data['password']);
            
            // إدراج المستخدم الجديد
            $query = "INSERT INTO users (email, password_hash, name, role, phone, bio, school, theme_color) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $data['email'],
                $passwordHash,
                $data['name'],
                $data['role'] ?? 'subscriber',
                $data['phone'] ?? null,
                $data['bio'] ?? null,
                $data['school'] ?? null,
                $data['theme_color'] ?? '#66CCFF'
            ];
            
            $userId = $this->db->insert($query, $params);
            
            if ($userId) {
                // تسجيل النشاط
                logActivity($userId, 'register');
                
                // إنشاء إشعار ترحيب
                createNotification(
                    $userId, 
                    'welcome', 
                    'مرحباً بك في منصة المحللين الماليين', 
                    'نرحب بك في منصة jorinvforex للمحللين الماليين'
                );
                
                return [
                    'success' => true, 
                    'message' => 'تم إنشاء الحساب بنجاح',
                    'user_id' => $userId
                ];
            } else {
                return ['success' => false, 'message' => 'فشل في إنشاء الحساب'];
            }
            
        } catch (Exception $e) {
            error_log("Registration Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في إنشاء الحساب'];
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        try {
            $userId = $this->getCurrentUserId();
            
            // حذف رمز الجلسة من قاعدة البيانات
            if (isset($_SESSION[$this->sessionName]['token'])) {
                $this->db->delete("DELETE FROM user_sessions WHERE session_token = ?", 
                    [$_SESSION[$this->sessionName]['token']]);
            }
            
            // تسجيل النشاط
            if ($userId) {
                logActivity($userId, 'logout');
            }
            
            // مسح الجلسة
            unset($_SESSION[$this->sessionName]);
            
            // مسح الكوكيز
            if (isset($_COOKIE[$this->sessionName])) {
                setcookie($this->sessionName, '', time() - 3600, '/');
            }
            
            return ['success' => true, 'message' => 'تم تسجيل الخروج بنجاح'];
            
        } catch (Exception $e) {
            error_log("Logout Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في تسجيل الخروج'];
        }
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        // التحقق من الجلسة
        if (isset($_SESSION[$this->sessionName])) {
            return $this->validateSession($_SESSION[$this->sessionName]);
        }
        
        // التحقق من الكوكيز
        if (isset($_COOKIE[$this->sessionName])) {
            return $this->validateCookie($_COOKIE[$this->sessionName]);
        }
        
        return false;
    }
    
    /**
     * الحصول على المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        $userId = $_SESSION[$this->sessionName]['user_id'] ?? null;
        if (!$userId) {
            return null;
        }
        
        $query = "SELECT * FROM users WHERE id = ? AND is_active = 1";
        $user = $this->db->selectOne($query, [$userId]);
        
        return $user ? $this->getUserData($user) : null;
    }
    
    /**
     * الحصول على معرف المستخدم الحالي
     */
    public function getCurrentUserId() {
        $user = $this->getCurrentUser();
        return $user ? $user['id'] : null;
    }
    
    /**
     * التحقق من دور المستخدم
     */
    public function hasRole($role) {
        $user = $this->getCurrentUser();
        return $user && $user['role'] === $role;
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public function canAccess($requiredRole) {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }
        
        $roleHierarchy = ['subscriber' => 1, 'analyst' => 2, 'admin' => 3];
        $userLevel = $roleHierarchy[$user['role']] ?? 0;
        $requiredLevel = $roleHierarchy[$requiredRole] ?? 0;
        
        return $userLevel >= $requiredLevel;
    }
    
    /**
     * حماية الصفحة
     */
    public function requireLogin($redirectUrl = 'login.php') {
        if (!$this->isLoggedIn()) {
            header("Location: $redirectUrl");
            exit;
        }
    }
    
    /**
     * حماية الصفحة بدور معين
     */
    public function requireRole($role, $redirectUrl = 'index.php') {
        $this->requireLogin();
        
        if (!$this->hasRole($role)) {
            header("Location: $redirectUrl");
            exit;
        }
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // الحصول على المستخدم
            $user = $this->db->selectOne("SELECT password_hash FROM users WHERE id = ?", [$userId]);
            if (!$user) {
                return ['success' => false, 'message' => 'المستخدم غير موجود'];
            }
            
            // التحقق من كلمة المرور الحالية
            if (!$this->db->verifyPassword($currentPassword, $user['password_hash'])) {
                return ['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'];
            }
            
            // التحقق من قوة كلمة المرور الجديدة
            if (!$this->db->validatePassword($newPassword)) {
                return ['success' => false, 'message' => 'كلمة المرور الجديدة ضعيفة'];
            }
            
            // تحديث كلمة المرور
            $newHash = $this->db->hashPassword($newPassword);
            $updated = $this->db->update("UPDATE users SET password_hash = ? WHERE id = ?", 
                [$newHash, $userId]);
            
            if ($updated) {
                logActivity($userId, 'change_password');
                return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في تغيير كلمة المرور'];
            }
            
        } catch (Exception $e) {
            error_log("Change Password Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في تغيير كلمة المرور'];
        }
    }
    
    /**
     * إنشاء جلسة جديدة
     */
    private function createSession($user, $rememberMe = false) {
        $sessionToken = $this->db->generateSessionToken();
        $expiresAt = $rememberMe ? 
            date('Y-m-d H:i:s', strtotime('+30 days')) : 
            date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        // حفظ الجلسة في قاعدة البيانات
        $query = "INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, expires_at) 
                  VALUES (?, ?, ?, ?, ?)";
        
        $params = [
            $user['id'],
            $sessionToken,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            $expiresAt
        ];
        
        $this->db->insert($query, $params);
        
        // إنشاء جلسة PHP
        $_SESSION[$this->sessionName] = [
            'user_id' => $user['id'],
            'token' => $sessionToken,
            'expires_at' => $expiresAt
        ];
        
        // إنشاء كوكيز إذا كان "تذكرني" مفعل
        if ($rememberMe) {
            setcookie($this->sessionName, $sessionToken, strtotime($expiresAt), '/');
        }
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    private function validateSession($sessionData) {
        if (!isset($sessionData['token']) || !isset($sessionData['expires_at'])) {
            return false;
        }
        
        // التحقق من انتهاء الصلاحية
        if (strtotime($sessionData['expires_at']) < time()) {
            return false;
        }
        
        // التحقق من وجود الجلسة في قاعدة البيانات
        $query = "SELECT user_id FROM user_sessions WHERE session_token = ? AND expires_at > NOW()";
        $session = $this->db->selectOne($query, [$sessionData['token']]);
        
        return $session !== false;
    }
    
    /**
     * التحقق من صحة الكوكيز
     */
    private function validateCookie($token) {
        $query = "SELECT user_id, expires_at FROM user_sessions WHERE session_token = ? AND expires_at > NOW()";
        $session = $this->db->selectOne($query, [$token]);
        
        if ($session) {
            // إعادة إنشاء الجلسة
            $_SESSION[$this->sessionName] = [
                'user_id' => $session['user_id'],
                'token' => $token,
                'expires_at' => $session['expires_at']
            ];
            return true;
        }
        
        return false;
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($userId) {
        $this->db->update("UPDATE users SET last_login = NOW() WHERE id = ?", [$userId]);
    }
    
    /**
     * الحصول على بيانات المستخدم للعرض
     */
    private function getUserData($user) {
        return [
            'id' => $user['id'],
            'uuid' => $user['uuid'],
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'],
            'phone' => $user['phone'],
            'profile_pic_url' => $user['profile_pic_url'],
            'cover_pic_url' => $user['cover_pic_url'],
            'theme_color' => $user['theme_color'],
            'bio' => $user['bio'],
            'school' => $user['school'],
            'experience_years' => $user['experience_years'],
            'specialization' => $user['specialization'],
            'is_verified' => $user['is_verified'],
            'created_at' => $user['created_at']
        ];
    }
    
    /**
     * الحصول على رابط التوجيه حسب الدور
     */
    private function getRedirectUrl($role) {
        switch ($role) {
            case 'admin':
                return 'admin/dashboard.php';
            case 'analyst':
                return 'dashboard-analyst.php';
            case 'subscriber':
                return 'dashboard-subscriber.php';
            default:
                return 'index.php';
        }
    }
    
    /**
     * التحقق من صحة بيانات التسجيل
     */
    private function validateRegistrationData($data) {
        if (empty($data['email']) || empty($data['password']) || empty($data['name'])) {
            return ['valid' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة'];
        }
        
        if (!$this->db->validateEmail($data['email'])) {
            return ['valid' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
        }
        
        if (!$this->db->validatePassword($data['password'])) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل وتحتوي على حروف كبيرة وصغيرة وأرقام'];
        }
        
        if (strlen($data['name']) < 2) {
            return ['valid' => false, 'message' => 'الاسم يجب أن يكون حرفين على الأقل'];
        }
        
        $allowedRoles = ['analyst', 'subscriber'];
        if (isset($data['role']) && !in_array($data['role'], $allowedRoles)) {
            return ['valid' => false, 'message' => 'نوع الحساب غير صحيح'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public function cleanupExpiredSessions() {
        return $this->db->cleanExpiredSessions();
    }
}

// إنشاء متغير عام للمصادقة
$GLOBALS['auth'] = new Auth();

/**
 * دالة مساعدة للحصول على نظام المصادقة
 */
function getAuth() {
    return $GLOBALS['auth'];
}

/**
 * دالة مساعدة للتحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return getAuth()->isLoggedIn();
}

/**
 * دالة مساعدة للحصول على المستخدم الحالي
 */
function getCurrentUser() {
    return getAuth()->getCurrentUser();
}

/**
 * دالة مساعدة للتحقق من الدور
 */
function hasRole($role) {
    return getAuth()->hasRole($role);
}

/**
 * دالة مساعدة لحماية الصفحة
 */
function requireLogin($redirectUrl = 'login.php') {
    return getAuth()->requireLogin($redirectUrl);
}

/**
 * دالة مساعدة لحماية الصفحة بدور معين
 */
function requireRole($role, $redirectUrl = 'index.php') {
    return getAuth()->requireRole($role, $redirectUrl);
}

?>

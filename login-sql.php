<?php
/**
 * صفحة تسجيل الدخول - منصة المحللين الماليين
 * jorinvforex Login Page with SQL Database
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// تضمين الملفات المطلوبة
require_once 'config/database.php';
require_once 'includes/auth.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    $user = getCurrentUser();
    $redirectUrl = getAuth()->getRedirectUrl($user['role']);
    header("Location: $redirectUrl");
    exit;
}

$error = '';
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    
    $result = getAuth()->login($email, $password, $rememberMe);
    
    if ($result['success']) {
        $success = $result['message'];
        // إعادة توجيه بعد ثانيتين
        header("refresh:2;url=" . $result['redirect']);
    } else {
        $error = $result['message'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول | منصة المحللين الماليين</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
            margin: 0 1rem;
        }

        .login-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .login-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            opacity: 0.9;
            margin: 0;
        }

        .login-content {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: var(--text-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 0.2rem rgba(102, 204, 255, 0.25);
            background: var(--primary-white);
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            background: transparent;
            border: none;
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            color: var(--text-light);
        }

        .form-control.with-icon {
            padding-right: 45px;
        }

        .btn-login {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .form-check {
            margin: 1rem 0;
        }

        .form-check-input:checked {
            background-color: var(--accent-blue);
            border-color: var(--accent-blue);
        }

        .form-check-label {
            color: var(--text-dark);
            font-weight: 500;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }

        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            border-top: 1px solid #e9ecef;
        }

        .login-footer a {
            color: var(--accent-blue);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .login-footer a:hover {
            color: var(--primary-blue);
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            z-index: 10;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary-white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-accounts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .demo-accounts h6 {
            color: var(--primary-blue);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .demo-account {
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: var(--primary-white);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-account:hover {
            background: var(--accent-blue);
            color: var(--primary-white);
        }

        @media (max-width: 576px) {
            .login-container {
                margin: 0 0.5rem;
            }
            
            .login-header {
                padding: 1.5rem;
            }
            
            .login-content {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-chart-line"></i> منصة المحللين الماليين</h1>
            <p>تسجيل الدخول إلى حسابك</p>
        </div>

        <div class="login-content">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <div class="mt-2">
                        <i class="fas fa-spinner fa-spin"></i> جاري التوجيه...
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" id="loginForm">
                <div class="form-group">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <div class="input-group">
                        <input type="email" 
                               class="form-control with-icon" 
                               id="email" 
                               name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               required 
                               autocomplete="email">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <input type="password" 
                               class="form-control with-icon" 
                               id="password" 
                               name="password" 
                               required 
                               autocomplete="current-password">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                    <label class="form-check-label" for="remember_me">
                        تذكرني لمدة 30 يوماً
                    </label>
                </div>

                <button type="submit" class="btn btn-login" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                    <span class="loading-spinner" id="loadingSpinner"></span>
                </button>
            </form>

            <!-- حسابات تجريبية -->
            <div class="demo-accounts">
                <h6><i class="fas fa-users"></i> حسابات تجريبية:</h6>
                
                <div class="demo-account" onclick="fillDemoAccount('<EMAIL>', 'Admin123456')">
                    <strong>👑 مدير النظام:</strong> <EMAIL>
                </div>
                
                <div class="demo-account" onclick="fillDemoAccount('<EMAIL>', 'Analyst123456')">
                    <strong>📊 محلل مالي:</strong> <EMAIL>
                </div>
                
                <div class="demo-account" onclick="fillDemoAccount('<EMAIL>', 'Subscriber123456')">
                    <strong>👤 مشترك:</strong> <EMAIL>
                </div>
            </div>
        </div>

        <div class="login-footer">
            <p>ليس لديك حساب؟ <a href="register-sql.php">إنشاء حساب جديد</a></p>
            <p><a href="index-sql.php">العودة للصفحة الرئيسية</a></p>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }
        
        // ملء بيانات الحساب التجريبي
        function fillDemoAccount(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            
            // تأثير بصري
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            
            emailInput.style.background = '#e8f5e8';
            passwordInput.style.background = '#e8f5e8';
            
            setTimeout(() => {
                emailInput.style.background = '';
                passwordInput.style.background = '';
            }, 1000);
        }
        
        // معالجة إرسال النموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const loginBtn = document.getElementById('loginBtn');
            const loadingSpinner = document.getElementById('loadingSpinner');
            
            // إظهار مؤشر التحميل
            loginBtn.disabled = true;
            loadingSpinner.style.display = 'inline-block';
            
            // إعادة تفعيل الزر بعد 5 ثوان (في حالة عدم إعادة التوجيه)
            setTimeout(() => {
                loginBtn.disabled = false;
                loadingSpinner.style.display = 'none';
            }, 5000);
        });
        
        // التركيز على حقل البريد الإلكتروني عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });
        
        // إضافة تأثيرات بصرية للحقول
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>

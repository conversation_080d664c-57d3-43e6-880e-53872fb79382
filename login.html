<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول | منصة المحللين الماليين</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --border-light: #e9ecef;
            --shadow-light: rgba(0, 51, 102, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23003366" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .login-container {
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px var(--shadow-light);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            position: relative;
            z-index: 2;
            border: 1px solid var(--border-light);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 1rem;
        }

        .login-title {
            color: var(--primary-blue);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: var(--text-light);
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: var(--text-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 2px solid var(--border-light);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--primary-white);
        }

        .form-control:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 0.2rem rgba(102, 204, 255, 0.25);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            z-index: 3;
        }

        .form-control.with-icon {
            padding-right: 45px;
        }

        .btn-login {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 51, 102, 0.3);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-light);
        }

        .divider span {
            background: var(--primary-white);
            padding: 0 1rem;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .register-link {
            text-align: center;
            margin-top: 1.5rem;
        }

        .register-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 600;
        }

        .register-link a:hover {
            color: var(--accent-blue);
            text-decoration: underline;
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 1.1rem;
            z-index: 3;
        }

        .back-link:hover {
            color: var(--accent-blue);
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary-white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 2rem;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
            
            .back-link {
                position: relative;
                top: auto;
                left: auto;
                display: block;
                text-align: center;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <a href="index-new.html" class="back-link">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </a>

    <div class="login-container">
        <div class="login-header">
            <img src="assets/img/logo.png" alt="فوركس الأردن" class="login-logo">
            <h1 class="login-title">تسجيل الدخول</h1>
            <p class="login-subtitle">مرحباً بك في منصة المحللين الماليين</p>
        </div>

        <div id="errorMessage" class="error-message" style="display: none;"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <div class="input-group">
                    <input type="email" class="form-control with-icon" id="email" required>
                    <span class="input-group-text">
                        <i class="fas fa-envelope"></i>
                    </span>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <input type="password" class="form-control with-icon" id="password" required>
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                </div>
            </div>

            <button type="submit" class="btn btn-login" id="loginBtn">
                <span id="loginBtnText">تسجيل الدخول</span>
                <span id="loginSpinner" class="loading-spinner" style="display: none;"></span>
            </button>
        </form>

        <div class="divider">
            <span>أو</span>
        </div>

        <div class="register-link">
            <p>ليس لديك حساب؟ <a href="register.html">إنشاء حساب جديد</a></p>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase Configuration -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "jorinvforex.firebaseapp.com",
            projectId: "jorinvforex",
            storageBucket: "jorinvforex.appspot.com",
            messagingSenderId: "your-sender-id",
            appId: "your-app-id"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();
    </script>
    
    <!-- Login Script -->
    <script src="js/login.js"></script>
</body>
</html>

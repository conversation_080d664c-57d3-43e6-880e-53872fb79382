# كيفية الحصول على Web API Key لمشروع jorinvforex

## الخطوات السريعة:

### 1. الذهاب إلى Firebase Console
اذهب إلى: [Firebase Console - jorinvforex](https://console.firebase.google.com/u/0/project/jorinvforex/settings/general)

### 2. إضافة تطبيق ويب جديد
1. في صفحة Project Settings
2. انتقل إلى تبويب **"General"**
3. في قسم **"Your apps"** في الأسفل
4. اضغط على أيقونة **"</>"** (Web app)
5. أدخل اسم التطبيق: **"منصة المحللين الماليين"**
6. ✅ فعّل **"Also set up Firebase Hosting"** (اختياري)
7. اضغط **"Register app"**

### 3. نسخ الإعدادات
ستظهر لك إعدادات مثل هذه:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "jorinvforex.firebaseapp.com",
  projectId: "jorinvforex",
  storageBucket: "jorinvforex.appspot.com",
  messagingSenderId: "862703765675",
  appId: "1:862703765675:web:XXXXXXXXXXXXXXXX"
};
```

### 4. تحديث الملفات
استبدل الإعدادات في هذه الملفات:

#### ملف index-new.html
```javascript
// ابحث عن هذا القسم واستبدله
const firebaseConfig = {
    apiKey: "YOUR_ACTUAL_API_KEY_HERE",
    authDomain: "jorinvforex.firebaseapp.com",
    projectId: "jorinvforex",
    storageBucket: "jorinvforex.appspot.com",
    messagingSenderId: "862703765675",
    appId: "YOUR_ACTUAL_APP_ID_HERE"
};
```

#### الملفات الأخرى التي تحتاج نفس التحديث:
- `login.html`
- `register.html` 
- `dashboard-analyst.html`
- `dashboard-subscriber.html`

### 5. تفعيل الخدمات المطلوبة

#### Authentication
1. اذهب إلى [Authentication](https://console.firebase.google.com/u/0/project/jorinvforex/authentication/providers)
2. اضغط **"Get started"**
3. في تبويب **"Sign-in method"**
4. فعّل **"Email/Password"**

#### Firestore Database
1. اذهب إلى [Firestore](https://console.firebase.google.com/u/0/project/jorinvforex/firestore)
2. اضغط **"Create database"**
3. اختر **"Start in production mode"**
4. اختر المنطقة: **us-central1** (أو الأقرب لك)

#### Storage
1. اذهب إلى [Storage](https://console.firebase.google.com/u/0/project/jorinvforex/storage)
2. اضغط **"Get started"**
3. اختر **"Start in production mode"**
4. اختر نفس المنطقة

### 6. نشر قواعد الأمان (اختياري - للمطورين)

إذا كان لديك Firebase CLI:

```bash
# تسجيل الدخول
firebase login

# ربط المشروع
firebase use jorinvforex

# نشر القواعد
firebase deploy --only firestore:rules,storage
```

### 7. اختبار الإعداد

1. افتح `setup-project.html` في المتصفح
2. اتبع الخطوات لإنشاء مستخدم أدمن
3. أنشئ بيانات تجريبية
4. اذهب إلى `index-new.html` لاختبار المنصة

---

## مشاكل شائعة وحلولها:

### ❌ "Firebase configuration not found"
**الحل:** تأكد من تحديث `apiKey` و `appId` في جميع ملفات HTML

### ❌ "Missing or insufficient permissions"
**الحل:** تأكد من تفعيل Firestore وAuthentication في Firebase Console

### ❌ "Storage bucket not found"
**الحل:** تأكد من تفعيل Storage في Firebase Console

---

## روابط مفيدة:

- [Firebase Console - jorinvforex](https://console.firebase.google.com/u/0/project/jorinvforex)
- [Project Settings](https://console.firebase.google.com/u/0/project/jorinvforex/settings/general)
- [Authentication](https://console.firebase.google.com/u/0/project/jorinvforex/authentication)
- [Firestore](https://console.firebase.google.com/u/0/project/jorinvforex/firestore)
- [Storage](https://console.firebase.google.com/u/0/project/jorinvforex/storage)

---

**ملاحظة:** احفظ إعدادات Firebase في مكان آمن ولا تشاركها علناً!

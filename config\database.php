<?php
/**
 * إعدادات قاعدة البيانات - منصة المحللين الماليين
 * jorinvforex Database Configuration
 */

// منع الوصول المباشر
if (!defined('JORINVFOREX_APP')) {
    die('Access Denied');
}

// إعدادات قاعدة البيانات
class DatabaseConfig {
    
    // إعدادات الاتصال
    const DB_HOST = 'localhost';
    const DB_NAME = 'jorinvforex';
    const DB_USER = 'root';
    const DB_PASS = '';
    const DB_CHARSET = 'utf8mb4';
    const DB_PORT = 3306;
    
    // إعدادات PDO
    const PDO_OPTIONS = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    // متغير الاتصال
    private static $connection = null;
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public static function getConnection() {
        if (self::$connection === null) {
            try {
                $dsn = sprintf(
                    "mysql:host=%s;port=%d;dbname=%s;charset=%s",
                    self::DB_HOST,
                    self::DB_PORT,
                    self::DB_NAME,
                    self::DB_CHARSET
                );
                
                self::$connection = new PDO($dsn, self::DB_USER, self::DB_PASS, self::PDO_OPTIONS);
                
                // تعيين المنطقة الزمنية
                self::$connection->exec("SET time_zone = '+03:00'");
                
            } catch (PDOException $e) {
                error_log("Database Connection Error: " . $e->getMessage());
                throw new Exception("فشل الاتصال بقاعدة البيانات");
            }
        }
        
        return self::$connection;
    }
    
    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public static function testConnection() {
        try {
            $pdo = self::getConnection();
            $stmt = $pdo->query("SELECT 1");
            return $stmt !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * إغلاق الاتصال
     */
    public static function closeConnection() {
        self::$connection = null;
    }
}

/**
 * كلاس قاعدة البيانات الرئيسي
 */
class Database {
    
    private $pdo;
    
    public function __construct() {
        $this->pdo = DatabaseConfig::getConnection();
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Database Select Error: " . $e->getMessage());
            throw new Exception("خطأ في استعلام البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام SELECT لسجل واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Database SelectOne Error: " . $e->getMessage());
            throw new Exception("خطأ في استعلام البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $result = $stmt->execute($params);
            return $result ? $this->pdo->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Database Insert Error: " . $e->getMessage());
            throw new Exception("خطأ في إدراج البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Database Update Error: " . $e->getMessage());
            throw new Exception("خطأ في تحديث البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Database Delete Error: " . $e->getMessage());
            throw new Exception("خطأ في حذف البيانات");
        }
    }
    
    /**
     * تنفيذ استعلام عام
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Database Execute Error: " . $e->getMessage());
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    /**
     * الحصول على آخر معرف مدرج
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * تنظيف البيانات
     */
    public function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * تشفير كلمة المرور
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * إنشاء UUID
     */
    public function generateUUID() {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    /**
     * إنشاء رمز جلسة آمن
     */
    public function generateSessionToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * تسجيل النشاط
     */
    public function logActivity($userId, $action, $tableName = null, $recordId = null, $oldData = null, $newData = null) {
        $query = "INSERT INTO activity_logs (user_id, action, table_name, record_id, old_data, new_data, ip_address, user_agent) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $userId,
            $action,
            $tableName,
            $recordId,
            $oldData ? json_encode($oldData) : null,
            $newData ? json_encode($newData) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        return $this->execute($query, $params);
    }
    
    /**
     * إنشاء إشعار
     */
    public function createNotification($userId, $type, $title, $message, $data = null) {
        $query = "INSERT INTO notifications (user_id, type, title, message, data) VALUES (?, ?, ?, ?, ?)";
        $params = [$userId, $type, $title, $message, $data ? json_encode($data) : null];
        return $this->insert($query, $params);
    }
    
    /**
     * تحديث الإحصائيات اليومية
     */
    public function updateDailyStats() {
        try {
            return $this->execute("CALL UpdateDailyStats()");
        } catch (Exception $e) {
            // في حالة عدم وجود الإجراء، نقوم بالتحديث يدوياً
            $query = "INSERT INTO daily_stats (
                date, total_users, new_users, total_analysts, total_subscribers,
                total_posts, new_posts, total_subscriptions, new_subscriptions
            ) VALUES (
                CURDATE(),
                (SELECT COUNT(*) FROM users WHERE is_active = 1),
                (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()),
                (SELECT COUNT(*) FROM users WHERE role = 'analyst' AND is_active = 1),
                (SELECT COUNT(*) FROM users WHERE role = 'subscriber' AND is_active = 1),
                (SELECT COUNT(*) FROM posts WHERE status = 'published'),
                (SELECT COUNT(*) FROM posts WHERE DATE(created_at) = CURDATE()),
                (SELECT COUNT(*) FROM subscriptions WHERE status = 'accepted'),
                (SELECT COUNT(*) FROM subscriptions WHERE DATE(created_at) = CURDATE())
            ) ON DUPLICATE KEY UPDATE
                total_users = VALUES(total_users),
                total_analysts = VALUES(total_analysts),
                total_subscribers = VALUES(total_subscribers),
                total_posts = VALUES(total_posts),
                total_subscriptions = VALUES(total_subscriptions)";

            return $this->execute($query);
        }
    }
    
    /**
     * البحث في النصوص العربية
     */
    public function searchArabic($query, $searchTerm) {
        // إضافة دعم البحث العربي
        $searchTerm = '%' . $searchTerm . '%';
        return str_replace(':search', $searchTerm, $query);
    }
    
    /**
     * تنسيق التاريخ للعرض
     */
    public function formatDate($date, $format = 'Y-m-d H:i:s') {
        if (!$date) return null;
        return date($format, strtotime($date));
    }
    
    /**
     * تنسيق التاريخ بالعربية
     */
    public function formatDateArabic($date) {
        if (!$date) return '';
        
        $timestamp = strtotime($date);
        $now = time();
        $diff = $now - $timestamp;
        
        if ($diff < 60) {
            return 'الآن';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . ' دقيقة';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' ساعة';
        } elseif ($diff < 2592000) {
            $days = floor($diff / 86400);
            return $days . ' يوم';
        } else {
            return date('Y/m/d', $timestamp);
        }
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public function validatePassword($password) {
        // كلمة المرور يجب أن تكون 8 أحرف على الأقل
        if (strlen($password) < 8) {
            return false;
        }
        
        // يجب أن تحتوي على حرف كبير وصغير ورقم
        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $password)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public function cleanExpiredSessions() {
        $query = "DELETE FROM user_sessions WHERE expires_at < NOW()";
        return $this->execute($query);
    }
    
    /**
     * إحصائيات سريعة
     */
    public function getQuickStats() {
        $stats = [];
        
        // عدد المستخدمين
        $result = $this->selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
        $stats['total_users'] = $result['count'];
        
        // عدد المحللين
        $result = $this->selectOne("SELECT COUNT(*) as count FROM users WHERE role = 'analyst' AND is_active = 1");
        $stats['total_analysts'] = $result['count'];
        
        // عدد المشتركين
        $result = $this->selectOne("SELECT COUNT(*) as count FROM users WHERE role = 'subscriber' AND is_active = 1");
        $stats['total_subscribers'] = $result['count'];
        
        // عدد المنشورات
        $result = $this->selectOne("SELECT COUNT(*) as count FROM posts WHERE status = 'published'");
        $stats['total_posts'] = $result['count'];
        
        // عدد الاشتراكات النشطة
        $result = $this->selectOne("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'accepted'");
        $stats['active_subscriptions'] = $result['count'];
        
        return $stats;
    }
}

// إنشاء متغير عام لقاعدة البيانات
$GLOBALS['db'] = new Database();

/**
 * دالة مساعدة للحصول على قاعدة البيانات
 */
function getDB() {
    return $GLOBALS['db'];
}

/**
 * دالة مساعدة لتنظيف البيانات
 */
function sanitize($data) {
    return getDB()->sanitize($data);
}

/**
 * دالة مساعدة لتشفير كلمة المرور
 */
function hashPassword($password) {
    return getDB()->hashPassword($password);
}

/**
 * دالة مساعدة للتحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return getDB()->verifyPassword($password, $hash);
}

/**
 * دالة مساعدة لتسجيل النشاط
 */
function logActivity($userId, $action, $tableName = null, $recordId = null, $oldData = null, $newData = null) {
    return getDB()->logActivity($userId, $action, $tableName, $recordId, $oldData, $newData);
}

/**
 * دالة مساعدة لإنشاء إشعار
 */
function createNotification($userId, $type, $title, $message, $data = null) {
    return getDB()->createNotification($userId, $type, $title, $message, $data);
}

/**
 * دالة مساعدة لتنسيق التاريخ بالعربية
 */
function formatDateArabic($date) {
    return getDB()->formatDateArabic($date);
}

?>

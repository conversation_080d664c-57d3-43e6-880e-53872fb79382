-- ===== قاعدة بيانات منصة المحللين الماليين - jorinvforex =====
-- MySQL Database Schema
-- Created for jorinvforex financial analysts platform

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS jorinvforex 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE jorinvforex;

-- ===== جدول المستخدمين =====
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role ENUM('admin', 'analyst', 'subscriber') NOT NULL DEFAULT 'subscriber',
    phone VARCHAR(20),
    profile_pic_url VARCHAR(500),
    cover_pic_url VARCHAR(500),
    theme_color VARCHAR(7) DEFAULT '#66CCFF',
    bio TEXT,
    school VARCHAR(255),
    experience_years INT DEFAULT 0,
    specialization VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_uuid (uuid),
    INDEX idx_active (is_active)
);

-- ===== جدول المنشورات =====
CREATE TABLE posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    analyst_id INT NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    content LONGTEXT,
    image_url VARCHAR(500),
    tradingview_link VARCHAR(500),
    is_paid_content BOOLEAN DEFAULT FALSE,
    is_free_now BOOLEAN DEFAULT FALSE,
    price DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    free_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_analyst (analyst_id),
    INDEX idx_status (status),
    INDEX idx_paid (is_paid_content),
    INDEX idx_published (published_at),
    INDEX idx_uuid (uuid)
);

-- ===== جدول الاشتراكات =====
CREATE TABLE subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    analyst_id INT NOT NULL,
    subscriber_id INT NOT NULL,
    status ENUM('pending', 'accepted', 'rejected', 'cancelled') DEFAULT 'pending',
    payment_confirmed BOOLEAN DEFAULT FALSE,
    payment_method VARCHAR(50),
    payment_reference VARCHAR(255),
    amount DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    subscription_type ENUM('monthly', 'quarterly', 'yearly') DEFAULT 'monthly',
    expires_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscriber_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_subscription (analyst_id, subscriber_id),
    INDEX idx_analyst (analyst_id),
    INDEX idx_subscriber (subscriber_id),
    INDEX idx_status (status),
    INDEX idx_uuid (uuid)
);

-- ===== جدول المحادثات =====
CREATE TABLE chats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    analyst_id INT NOT NULL,
    subscriber_id INT NOT NULL,
    last_message_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscriber_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_chat (analyst_id, subscriber_id),
    INDEX idx_analyst (analyst_id),
    INDEX idx_subscriber (subscriber_id),
    INDEX idx_uuid (uuid)
);

-- ===== جدول الرسائل =====
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    chat_id INT NOT NULL,
    sender_id INT NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file', 'link') DEFAULT 'text',
    file_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_chat (chat_id),
    INDEX idx_sender (sender_id),
    INDEX idx_created (created_at),
    INDEX idx_uuid (uuid)
);

-- ===== جدول الإشعارات =====
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSON,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_type (type),
    INDEX idx_read (is_read),
    INDEX idx_created (created_at),
    INDEX idx_uuid (uuid)
);

-- ===== جدول التقييمات =====
CREATE TABLE ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    analyst_id INT NOT NULL,
    subscriber_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscriber_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_rating (analyst_id, subscriber_id),
    INDEX idx_analyst (analyst_id),
    INDEX idx_rating (rating),
    INDEX idx_uuid (uuid)
);

-- ===== جدول الإحصائيات اليومية =====
CREATE TABLE daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    total_users INT DEFAULT 0,
    new_users INT DEFAULT 0,
    total_analysts INT DEFAULT 0,
    total_subscribers INT DEFAULT 0,
    total_posts INT DEFAULT 0,
    new_posts INT DEFAULT 0,
    total_subscriptions INT DEFAULT 0,
    new_subscriptions INT DEFAULT 0,
    total_messages INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_date (date),
    INDEX idx_date (date)
);

-- ===== جدول الجلسات =====
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_token (session_token),
    INDEX idx_expires (expires_at)
);

-- ===== جدول سجل النشاط =====
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_data JSON,
    new_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_table (table_name),
    INDEX idx_created (created_at)
);

-- ===== جدول الإعلانات =====
CREATE TABLE ads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    link_url VARCHAR(500),
    position ENUM('header', 'sidebar', 'footer', 'popup') DEFAULT 'sidebar',
    is_active BOOLEAN DEFAULT TRUE,
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    clicks_count INT DEFAULT 0,
    views_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_position (position),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_uuid (uuid)
);

-- ===== إدراج بيانات تجريبية =====

-- إدراج مستخدم أدمن
INSERT INTO users (email, password_hash, name, role, bio, is_verified, is_active, email_verified) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 'مدير منصة المحللين الماليين', TRUE, TRUE, TRUE);

-- إدراج محلل تجريبي
INSERT INTO users (email, password_hash, name, role, bio, school, experience_years, specialization, is_verified, is_active, email_verified) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد', 'analyst', 'محلل مالي معتمد مع خبرة 5 سنوات في أسواق الفوركس والأسهم', 'جامعة الأردن - كلية الاقتصاد', 5, 'تحليل الفوركس والأسهم', TRUE, TRUE, TRUE);

-- إدراج مشترك تجريبي
INSERT INTO users (email, password_hash, name, role, bio, is_active, email_verified) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'سارة أحمد', 'subscriber', 'مستثمرة مهتمة بأسواق المال والعملات', TRUE, TRUE);

-- إدراج منشور تجريبي
INSERT INTO posts (analyst_id, title, description, content, is_paid_content, status, published_at) VALUES
(2, 'تحليل زوج EUR/USD - فرصة شراء قوية', 'بناءً على التحليل الفني والأساسي، نتوقع ارتفاع زوج اليورو دولار خلال الأسبوع القادم...', 'تحليل مفصل للزوج مع نقاط الدخول والخروج والأهداف المتوقعة.', TRUE, 'published', NOW());

-- إدراج طلب اشتراك تجريبي
INSERT INTO subscriptions (analyst_id, subscriber_id, status) VALUES
(2, 3, 'pending');

-- إدراج إحصائيات يومية
INSERT INTO daily_stats (date, total_users, new_users, total_analysts, total_subscribers, total_posts, new_posts) VALUES
(CURDATE(), 3, 3, 1, 1, 1, 1);

-- ===== إنشاء Views مفيدة =====

-- عرض المحللين مع إحصائياتهم
CREATE VIEW analysts_stats AS
SELECT 
    u.id,
    u.uuid,
    u.name,
    u.email,
    u.bio,
    u.school,
    u.experience_years,
    u.specialization,
    u.profile_pic_url,
    u.theme_color,
    COUNT(DISTINCT s.id) as subscribers_count,
    COUNT(DISTINCT p.id) as posts_count,
    AVG(r.rating) as average_rating,
    COUNT(DISTINCT r.id) as ratings_count,
    u.created_at
FROM users u
LEFT JOIN subscriptions s ON u.id = s.analyst_id AND s.status = 'accepted'
LEFT JOIN posts p ON u.id = p.analyst_id AND p.status = 'published'
LEFT JOIN ratings r ON u.id = r.analyst_id
WHERE u.role = 'analyst' AND u.is_active = TRUE
GROUP BY u.id;

-- عرض المنشورات مع تفاصيل المحلل
CREATE VIEW posts_with_analyst AS
SELECT 
    p.*,
    u.name as analyst_name,
    u.profile_pic_url as analyst_pic,
    u.theme_color as analyst_theme
FROM posts p
JOIN users u ON p.analyst_id = u.id
WHERE p.status = 'published';

-- عرض الاشتراكات مع تفاصيل المستخدمين
CREATE VIEW subscriptions_details AS
SELECT 
    s.*,
    a.name as analyst_name,
    a.email as analyst_email,
    sub.name as subscriber_name,
    sub.email as subscriber_email
FROM subscriptions s
JOIN users a ON s.analyst_id = a.id
JOIN users sub ON s.subscriber_id = sub.id;

-- ===== إنشاء Stored Procedures (مبسطة) =====

-- إجراء لتحديث الإحصائيات اليومية
DELIMITER $$
CREATE PROCEDURE UpdateDailyStats()
BEGIN
    INSERT INTO daily_stats (
        date,
        total_users,
        new_users,
        total_analysts,
        total_subscribers,
        total_posts,
        new_posts,
        total_subscriptions,
        new_subscriptions
    ) VALUES (
        CURDATE(),
        (SELECT COUNT(*) FROM users WHERE is_active = TRUE),
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()),
        (SELECT COUNT(*) FROM users WHERE role = 'analyst' AND is_active = TRUE),
        (SELECT COUNT(*) FROM users WHERE role = 'subscriber' AND is_active = TRUE),
        (SELECT COUNT(*) FROM posts WHERE status = 'published'),
        (SELECT COUNT(*) FROM posts WHERE DATE(created_at) = CURDATE()),
        (SELECT COUNT(*) FROM subscriptions WHERE status = 'accepted'),
        (SELECT COUNT(*) FROM subscriptions WHERE DATE(created_at) = CURDATE())
    )
    ON DUPLICATE KEY UPDATE
        total_users = VALUES(total_users),
        total_analysts = VALUES(total_analysts),
        total_subscribers = VALUES(total_subscribers),
        total_posts = VALUES(total_posts),
        total_subscriptions = VALUES(total_subscriptions);
END$$
DELIMITER ;

-- ===== إنشاء Triggers (مبسطة) =====

-- Trigger لتحديث عداد المشاهدات
DELIMITER $$
CREATE TRIGGER update_post_views
AFTER INSERT ON activity_logs
FOR EACH ROW
BEGIN
    IF NEW.action = 'view_post' AND NEW.table_name = 'posts' THEN
        UPDATE posts SET views_count = views_count + 1 WHERE id = NEW.record_id;
    END IF;
END$$
DELIMITER ;

-- Trigger لتحديث آخر رسالة في المحادثة
DELIMITER $$
CREATE TRIGGER update_chat_last_message
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    UPDATE chats SET
        last_message_id = NEW.id,
        updated_at = NOW()
    WHERE id = NEW.chat_id;
END$$
DELIMITER ;

-- ===== إنشاء Indexes إضافية للأداء =====
CREATE INDEX idx_posts_analyst_published ON posts(analyst_id, status, published_at);
CREATE INDEX idx_messages_chat_created ON messages(chat_id, created_at);
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read, created_at);
CREATE INDEX idx_subscriptions_status_created ON subscriptions(status, created_at);

-- ===== تعليق نهائي =====
-- قاعدة البيانات جاهزة للاستخدام مع منصة المحللين الماليين
-- تحتوي على جميع الجداول والعلاقات والإجراءات المطلوبة
-- مُحسنة للأداء مع Indexes مناسبة
-- تدعم العربية بالكامل مع UTF8MB4

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حسابات تجريبية | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .container-custom {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .account-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .account-card:hover {
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
            transform: translateY(-2px);
        }

        .account-type {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .admin-badge {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }

        .analyst-badge {
            background: rgba(0, 51, 102, 0.1);
            color: var(--primary-blue);
        }

        .subscriber-badge {
            background: rgba(102, 204, 255, 0.1);
            color: var(--accent-blue);
        }

        .credentials {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
        }

        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .btn-create {
            background: linear-gradient(45deg, var(--success-color), #20c997);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 5px;
        }

        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }

        .copy-btn {
            background: var(--accent-blue);
            border: none;
            color: var(--primary-white);
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            margin-left: 10px;
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <div class="header">
            <h1><i class="fas fa-users"></i> إنشاء حسابات تجريبية</h1>
            <p>حسابات جاهزة للاختبار - مشروع jorinvforex</p>
        </div>

        <div class="content">
            <!-- الحسابات الجاهزة -->
            <h3><i class="fas fa-star"></i> حسابات تجريبية جاهزة</h3>
            
            <!-- حساب الأدمن -->
            <div class="account-card">
                <div class="account-type admin-badge">
                    <i class="fas fa-crown"></i> مدير النظام (Admin)
                </div>
                <h5>حساب المدير الرئيسي</h5>
                <p class="text-muted">صلاحيات كاملة لإدارة النظام والمستخدمين</p>
                
                <div class="credentials">
                    <div><strong>البريد الإلكتروني:</strong> <EMAIL></div>
                    <div><strong>كلمة المرور:</strong> Admin123456</div>
                    <div><strong>الاسم:</strong> مدير النظام</div>
                </div>
                
                <div class="d-flex flex-wrap">
                    <button class="btn btn-custom btn-create" onclick="createAccount('admin', '<EMAIL>', 'Admin123456', 'مدير النظام')">
                        <i class="fas fa-plus"></i> إنشاء الحساب
                    </button>
                    <button class="copy-btn" onclick="copyCredentials('<EMAIL>', 'Admin123456')">
                        <i class="fas fa-copy"></i> نسخ البيانات
                    </button>
                    <span class="status-indicator status-pending" id="status-admin">
                        <i class="fas fa-clock"></i> لم يتم الإنشاء
                    </span>
                </div>
            </div>

            <!-- حساب المحلل -->
            <div class="account-card">
                <div class="account-type analyst-badge">
                    <i class="fas fa-chart-line"></i> محلل مالي (Analyst)
                </div>
                <h5>أحمد محمد - محلل مالي معتمد</h5>
                <p class="text-muted">خبرة 5 سنوات في تحليل أسواق الفوركس والأسهم</p>
                
                <div class="credentials">
                    <div><strong>البريد الإلكتروني:</strong> <EMAIL></div>
                    <div><strong>كلمة المرور:</strong> Analyst123456</div>
                    <div><strong>الاسم:</strong> أحمد محمد</div>
                    <div><strong>المؤسسة:</strong> جامعة الأردن - كلية الاقتصاد</div>
                </div>
                
                <div class="d-flex flex-wrap">
                    <button class="btn btn-custom btn-create" onclick="createAccount('analyst', '<EMAIL>', 'Analyst123456', 'أحمد محمد')">
                        <i class="fas fa-plus"></i> إنشاء الحساب
                    </button>
                    <button class="copy-btn" onclick="copyCredentials('<EMAIL>', 'Analyst123456')">
                        <i class="fas fa-copy"></i> نسخ البيانات
                    </button>
                    <span class="status-indicator status-pending" id="status-analyst">
                        <i class="fas fa-clock"></i> لم يتم الإنشاء
                    </span>
                </div>
            </div>

            <!-- حساب المشترك -->
            <div class="account-card">
                <div class="account-type subscriber-badge">
                    <i class="fas fa-users"></i> مشترك (Subscriber)
                </div>
                <h5>سارة أحمد - مستثمرة</h5>
                <p class="text-muted">مهتمة بالاستثمار في أسواق المال والعملات</p>
                
                <div class="credentials">
                    <div><strong>البريد الإلكتروني:</strong> <EMAIL></div>
                    <div><strong>كلمة المرور:</strong> Subscriber123456</div>
                    <div><strong>الاسم:</strong> سارة أحمد</div>
                </div>
                
                <div class="d-flex flex-wrap">
                    <button class="btn btn-custom btn-create" onclick="createAccount('subscriber', '<EMAIL>', 'Subscriber123456', 'سارة أحمد')">
                        <i class="fas fa-plus"></i> إنشاء الحساب
                    </button>
                    <button class="copy-btn" onclick="copyCredentials('<EMAIL>', 'Subscriber123456')">
                        <i class="fas fa-copy"></i> نسخ البيانات
                    </button>
                    <span class="status-indicator status-pending" id="status-subscriber">
                        <i class="fas fa-clock"></i> لم يتم الإنشاء
                    </span>
                </div>
            </div>

            <!-- إنشاء جميع الحسابات -->
            <div class="text-center mt-4">
                <button class="btn btn-custom btn-create" style="font-size: 1.2rem; padding: 15px 30px;" onclick="createAllAccounts()">
                    <i class="fas fa-magic"></i> إنشاء جميع الحسابات التجريبية
                </button>
            </div>

            <!-- تعليمات الاختبار -->
            <div class="alert alert-info alert-custom mt-4">
                <h5><i class="fas fa-info-circle"></i> كيفية الاختبار:</h5>
                <ol>
                    <li><strong>أنشئ الحسابات:</strong> اضغط "إنشاء جميع الحسابات التجريبية"</li>
                    <li><strong>اختبر تسجيل الدخول:</strong> اذهب إلى <a href="login.html" target="_blank">صفحة تسجيل الدخول</a></li>
                    <li><strong>جرب الأدوار المختلفة:</strong> سجل دخول بكل حساب لاختبار الميزات</li>
                    <li><strong>اختبر الاشتراكات:</strong> أرسل طلب اشتراك من المشترك للمحلل</li>
                </ol>
            </div>

            <!-- روابط سريعة -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <a href="login.html" class="btn btn-custom w-100">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="register.html" class="btn btn-custom w-100">
                        <i class="fas fa-user-plus"></i> إنشاء حساب جديد
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="index-new.html" class="btn btn-custom w-100">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase Configuration -->
    <script>
        // Firebase configuration for jorinvforex
        const firebaseConfig = {
            apiKey: "AIzaSyBXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX", // سيتم تحديثه
            authDomain: "jorinvforex.firebaseapp.com",
            projectId: "jorinvforex",
            storageBucket: "jorinvforex.appspot.com",
            messagingSenderId: "862703765675",
            appId: "1:862703765675:web:XXXXXXXXXXXXXXXX" // سيتم تحديثه
        };

        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            window.auth = firebase.auth();
            window.db = firebase.firestore();
            console.log('✅ تم تهيئة Firebase بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة Firebase:', error);
        }
    </script>
    
    <script>
        // إنشاء حساب واحد
        async function createAccount(role, email, password, name) {
            const statusElement = document.getElementById(`status-${role}`);
            
            try {
                statusElement.className = 'status-indicator status-pending';
                statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
                
                // إنشاء المستخدم
                const userCredential = await auth.createUserWithEmailAndPassword(email, password);
                const user = userCredential.user;
                
                // تحديث الملف الشخصي
                await user.updateProfile({
                    displayName: name
                });
                
                // إنشاء وثيقة المستخدم في Firestore
                const userData = {
                    name: name,
                    email: email,
                    role: role,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    profilePicURL: '',
                    coverPicURL: '',
                    themeColor: '#66CCFF'
                };
                
                // إضافة حقول خاصة بالمحلل
                if (role === 'analyst') {
                    userData.school = 'جامعة الأردن - كلية الاقتصاد';
                    userData.description = 'محلل مالي معتمد مع خبرة 5 سنوات في أسواق الفوركس والأسهم';
                    userData.acceptedSubscribers = [];
                }
                
                await db.collection('users').doc(user.uid).set(userData);
                
                statusElement.className = 'status-indicator status-success';
                statusElement.innerHTML = '<i class="fas fa-check"></i> تم الإنشاء بنجاح';
                
                console.log(`✅ تم إنشاء حساب ${role}: ${email}`);
                
                // تسجيل الخروج للسماح بإنشاء حسابات أخرى
                await auth.signOut();
                
            } catch (error) {
                console.error(`❌ خطأ في إنشاء حساب ${role}:`, error);
                
                let errorMessage = 'حدث خطأ في الإنشاء';
                if (error.code === 'auth/email-already-in-use') {
                    errorMessage = 'الحساب موجود بالفعل';
                    statusElement.className = 'status-indicator status-success';
                    statusElement.innerHTML = '<i class="fas fa-check"></i> موجود بالفعل';
                    return;
                }
                
                statusElement.className = 'status-indicator status-pending';
                statusElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${errorMessage}`;
            }
        }
        
        // إنشاء جميع الحسابات
        async function createAllAccounts() {
            const accounts = [
                { role: 'admin', email: '<EMAIL>', password: 'Admin123456', name: 'مدير النظام' },
                { role: 'analyst', email: '<EMAIL>', password: 'Analyst123456', name: 'أحمد محمد' },
                { role: 'subscriber', email: '<EMAIL>', password: 'Subscriber123456', name: 'سارة أحمد' }
            ];
            
            for (const account of accounts) {
                await createAccount(account.role, account.email, account.password, account.name);
                // انتظار ثانية بين كل حساب
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            alert('🎉 تم إنشاء جميع الحسابات التجريبية بنجاح!\n\nيمكنك الآن اختبار تسجيل الدخول.');
        }
        
        // نسخ بيانات الحساب
        function copyCredentials(email, password) {
            const text = `البريد الإلكتروني: ${email}\nكلمة المرور: ${password}`;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('✅ تم نسخ بيانات الحساب!');
                });
            } else {
                // للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('✅ تم نسخ بيانات الحساب!');
            }
        }
        
        // التحقق من حالة Firebase عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 صفحة إنشاء الحسابات التجريبية جاهزة!');
            
            // التحقق من تهيئة Firebase
            setTimeout(() => {
                if (typeof firebase === 'undefined' || !firebase.apps.length) {
                    alert('⚠️ يرجى تحديث إعدادات Firebase أولاً في setup-project.html');
                }
            }, 2000);
        });
    </script>
</body>
</html>

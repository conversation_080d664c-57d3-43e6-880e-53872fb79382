# 👥 حسابات تجريبية جاهزة - مشروع jorinvforex

## 🎯 الهدف
حسابات تجريبية لاختبار جميع ميزات منصة المحللين الماليين

---

## 🔑 الحسابات الجاهزة

### 1. 👑 حساب المدير (Admin)
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: Admin123456
الاسم: مدير النظام
الدور: admin
الصلاحيات: إدارة كاملة للنظام
```

**الميزات المتاحة:**
- ✅ إدارة جميع المستخدمين
- ✅ مراقبة النشاط
- ✅ إدارة المحتوى
- ✅ الإحصائيات الشاملة
- ✅ إعدادات النظام

---

### 2. 📊 حساب المحلل المالي (Analyst)
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: Analyst123456
الاسم: أحمد محمد
الدور: analyst
المؤسسة: جامعة الأردن - كلية الاقتصاد
الوصف: محلل مالي معتمد مع خبرة 5 سنوات في أسواق الفوركس والأسهم
```

**الميزات المتاحة:**
- ✅ إنشاء وإدارة المنشورات
- ✅ إدارة المشتركين
- ✅ قبول/رفض طلبات الاشتراك
- ✅ المحادثات مع المشتركين
- ✅ إحصائيات المنشورات

---

### 3. 👤 حساب المشترك (Subscriber)
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: Subscriber123456
الاسم: سارة أحمد
الدور: subscriber
الوصف: مستثمرة مهتمة بأسواق المال والعملات
```

**الميزات المتاحة:**
- ✅ تصفح المحللين المتاحين
- ✅ إرسال طلبات اشتراك
- ✅ عرض التحليلات المالية
- ✅ المحادثة مع المحللين
- ✅ متابعة الاشتراكات

---

## 🚀 كيفية الاستخدام

### الطريقة الأولى: إنشاء تلقائي
1. **افتح `create-test-accounts.html`**
2. **اضغط "إنشاء جميع الحسابات التجريبية"**
3. **انتظر حتى يتم إنشاء جميع الحسابات**
4. **ابدأ الاختبار!**

### الطريقة الثانية: إنشاء يدوي
1. **اذهب إلى `register.html`**
2. **أنشئ حساب بالبيانات المذكورة أعلاه**
3. **كرر للحسابات الأخرى**

---

## 🧪 سيناريوهات الاختبار

### السيناريو 1: اختبار المدير
```
1. سجل دخول بحساب المدير
2. تصفح لوحة تحكم الأدمن
3. راجع قائمة المستخدمين
4. اطلع على الإحصائيات
```

### السيناريو 2: اختبار المحلل
```
1. سجل دخول بحساب المحلل
2. أنشئ منشور تحليلي جديد
3. راجع طلبات الاشتراك
4. اقبل طلب من مشترك
5. ابدأ محادثة مع مشترك
```

### السيناريو 3: اختبار المشترك
```
1. سجل دخول بحساب المشترك
2. تصفح المحللين المتاحين
3. أرسل طلب اشتراك لمحلل
4. اقرأ التحليلات المتاحة
5. ابدأ محادثة مع محلل
```

### السيناريو 4: اختبار التفاعل
```
1. أرسل طلب اشتراك من المشترك
2. اقبل الطلب من المحلل
3. أنشئ منشور مدفوع من المحلل
4. اقرأ المنشور من المشترك
5. انتظر 3 ساعات (أو غيّر الوقت) لتحويل المنشور لمجاني
```

---

## 📋 قائمة اختبار شاملة

### ✅ اختبار المصادقة
- [ ] تسجيل دخول بحساب المدير
- [ ] تسجيل دخول بحساب المحلل
- [ ] تسجيل دخول بحساب المشترك
- [ ] تسجيل الخروج من جميع الحسابات
- [ ] اختبار كلمات مرور خاطئة

### ✅ اختبار الأدوار
- [ ] المدير يصل لجميع الصفحات
- [ ] المحلل يصل للوحة تحكمه فقط
- [ ] المشترك يصل للوحة تحكمه فقط
- [ ] منع الوصول غير المصرح

### ✅ اختبار الميزات
- [ ] إنشاء منشور من المحلل
- [ ] إرسال طلب اشتراك من المشترك
- [ ] قبول طلب اشتراك من المحلل
- [ ] عرض المنشورات للمشتركين
- [ ] البحث عن المحللين

### ✅ اختبار التصميم
- [ ] التصميم يعمل على الكمبيوتر
- [ ] التصميم يعمل على الجوال
- [ ] الألوان متناسقة
- [ ] النصوص العربية واضحة
- [ ] الأيقونات مناسبة

---

## 🔧 بيانات إضافية للاختبار

### منشورات تجريبية:
```javascript
const samplePosts = [
    {
        title: "تحليل زوج EUR/USD - فرصة شراء قوية",
        description: "بناءً على التحليل الفني والأساسي، نتوقع ارتفاع زوج اليورو دولار...",
        isPaidContent: true
    },
    {
        title: "نظرة على الذهب هذا الأسبوع",
        description: "الذهب يواجه مقاومة قوية عند مستوى 2000 دولار...",
        isPaidContent: false
    }
];
```

### طلبات اشتراك تجريبية:
```javascript
const sampleSubscriptions = [
    {
        analystId: "analyst_user_id",
        subscriberId: "subscriber_user_id",
        status: "pending"
    }
];
```

---

## 🎯 نصائح للاختبار

### 🔍 ما يجب اختباره:
1. **الوظائف الأساسية:** تسجيل دخول، إنشاء حسابات، التنقل
2. **الأدوار والصلاحيات:** كل دور يصل لما يجب أن يصل إليه فقط
3. **التفاعل:** طلبات الاشتراك، المنشورات، المحادثات
4. **التصميم:** الألوان، التجاوب، النصوص العربية
5. **الأمان:** عدم الوصول لصفحات غير مصرح بها

### ⚠️ مشاكل محتملة:
- **"Firebase configuration not found"** → حدّث إعدادات Firebase
- **"Permission denied"** → تأكد من قواعد Firestore
- **"User not found"** → تأكد من إنشاء الحسابات أولاً
- **صفحة فارغة** → تحقق من Console للأخطاء

---

## 📞 للمساعدة

### 🔗 روابط مفيدة:
- [إنشاء حسابات تجريبية](create-test-accounts.html)
- [تسجيل الدخول](login.html)
- [إنشاء حساب جديد](register.html)
- [الصفحة الرئيسية](index-new.html)

### 📧 للدعم:
- راجع `CREATE_DATABASE_GUIDE.md` لإعداد قاعدة البيانات
- استخدم `setup-project.html` للإعداد الشامل
- تواصل مع فريق التطوير للمساعدة

---

## 🎉 ملاحظات مهمة

### ✅ تذكر:
- **هذه حسابات تجريبية** - لا تستخدمها في الإنتاج
- **غيّر كلمات المرور** في البيئة الحقيقية
- **احذف الحسابات التجريبية** بعد انتهاء الاختبار
- **استخدم بيانات حقيقية** في الإنتاج

### 🔒 الأمان:
- لا تشارك هذه البيانات علناً
- استخدمها للاختبار المحلي فقط
- احذفها من قاعدة البيانات بعد الانتهاء

---

**🎊 مع هذه الحسابات التجريبية، يمكنك اختبار جميع ميزات المنصة بسهولة!**

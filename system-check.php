<?php
/**
 * فحص النظام - منصة المحللين الماليين
 * jorinvforex System Check
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// فحص PHP
$phpVersion = phpversion();
$phpOk = version_compare($phpVersion, '7.4.0', '>=');

// فحص الإضافات المطلوبة
$extensions = [
    'pdo' => extension_loaded('pdo'),
    'pdo_mysql' => extension_loaded('pdo_mysql'),
    'mbstring' => extension_loaded('mbstring'),
    'json' => extension_loaded('json'),
    'session' => extension_loaded('session')
];

// فحص الملفات المطلوبة
$requiredFiles = [
    'config/database.php',
    'includes/auth.php',
    'database/database.sql',
    'login-sql.php',
    'register-sql.php',
    'setup-database.php'
];

$filesExist = [];
foreach ($requiredFiles as $file) {
    $filesExist[$file] = file_exists($file);
}

// فحص قاعدة البيانات
$dbStatus = 'غير متصل';
$dbError = '';
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        $connection = DatabaseConfig::getConnection();
        if ($connection) {
            $dbStatus = 'متصل';
            
            // فحص الجداول
            $stmt = $connection->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $requiredTables = ['users', 'posts', 'subscriptions', 'chats', 'messages', 'notifications'];
            $tablesExist = [];
            foreach ($requiredTables as $table) {
                $tablesExist[$table] = in_array($table, $tables);
            }
        }
    }
} catch (Exception $e) {
    $dbError = $e->getMessage();
}

// فحص الصلاحيات
$permissions = [
    'config/' => is_writable('config/'),
    'uploads/' => is_writable('.') // فحص المجلد الحالي
];

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .check-container {
            max-width: 900px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .check-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .check-content {
            padding: 2rem;
        }

        .check-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .status-ok {
            color: var(--success-color);
            font-weight: 600;
        }

        .status-error {
            color: var(--danger-color);
            font-weight: 600;
        }

        .status-warning {
            color: var(--warning-color);
            font-weight: 600;
        }

        .overall-status {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .overall-status.success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 2px solid var(--success-color);
        }

        .overall-status.error {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 2px solid var(--danger-color);
        }

        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .system-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="check-container">
        <div class="check-header">
            <h1><i class="fas fa-stethoscope"></i> فحص النظام</h1>
            <p>التحقق من جاهزية منصة المحللين الماليين</p>
        </div>

        <div class="check-content">
            <?php
            // حساب الحالة العامة
            $allOk = $phpOk && 
                     array_reduce($extensions, function($carry, $item) { return $carry && $item; }, true) &&
                     array_reduce($filesExist, function($carry, $item) { return $carry && $item; }, true) &&
                     $dbStatus === 'متصل';
            ?>

            <!-- الحالة العامة -->
            <div class="overall-status <?php echo $allOk ? 'success' : 'error'; ?>">
                <?php if ($allOk): ?>
                    <i class="fas fa-check-circle"></i>
                    النظام جاهز للعمل! جميع المتطلبات متوفرة.
                <?php else: ?>
                    <i class="fas fa-exclamation-triangle"></i>
                    يحتاج النظام إلى إصلاح بعض المشاكل قبل العمل.
                <?php endif; ?>
            </div>

            <!-- فحص PHP -->
            <div class="check-section">
                <h4><i class="fas fa-code"></i> فحص PHP</h4>
                <div class="check-item">
                    <span>إصدار PHP</span>
                    <span class="<?php echo $phpOk ? 'status-ok' : 'status-error'; ?>">
                        <?php echo $phpVersion; ?>
                        <?php echo $phpOk ? '✅' : '❌ (يتطلب 7.4+)'; ?>
                    </span>
                </div>
            </div>

            <!-- فحص الإضافات -->
            <div class="check-section">
                <h4><i class="fas fa-puzzle-piece"></i> إضافات PHP المطلوبة</h4>
                <?php foreach ($extensions as $ext => $loaded): ?>
                    <div class="check-item">
                        <span><?php echo $ext; ?></span>
                        <span class="<?php echo $loaded ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $loaded ? '✅ مثبت' : '❌ غير مثبت'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- فحص الملفات -->
            <div class="check-section">
                <h4><i class="fas fa-file-code"></i> الملفات المطلوبة</h4>
                <?php foreach ($filesExist as $file => $exists): ?>
                    <div class="check-item">
                        <span><?php echo $file; ?></span>
                        <span class="<?php echo $exists ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $exists ? '✅ موجود' : '❌ غير موجود'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- فحص قاعدة البيانات -->
            <div class="check-section">
                <h4><i class="fas fa-database"></i> قاعدة البيانات</h4>
                <div class="check-item">
                    <span>حالة الاتصال</span>
                    <span class="<?php echo $dbStatus === 'متصل' ? 'status-ok' : 'status-error'; ?>">
                        <?php echo $dbStatus === 'متصل' ? '✅ متصل' : '❌ ' . $dbStatus; ?>
                    </span>
                </div>
                
                <?php if ($dbError): ?>
                    <div class="alert alert-danger mt-2">
                        <strong>خطأ في قاعدة البيانات:</strong> <?php echo htmlspecialchars($dbError); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($tablesExist)): ?>
                    <h6 class="mt-3">الجداول المطلوبة:</h6>
                    <?php foreach ($tablesExist as $table => $exists): ?>
                        <div class="check-item">
                            <span><?php echo $table; ?></span>
                            <span class="<?php echo $exists ? 'status-ok' : 'status-error'; ?>">
                                <?php echo $exists ? '✅ موجود' : '❌ غير موجود'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- فحص الصلاحيات -->
            <div class="check-section">
                <h4><i class="fas fa-key"></i> صلاحيات المجلدات</h4>
                <?php foreach ($permissions as $dir => $writable): ?>
                    <div class="check-item">
                        <span><?php echo $dir; ?></span>
                        <span class="<?php echo $writable ? 'status-ok' : 'status-warning'; ?>">
                            <?php echo $writable ? '✅ قابل للكتابة' : '⚠️ غير قابل للكتابة'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- معلومات النظام -->
            <div class="check-section">
                <h4><i class="fas fa-info-circle"></i> معلومات النظام</h4>
                <div class="system-info">
                    <strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?><br>
                    <strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?><br>
                    <strong>مجلد المشروع:</strong> <?php echo __DIR__; ?><br>
                    <strong>وقت الخادم:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                    <strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?>
                </div>
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <?php if (!$allOk): ?>
                    <a href="setup-database.php" class="btn-custom">
                        <i class="fas fa-tools"></i> إعداد قاعدة البيانات
                    </a>
                <?php endif; ?>
                
                <a href="login-sql.php" class="btn-custom">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </a>
                
                <a href="register-sql.php" class="btn-custom">
                    <i class="fas fa-user-plus"></i> إنشاء حساب
                </a>
                
                <a href="index.html" class="btn-custom">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                
                <button onclick="location.reload()" class="btn-custom">
                    <i class="fas fa-sync"></i> إعادة الفحص
                </button>
            </div>

            <!-- نصائح الإصلاح -->
            <?php if (!$allOk): ?>
                <div class="check-section mt-4">
                    <h4><i class="fas fa-wrench"></i> نصائح الإصلاح</h4>
                    
                    <?php if (!$phpOk): ?>
                        <div class="alert alert-warning">
                            <strong>إصدار PHP قديم:</strong> يرجى تحديث PHP إلى الإصدار 7.4 أو أحدث.
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!array_reduce($extensions, function($carry, $item) { return $carry && $item; }, true)): ?>
                        <div class="alert alert-warning">
                            <strong>إضافات مفقودة:</strong> تأكد من تثبيت جميع إضافات PHP المطلوبة.
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!array_reduce($filesExist, function($carry, $item) { return $carry && $item; }, true)): ?>
                        <div class="alert alert-warning">
                            <strong>ملفات مفقودة:</strong> تأكد من نسخ جميع ملفات المشروع إلى المجلد الصحيح.
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($dbStatus !== 'متصل'): ?>
                        <div class="alert alert-danger">
                            <strong>مشكلة في قاعدة البيانات:</strong> 
                            <ul>
                                <li>تأكد من تشغيل MySQL في XAMPP</li>
                                <li>شغّل معالج الإعداد: <a href="setup-database.php">setup-database.php</a></li>
                                <li>تحقق من إعدادات الاتصال في config/database.php</li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

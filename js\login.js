// ===== صفحة تسجيل الدخول =====

document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تسجيل الدخول');
    
    // التحقق من حالة المصادقة
    auth.onAuthStateChanged((user) => {
        if (user) {
            console.log('المستخدم مسجل الدخول بالفعل');
            redirectToDashboard(user);
        }
    });
    
    // إعداد نموذج تسجيل الدخول
    setupLoginForm();
});

// ===== إعداد نموذج تسجيل الدخول =====
function setupLoginForm() {
    const loginForm = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.getElementById('loginBtn');
    const loginBtnText = document.getElementById('loginBtnText');
    const loginSpinner = document.getElementById('loginSpinner');
    const errorMessage = document.getElementById('errorMessage');

    // إضافة مستمع للنموذج
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const email = emailInput.value.trim();
        const password = passwordInput.value;
        
        // التحقق من صحة البيانات
        if (!validateLoginData(email, password)) {
            return;
        }
        
        // إظهار حالة التحميل
        showLoginLoading(true);
        hideError();
        
        try {
            // تسجيل الدخول باستخدام Firebase
            const userCredential = await auth.signInWithEmailAndPassword(email, password);
            const user = userCredential.user;
            
            console.log('تم تسجيل الدخول بنجاح:', user.email);
            
            // توجيه المستخدم إلى لوحة التحكم
            await redirectToDashboard(user);
            
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            showError(getErrorMessage(error.code));
        } finally {
            showLoginLoading(false);
        }
    });

    // تحسين تجربة المستخدم
    emailInput.addEventListener('input', hideError);
    passwordInput.addEventListener('input', hideError);
    
    // التركيز على حقل البريد الإلكتروني
    emailInput.focus();
}

// ===== التحقق من صحة بيانات تسجيل الدخول =====
function validateLoginData(email, password) {
    if (!email) {
        showError('يرجى إدخال البريد الإلكتروني');
        document.getElementById('email').focus();
        return false;
    }
    
    if (!isValidEmail(email)) {
        showError('يرجى إدخال بريد إلكتروني صحيح');
        document.getElementById('email').focus();
        return false;
    }
    
    if (!password) {
        showError('يرجى إدخال كلمة المرور');
        document.getElementById('password').focus();
        return false;
    }
    
    if (password.length < 6) {
        showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        document.getElementById('password').focus();
        return false;
    }
    
    return true;
}

// ===== التحقق من صحة البريد الإلكتروني =====
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// ===== توجيه المستخدم إلى لوحة التحكم =====
async function redirectToDashboard(user) {
    try {
        // جلب دور المستخدم من Firestore
        const userDoc = await db.collection('users').doc(user.uid).get();
        
        if (userDoc.exists) {
            const userData = userDoc.data();
            const userRole = userData.role;
            
            console.log('دور المستخدم:', userRole);
            
            // توجيه المستخدم حسب دوره
            switch (userRole) {
                case 'analyst':
                    window.location.href = 'dashboard-analyst.html';
                    break;
                case 'subscriber':
                    window.location.href = 'dashboard-subscriber.html';
                    break;
                case 'admin':
                    window.location.href = 'dashboard-admin.html';
                    break;
                default:
                    console.error('دور غير معروف:', userRole);
                    showError('حدث خطأ في تحديد نوع الحساب');
                    return;
            }
        } else {
            console.error('لم يتم العثور على بيانات المستخدم');
            showError('لم يتم العثور على بيانات الحساب');
        }
    } catch (error) {
        console.error('خطأ في جلب بيانات المستخدم:', error);
        showError('حدث خطأ في تحميل بيانات الحساب');
    }
}

// ===== إظهار/إخفاء حالة التحميل =====
function showLoginLoading(show) {
    const loginBtn = document.getElementById('loginBtn');
    const loginBtnText = document.getElementById('loginBtnText');
    const loginSpinner = document.getElementById('loginSpinner');
    
    if (show) {
        loginBtn.disabled = true;
        loginBtnText.textContent = 'جاري تسجيل الدخول...';
        loginSpinner.style.display = 'inline-block';
    } else {
        loginBtn.disabled = false;
        loginBtnText.textContent = 'تسجيل الدخول';
        loginSpinner.style.display = 'none';
    }
}

// ===== إظهار رسالة الخطأ =====
function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    
    // التمرير إلى رسالة الخطأ
    errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// ===== إخفاء رسالة الخطأ =====
function hideError() {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.style.display = 'none';
}

// ===== الحصول على رسالة الخطأ المناسبة =====
function getErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/user-not-found':
            return 'لا يوجد حساب مسجل بهذا البريد الإلكتروني';
        case 'auth/wrong-password':
            return 'كلمة المرور غير صحيحة';
        case 'auth/invalid-email':
            return 'البريد الإلكتروني غير صحيح';
        case 'auth/user-disabled':
            return 'تم تعطيل هذا الحساب';
        case 'auth/too-many-requests':
            return 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً';
        case 'auth/network-request-failed':
            return 'خطأ في الاتصال بالإنترنت';
        case 'auth/invalid-credential':
            return 'بيانات تسجيل الدخول غير صحيحة';
        default:
            return 'حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى';
    }
}

// ===== تحسينات إضافية =====

// إضافة تأثيرات بصرية للحقول
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('.form-control');
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.2s ease';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // Enter للتسجيل
    if (e.key === 'Enter' && !e.shiftKey) {
        const loginForm = document.getElementById('loginForm');
        if (document.activeElement.tagName === 'INPUT') {
            e.preventDefault();
            loginForm.dispatchEvent(new Event('submit'));
        }
    }
    
    // Escape للعودة
    if (e.key === 'Escape') {
        window.location.href = 'index-new.html';
    }
});

// تذكر البريد الإلكتروني (اختياري)
function rememberEmail() {
    const emailInput = document.getElementById('email');
    const rememberedEmail = localStorage.getItem('rememberedEmail');
    
    if (rememberedEmail) {
        emailInput.value = rememberedEmail;
    }
    
    // حفظ البريد الإلكتروني عند تسجيل الدخول بنجاح
    document.getElementById('loginForm').addEventListener('submit', function() {
        const email = emailInput.value.trim();
        if (email && isValidEmail(email)) {
            localStorage.setItem('rememberedEmail', email);
        }
    });
}

// تفعيل تذكر البريد الإلكتروني
document.addEventListener('DOMContentLoaded', rememberEmail);

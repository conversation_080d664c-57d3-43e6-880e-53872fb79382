const functions = require('firebase-functions');
const admin = require('firebase-admin');

// تهيئة Firebase Admin
admin.initializeApp();
const db = admin.firestore();

// ===== دالة تحويل المنشورات المدفوعة إلى مجانية بعد 3 ساعات =====
exports.convertPaidPostsToFree = functions.pubsub
  .schedule('every 30 minutes')
  .timeZone('Asia/Amman')
  .onRun(async (context) => {
    console.log('بدء عملية تحويل المنشورات المدفوعة إلى مجانية...');
    
    try {
      // حساب الوقت قبل 3 ساعات
      const threeHoursAgo = new Date();
      threeHoursAgo.setHours(threeHoursAgo.getHours() - 3);
      
      // البحث عن المنشورات المدفوعة التي مر عليها 3 ساعات
      const postsSnapshot = await db.collection('posts')
        .where('isPaidContent', '==', true)
        .where('isFreeNow', '==', false)
        .where('createdAt', '<=', threeHoursAgo)
        .get();
      
      if (postsSnapshot.empty) {
        console.log('لا توجد منشورات للتحويل');
        return null;
      }
      
      // تحديث المنشورات
      const batch = db.batch();
      let updatedCount = 0;
      
      postsSnapshot.forEach(doc => {
        batch.update(doc.ref, {
          isFreeNow: true,
          convertedToFreeAt: admin.firestore.FieldValue.serverTimestamp()
        });
        updatedCount++;
      });
      
      await batch.commit();
      
      console.log(`تم تحويل ${updatedCount} منشور إلى مجاني`);
      
      // إرسال إشعارات للمشتركين
      await sendFreePostNotifications(postsSnapshot);
      
      return null;
      
    } catch (error) {
      console.error('خطأ في تحويل المنشورات:', error);
      throw error;
    }
  });

// ===== دالة إرسال إشعارات المنشورات المجانية =====
async function sendFreePostNotifications(postsSnapshot) {
  try {
    const notifications = [];
    
    for (const postDoc of postsSnapshot.docs) {
      const post = postDoc.data();
      
      // جلب معلومات المحلل
      const analystDoc = await db.collection('users').doc(post.analystId).get();
      if (!analystDoc.exists) continue;
      
      const analyst = analystDoc.data();
      
      // إنشاء إشعار لكل مشترك
      if (analyst.acceptedSubscribers && analyst.acceptedSubscribers.length > 0) {
        for (const subscriberId of analyst.acceptedSubscribers) {
          notifications.push({
            userId: subscriberId,
            type: 'free_post',
            title: 'منشور جديد متاح مجاناً',
            message: `أصبح منشور "${post.title}" من المحلل ${analyst.name} متاحاً مجاناً الآن`,
            data: {
              postId: postDoc.id,
              analystId: post.analystId,
              analystName: analyst.name
            },
            read: false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
          });
        }
      }
    }
    
    // حفظ الإشعارات في قاعدة البيانات
    if (notifications.length > 0) {
      const batch = db.batch();
      notifications.forEach(notification => {
        const notificationRef = db.collection('notifications').doc();
        batch.set(notificationRef, notification);
      });
      
      await batch.commit();
      console.log(`تم إرسال ${notifications.length} إشعار`);
    }
    
  } catch (error) {
    console.error('خطأ في إرسال الإشعارات:', error);
  }
}

// ===== دالة تنظيف البيانات القديمة =====
exports.cleanupOldData = functions.pubsub
  .schedule('0 2 * * *') // يومياً في الساعة 2 صباحاً
  .timeZone('Asia/Amman')
  .onRun(async (context) => {
    console.log('بدء عملية تنظيف البيانات القديمة...');
    
    try {
      // حذف الإشعارات القديمة (أكثر من 30 يوم)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const oldNotifications = await db.collection('notifications')
        .where('createdAt', '<=', thirtyDaysAgo)
        .get();
      
      if (!oldNotifications.empty) {
        const batch = db.batch();
        oldNotifications.forEach(doc => {
          batch.delete(doc.ref);
        });
        
        await batch.commit();
        console.log(`تم حذف ${oldNotifications.size} إشعار قديم`);
      }
      
      // تنظيف المحادثات القديمة غير النشطة (أكثر من 90 يوم)
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
      
      const oldChats = await db.collection('chats')
        .where('lastMessageAt', '<=', ninetyDaysAgo)
        .get();
      
      if (!oldChats.empty) {
        const batch = db.batch();
        oldChats.forEach(doc => {
          batch.delete(doc.ref);
        });
        
        await batch.commit();
        console.log(`تم حذف ${oldChats.size} محادثة قديمة`);
      }
      
      return null;
      
    } catch (error) {
      console.error('خطأ في تنظيف البيانات:', error);
      throw error;
    }
  });

// ===== دالة إنشاء إحصائيات يومية =====
exports.generateDailyStats = functions.pubsub
  .schedule('0 1 * * *') // يومياً في الساعة 1 صباحاً
  .timeZone('Asia/Amman')
  .onRun(async (context) => {
    console.log('بدء إنشاء الإحصائيات اليومية...');
    
    try {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      // إحصائيات المستخدمين
      const usersSnapshot = await db.collection('users').get();
      const analystsCount = usersSnapshot.docs.filter(doc => doc.data().role === 'analyst').length;
      const subscribersCount = usersSnapshot.docs.filter(doc => doc.data().role === 'subscriber').length;
      
      // إحصائيات المنشورات
      const postsSnapshot = await db.collection('posts').get();
      const totalPosts = postsSnapshot.size;
      const paidPosts = postsSnapshot.docs.filter(doc => doc.data().isPaidContent === true).length;
      
      // إحصائيات الاشتراكات
      const subscriptionsSnapshot = await db.collection('subscriptions').get();
      const activeSubscriptions = subscriptionsSnapshot.docs.filter(doc => doc.data().status === 'accepted').length;
      const pendingSubscriptions = subscriptionsSnapshot.docs.filter(doc => doc.data().status === 'pending').length;
      
      // حفظ الإحصائيات
      const statsData = {
        date: admin.firestore.Timestamp.fromDate(yesterday),
        users: {
          total: usersSnapshot.size,
          analysts: analystsCount,
          subscribers: subscribersCount
        },
        posts: {
          total: totalPosts,
          paid: paidPosts,
          free: totalPosts - paidPosts
        },
        subscriptions: {
          active: activeSubscriptions,
          pending: pendingSubscriptions,
          total: subscriptionsSnapshot.size
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      };
      
      await db.collection('analytics').doc(yesterday.toISOString().split('T')[0]).set(statsData);
      
      console.log('تم إنشاء الإحصائيات اليومية بنجاح');
      return null;
      
    } catch (error) {
      console.error('خطأ في إنشاء الإحصائيات:', error);
      throw error;
    }
  });

// ===== دالة معالجة طلبات الاشتراك الجديدة =====
exports.onNewSubscriptionRequest = functions.firestore
  .document('subscriptions/{subscriptionId}')
  .onCreate(async (snap, context) => {
    const subscription = snap.data();
    
    try {
      // إرسال إشعار للمحلل
      await db.collection('notifications').add({
        userId: subscription.analystId,
        type: 'new_subscription_request',
        title: 'طلب اشتراك جديد',
        message: `طلب اشتراك جديد من ${subscription.subscriberName}`,
        data: {
          subscriptionId: context.params.subscriptionId,
          subscriberId: subscription.subscriberId,
          subscriberName: subscription.subscriberName
        },
        read: false,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log(`تم إرسال إشعار طلب اشتراك جديد للمحلل ${subscription.analystId}`);
      
    } catch (error) {
      console.error('خطأ في معالجة طلب الاشتراك الجديد:', error);
    }
  });

// ===== دالة معالجة قبول/رفض الاشتراك =====
exports.onSubscriptionStatusUpdate = functions.firestore
  .document('subscriptions/{subscriptionId}')
  .onUpdate(async (change, context) => {
    const before = change.before.data();
    const after = change.after.data();
    
    // التحقق من تغيير حالة الاشتراك
    if (before.status !== after.status) {
      try {
        let notificationTitle = '';
        let notificationMessage = '';
        
        if (after.status === 'accepted') {
          notificationTitle = 'تم قبول اشتراكك';
          notificationMessage = `تم قبول طلب اشتراكك مع المحلل. يمكنك الآن الوصول للتحليلات المدفوعة`;
          
          // إضافة المشترك لقائمة المشتركين المقبولين
          const analystRef = db.collection('users').doc(after.analystId);
          await analystRef.update({
            acceptedSubscribers: admin.firestore.FieldValue.arrayUnion(after.subscriberId)
          });
          
        } else if (after.status === 'rejected') {
          notificationTitle = 'تم رفض اشتراكك';
          notificationMessage = `تم رفض طلب اشتراكك. يمكنك التواصل مع المحلل لمعرفة السبب`;
        }
        
        // إرسال إشعار للمشترك
        if (notificationTitle) {
          await db.collection('notifications').add({
            userId: after.subscriberId,
            type: 'subscription_status_update',
            title: notificationTitle,
            message: notificationMessage,
            data: {
              subscriptionId: context.params.subscriptionId,
              analystId: after.analystId,
              status: after.status
            },
            read: false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
          });
        }
        
        console.log(`تم تحديث حالة الاشتراك إلى ${after.status}`);
        
      } catch (error) {
        console.error('خطأ في معالجة تحديث حالة الاشتراك:', error);
      }
    }
  });

// ===== دالة منع الروابط الخارجية في المحادثات =====
exports.moderateChat = functions.firestore
  .document('chats/{chatId}')
  .onUpdate(async (change, context) => {
    const before = change.before.data();
    const after = change.after.data();
    
    // التحقق من وجود رسائل جديدة
    if (after.messages && after.messages.length > before.messages.length) {
      const newMessages = after.messages.slice(before.messages.length);
      
      for (const message of newMessages) {
        // البحث عن روابط خارجية
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const socialRegex = /(telegram|whatsapp|instagram|facebook|twitter|snapchat|tiktok)/gi;
        
        if (urlRegex.test(message.text) || socialRegex.test(message.text)) {
          try {
            // حذف الرسالة المخالفة
            const updatedMessages = after.messages.filter(msg => 
              msg.timestamp !== message.timestamp || msg.senderId !== message.senderId
            );
            
            // إضافة رسالة تحذيرية
            updatedMessages.push({
              senderId: 'system',
              text: 'تم حذف رسالة تحتوي على روابط خارجية. يُمنع مشاركة معلومات التواصل الخارجي.',
              timestamp: admin.firestore.FieldValue.serverTimestamp(),
              type: 'warning'
            });
            
            await change.after.ref.update({
              messages: updatedMessages
            });
            
            console.log(`تم حذف رسالة مخالفة في المحادثة ${context.params.chatId}`);
            
          } catch (error) {
            console.error('خطأ في مراقبة المحادثة:', error);
          }
        }
      }
    }
  });

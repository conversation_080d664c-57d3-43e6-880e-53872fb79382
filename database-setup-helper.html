<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعد إنشاء قاعدة البيانات | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .helper-container {
            max-width: 900px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .helper-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .helper-content {
            padding: 2rem;
        }

        .step-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .step-number {
            position: absolute;
            top: -15px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .step-title {
            color: var(--primary-blue);
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            margin-top: 10px;
        }

        .step-description {
            color: #6c757d;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .action-button {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 5px;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }

        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }

        .progress-bar-custom {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            height: 100%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-white);
            font-weight: 600;
            font-size: 0.8rem;
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin: 1rem 0;
        }

        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .checklist-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .checklist-item:last-child {
            border-bottom: none;
        }

        .checklist-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid var(--accent-blue);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .checklist-checkbox.checked {
            background: var(--accent-blue);
            color: var(--primary-white);
        }
    </style>
</head>
<body>
    <div class="helper-container">
        <div class="helper-header">
            <h1><i class="fas fa-database"></i> مساعد إنشاء قاعدة البيانات</h1>
            <p>دليل تفاعلي خطوة بخطوة لإعداد Firebase لمشروع jorinvforex</p>
        </div>

        <div class="helper-content">
            <!-- شريط التقدم -->
            <div class="progress-bar-custom">
                <div class="progress-fill" id="progressBar" style="width: 0%;">0%</div>
            </div>

            <!-- الخطوة 1: Firestore Database -->
            <div class="step-container" id="step1">
                <div class="step-number">1</div>
                <h3 class="step-title">إنشاء Firestore Database</h3>
                <p class="step-description">
                    أولاً، نحتاج لإنشاء قاعدة بيانات Firestore لحفظ بيانات المستخدمين والمنشورات.
                </p>
                
                <div class="alert alert-info alert-custom">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> ستحتاج للدخول بحساب Google الذي أنشأ مشروع jorinvforex
                </div>

                <div class="d-flex flex-wrap align-items-center">
                    <a href="https://console.firebase.google.com/u/0/project/jorinvforex/firestore" 
                       target="_blank" class="action-button">
                        <i class="fas fa-external-link-alt"></i>
                        فتح Firestore Console
                    </a>
                    <span class="status-indicator status-pending" id="status1">
                        <i class="fas fa-clock"></i> في الانتظار
                    </span>
                </div>

                <div class="mt-3">
                    <h6>الخطوات في Firebase Console:</h6>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اضغط "Create database"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اختر "Start in production mode"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اختر المنطقة: us-central1 أو europe-west1</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اضغط "Done" وانتظر الإنشاء</span>
                    </div>
                </div>

                <button class="action-button mt-3" onclick="completeStep(1)">
                    <i class="fas fa-check"></i> أكملت هذه الخطوة
                </button>
            </div>

            <!-- الخطوة 2: Authentication -->
            <div class="step-container" id="step2">
                <div class="step-number">2</div>
                <h3 class="step-title">تفعيل Authentication</h3>
                <p class="step-description">
                    الآن نحتاج لتفعيل نظام المصادقة للسماح للمستخدمين بتسجيل الدخول.
                </p>

                <div class="d-flex flex-wrap align-items-center">
                    <a href="https://console.firebase.google.com/u/0/project/jorinvforex/authentication/providers" 
                       target="_blank" class="action-button">
                        <i class="fas fa-external-link-alt"></i>
                        فتح Authentication Console
                    </a>
                    <span class="status-indicator status-pending" id="status2">
                        <i class="fas fa-clock"></i> في الانتظار
                    </span>
                </div>

                <div class="mt-3">
                    <h6>الخطوات في Firebase Console:</h6>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اضغط "Get started"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اذهب إلى تبويب "Sign-in method"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اضغط على "Email/Password"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>فعّل الخيار الأول واضغط "Save"</span>
                    </div>
                </div>

                <button class="action-button mt-3" onclick="completeStep(2)">
                    <i class="fas fa-check"></i> أكملت هذه الخطوة
                </button>
            </div>

            <!-- الخطوة 3: Storage -->
            <div class="step-container" id="step3">
                <div class="step-number">3</div>
                <h3 class="step-title">تفعيل Storage</h3>
                <p class="step-description">
                    نحتاج Storage لحفظ الصور الشخصية وصور المنشورات.
                </p>

                <div class="d-flex flex-wrap align-items-center">
                    <a href="https://console.firebase.google.com/u/0/project/jorinvforex/storage" 
                       target="_blank" class="action-button">
                        <i class="fas fa-external-link-alt"></i>
                        فتح Storage Console
                    </a>
                    <span class="status-indicator status-pending" id="status3">
                        <i class="fas fa-clock"></i> في الانتظار
                    </span>
                </div>

                <div class="mt-3">
                    <h6>الخطوات في Firebase Console:</h6>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اضغط "Get started"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اختر "Start in production mode"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اختر نفس المنطقة التي اخترتها لـ Firestore</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اضغط "Done"</span>
                    </div>
                </div>

                <button class="action-button mt-3" onclick="completeStep(3)">
                    <i class="fas fa-check"></i> أكملت هذه الخطوة
                </button>
            </div>

            <!-- الخطوة 4: Web App -->
            <div class="step-container" id="step4">
                <div class="step-number">4</div>
                <h3 class="step-title">إنشاء Web App والحصول على المفاتيح</h3>
                <p class="step-description">
                    الآن نحتاج لإنشاء Web App والحصول على مفاتيح API للاستخدام في الكود.
                </p>

                <div class="d-flex flex-wrap align-items-center">
                    <a href="https://console.firebase.google.com/u/0/project/jorinvforex/settings/general" 
                       target="_blank" class="action-button">
                        <i class="fas fa-external-link-alt"></i>
                        فتح Project Settings
                    </a>
                    <span class="status-indicator status-pending" id="status4">
                        <i class="fas fa-clock"></i> في الانتظار
                    </span>
                </div>

                <div class="mt-3">
                    <h6>الخطوات في Firebase Console:</h6>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>في قسم "Your apps"، اضغط على أيقونة &lt;/&gt;</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>أدخل اسم التطبيق: "منصة المحللين الماليين"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>اضغط "Register app"</span>
                    </div>
                    <div class="checklist-item">
                        <div class="checklist-checkbox" onclick="toggleCheck(this)">
                            <i class="fas fa-check" style="display: none;"></i>
                        </div>
                        <span>انسخ الإعدادات (apiKey و appId)</span>
                    </div>
                </div>

                <div class="alert alert-warning alert-custom mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>مهم:</strong> احفظ الإعدادات في مكان آمن - ستحتاجها في الخطوة التالية!
                </div>

                <button class="action-button mt-3" onclick="completeStep(4)">
                    <i class="fas fa-check"></i> أكملت هذه الخطوة
                </button>
            </div>

            <!-- النتيجة النهائية -->
            <div class="step-container" id="finalStep" style="display: none;">
                <div class="step-number">✅</div>
                <h3 class="step-title">تهانينا! تم إعداد قاعدة البيانات بنجاح</h3>
                <p class="step-description">
                    الآن يمكنك الانتقال لتحديث الكود واختبار المنصة.
                </p>

                <div class="d-flex flex-wrap">
                    <a href="setup-project.html" class="action-button">
                        <i class="fas fa-cogs"></i> إعداد المشروع
                    </a>
                    <a href="index-new.html" class="action-button">
                        <i class="fas fa-home"></i> الذهاب للمنصة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let completedSteps = 0;
        const totalSteps = 4;

        function toggleCheck(checkbox) {
            const icon = checkbox.querySelector('i');
            if (checkbox.classList.contains('checked')) {
                checkbox.classList.remove('checked');
                icon.style.display = 'none';
            } else {
                checkbox.classList.add('checked');
                icon.style.display = 'block';
            }
        }

        function completeStep(stepNumber) {
            const statusElement = document.getElementById(`status${stepNumber}`);
            statusElement.className = 'status-indicator status-success';
            statusElement.innerHTML = '<i class="fas fa-check"></i> مكتمل';
            
            completedSteps++;
            updateProgress();
            
            if (completedSteps === totalSteps) {
                document.getElementById('finalStep').style.display = 'block';
                document.getElementById('finalStep').scrollIntoView({ behavior: 'smooth' });
            }
        }

        function updateProgress() {
            const percentage = (completedSteps / totalSteps) * 100;
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = Math.round(percentage) + '%';
        }

        // تحقق من الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('مساعد إنشاء قاعدة البيانات جاهز!');
        });
    </script>
</body>
</html>

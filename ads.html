<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    <title>قنوات التداول المميزة | فوركس الأردن</title>

    <!-- Primary Meta Tags -->
    <meta name="title" content="قنوات التداول المميزة | فوركس الأردن">
    <meta name="description" content="اكتشف أفضل قنوات التداول المميزة مع نسب ربح عالية وأسعار تنافسية">
    <meta name="keywords" content="قنوات تداول, إشارات فوركس, توصيات تداول, فوركس الأردن">
    <meta name="author" content="Forex Jordan">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Custom -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive-enhancements.css">

    <style>
        /* تحسينات عامة للصفحة */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background-color: #0a192f;
            color: #e6f1ff;
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* مؤشر تقدم التمرير */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            z-index: 10000;
            transition: width 0.1s ease;
        }

        /* تصميم الكروت */
        .channel-card {
            background: rgba(10, 25, 47, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(100, 255, 218, 0.3);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .channel-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(100, 255, 218, 0.05) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
            pointer-events: none;
        }

        .channel-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(100, 255, 218, 0.6);
            box-shadow: 0 25px 50px rgba(100, 255, 218, 0.3);
            background: rgba(10, 25, 47, 0.9);
        }

        .channel-header {
            position: relative;
            z-index: 2;
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .channel-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #64ffda, #57cbff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: #0a192f;
            box-shadow: 0 10px 30px rgba(100, 255, 218, 0.4);
            transition: all 0.3s ease;
        }

        .channel-card:hover .channel-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 40px rgba(100, 255, 218, 0.6);
        }

        .channel-name {
            color: #e6f1ff;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }

        .channel-stats {
            position: relative;
            z-index: 2;
            margin-bottom: 2rem;
        }

        .stat-item {
            background: rgba(100, 255, 218, 0.1);
            border: 1px solid rgba(100, 255, 218, 0.2);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(100, 255, 218, 0.15);
            border-color: rgba(100, 255, 218, 0.4);
            transform: translateY(-2px);
        }

        .stat-label {
            color: #8892b0;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            color: #64ffda;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .profit-percentage {
            color: #22c55e;
            font-size: 1.5rem;
            font-weight: 800;
        }

        .price-value {
            color: #fbbf24;
            font-size: 1.4rem;
            font-weight: 700;
        }

        .channel-button {
            background: linear-gradient(45deg, #64ffda, #57cbff);
            color: #0a192f;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 700;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-top: auto;
            position: relative;
            z-index: 2;
        }

        .channel-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(100, 255, 218, 0.4);
            color: #0a192f;
        }

        /* حالة التحميل */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
            flex-direction: column;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(100, 255, 218, 0.3);
            border-top: 4px solid #64ffda;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        .loading-text {
            color: #64ffda;
            font-size: 1.2rem;
            font-weight: 600;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0) rotate(0deg);
            }

            50% {
                transform: translateY(-10px) rotate(2deg);
            }
        }

        /* رسالة الخطأ */
        .error-container {
            text-align: center;
            padding: 3rem;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 15px;
            margin: 2rem 0;
        }

        .error-icon {
            font-size: 3rem;
            color: #ef4444;
            margin-bottom: 1rem;
        }

        .error-title {
            color: #ef4444;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #ccd6f6;
            font-size: 1rem;
            margin-bottom: 1.5rem;
        }

        .retry-button {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            .channel-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .channel-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .channel-name {
                font-size: 1.3rem;
            }

            .stat-value {
                font-size: 1.1rem;
            }

            .profit-percentage {
                font-size: 1.3rem;
            }

            .price-value {
                font-size: 1.2rem;
            }

            .channel-button {
                padding: 0.8rem 1.5rem;
                font-size: 1rem;
            }

            .section-title {
                font-size: 2rem !important;
            }

            .section-subtitle {
                font-size: 1rem !important;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 10px;
            }

            .channel-card {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .channel-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .channel-name {
                font-size: 1.1rem;
            }

            .stat-item {
                padding: 0.8rem;
            }

            .section-title {
                font-size: 1.8rem !important;
                padding: 0 10px;
            }

            .section-subtitle {
                font-size: 0.9rem !important;
                padding: 0 10px;
            }
        }
    </style>
</head>

<body>
    <!-- مؤشر تقدم التمرير -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg fixed-top"
        style="background: rgba(10, 25, 47, 0.85); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); padding: 15px 0; transition: all 0.5s ease; border-bottom: 1px solid rgba(100, 255, 218, 0.1);">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="fxjordan.html"
                style="color: #64ffda; font-weight: 700; font-size: 1.5rem;">
                <img src="assets/img/logo.png" alt="فوركس الأردن"
                    style="height: 40px; margin-left: 10px; border-radius: 50%;">
                فوركس الأردن
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                style="border: 1px solid #64ffda; color: #64ffda;">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" style="font-weight: 500;">
                    <li class="nav-item">
                        <a class="nav-link" href="fxjordan.html#home"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#channels"
                            style="color: #64ffda; margin: 0 10px; position: relative; padding: 8px 15px !important;">قنوات
                            التداول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fxjordan.html#contact"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">تواصل
                            معنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="fxjordan.html#plans" class="btn"
                        style="background: transparent; color: #64ffda; border: 1px solid #64ffda; border-radius: 5px; padding: 10px 20px; font-weight: 500; transition: all 0.3s ease;">
                        العودة للموقع الرئيسي
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- قسم العنوان الرئيسي -->
    <section id="channels" class="py-5"
        style="padding-top: 120px !important; background: linear-gradient(135deg, rgba(10, 25, 47, 0.95), rgba(17, 34, 64, 0.95)); position: relative; overflow: hidden;">
        <!-- خلفية متحركة -->
        <div
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, rgba(100, 255, 218, 0.05) 0%, rgba(100, 255, 218, 0.1) 50%, rgba(100, 255, 218, 0.05) 100%); background-size: 200% 100%; animation: slideBackground 15s linear infinite; opacity: 0.3;">
        </div>

        <div class="container" style="position: relative; z-index: 2;">
            <div class="text-center mb-5">
                <h1 class="section-title"
                    style="color: #e6f1ff; font-size: 3rem; font-weight: 800; margin-bottom: 1rem; background: linear-gradient(90deg, #64ffda, #57cbff, #64ffda); -webkit-background-clip: text; background-clip: text; color: transparent; background-size: 200% 100%; animation: gradientShift 4s ease-in-out infinite;">
                    <i class="fas fa-broadcast-tower" style="color: #64ffda; margin-left: 15px;"></i>
                    قنوات التداول المميزة
                </h1>
                <p class="section-subtitle"
                    style="color: #ccd6f6; font-size: 1.3rem; max-width: 600px; margin: 0 auto 2rem; line-height: 1.6;">
                    اكتشف أفضل قنوات التداول مع نسب ربح عالية وخدمات احترافية
                </p>

                <!-- أزرار التحكم -->
                <div class="d-flex justify-content-center gap-3 mb-4">
                    <button class="btn" onclick="loadChannelsData()" style="background: linear-gradient(45deg, #64ffda, #57cbff); color: #0a192f; border: none; padding: 10px 20px; border-radius: 8px; font-weight: 600;">
                        <i class="fas fa-download"></i> تحميل البيانات الفعلية
                    </button>
                    <button class="btn" onclick="showSampleData()" style="background: transparent; color: #64ffda; border: 1px solid #64ffda; padding: 10px 20px; border-radius: 8px; font-weight: 600;">
                        <i class="fas fa-eye"></i> عرض بيانات تجريبية
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم القنوات -->
    <section class="py-5" style="background-color: #0a192f; position: relative;">
        <div class="container">
            <!-- حاوية المحتوى -->
            <div id="channelsContainer">
                <!-- حالة التحميل -->
                <div id="loadingState" class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">جاري تحميل قنوات التداول...</div>
                </div>

                <!-- حالة الخطأ -->
                <div id="errorState" class="error-container" style="display: none;">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="error-title">حدث خطأ في تحميل البيانات</h3>
                    <p class="error-message">عذراً، لم نتمكن من تحميل قنوات التداول. يرجى التحقق من الرابط والمحاولة مرة
                        أخرى.</p>
                    <button class="retry-button" onclick="loadChannelsData()">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                    <button class="retry-button" onclick="showSampleData()" style="background: linear-gradient(45deg, #64ffda, #57cbff); margin-right: 10px;">
                        <i class="fas fa-eye"></i> عرض بيانات تجريبية
                    </button>
                </div>

                <!-- حاوية الكروت -->
                <div id="channelsGrid" class="row" style="display: none;">
                    <!-- سيتم إدراج الكروت هنا بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer
        style="background: rgba(10, 25, 47, 0.95); border-top: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem 0;">
        <div class="container">
            <div class="text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <img src="assets/img/logo.png" alt="فوركس الأردن"
                        style="height: 40px; margin-left: 10px; border-radius: 50%;">
                    <h5 style="color: #64ffda; font-weight: 700; margin: 0;">فوركس الأردن</h5>
                </div>
                <p style="color: #8892b0; margin-bottom: 1rem;">قنوات التداول المميزة - خبرة 15 عاماً في الأسواق المالية
                </p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="fxjordan.html" style="color: #64ffda; text-decoration: none; transition: all 0.3s ease;">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                    <a href="fxjordan.html#contact"
                        style="color: #64ffda; text-decoration: none; transition: all 0.3s ease;">
                        <i class="fas fa-envelope"></i> تواصل معنا
                    </a>
                </div>
                <hr style="border-color: rgba(100, 255, 218, 0.2); margin: 1.5rem 0;">
                <p style="color: #8892b0; font-size: 0.9rem; margin: 0;">
                    © 2024 فوركس الأردن. جميع الحقوق محفوظة.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // إعدادات Google Sheets
        const SHEET_CONFIG = {
            sheetId: '1wbgkSQoAGYwmdAE06AC9Z0PiPYWEtptHhHFDT4qz8fM',
            sheetName: '0', // معرف الورقة الأولى (gid=0)
            get url() {
                return `https://opensheet.elk.sh/${this.sheetId}/${this.sheetName}`;
            }
        };


        // متغيرات العناصر
        const loadingState = document.getElementById('loadingState');
        const errorState = document.getElementById('errorState');
        const channelsGrid = document.getElementById('channelsGrid');

        // دالة تحميل البيانات من Google Sheets
        async function loadChannelsData() {
            try {
                // إظهار حالة التحميل
                showLoadingState();

                console.log('محاولة تحميل البيانات من:', SHEET_CONFIG.url);

                // جلب البيانات من Google Sheets
                const response = await fetch(SHEET_CONFIG.url);

                console.log('استجابة الخادم:', response.status, response.statusText);
                console.log('رؤوس الاستجابة:', response.headers);

                if (!response.ok) {
                    // محاولة طريقة بديلة إذا فشلت الأولى
                    console.log('محاولة الطريقة البديلة...');
                    return await tryAlternativeMethod();
                }

                const data = await response.json();
                console.log('البيانات المستلمة:', data);
                console.log('عدد الصفوف:', data.length);

                if (data.length > 0) {
                    console.log('أسماء الأعمدة:', Object.keys(data[0]));
                    console.log('الصف الأول:', data[0]);
                }

                // التحقق من وجود البيانات
                if (!data || data.length === 0) {
                    throw new Error('لا توجد بيانات في الجدول');
                }

                // عرض البيانات
                displayChannels(data);

            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                // محاولة الطريقة البديلة
                await tryAlternativeMethod();
            }
        }

        // طريقة بديلة لتحميل البيانات
        async function tryAlternativeMethod() {
            try {
                // استخدام اسم الورقة بدلاً من الرقم
                const alternativeUrl = `https://opensheet.elk.sh/${SHEET_CONFIG.sheetId}/Sheet1`;
                console.log('محاولة الرابط البديل:', alternativeUrl);

                const response = await fetch(alternativeUrl);

                if (!response.ok) {
                    throw new Error(`فشل في تحميل البيانات: ${response.status}`);
                }

                const data = await response.json();
                console.log('البيانات من الطريقة البديلة:', data);

                if (!data || data.length === 0) {
                    // إنشاء بيانات تجريبية للاختبار
                    displaySampleData();
                    return;
                }

                displayChannels(data);

            } catch (error) {
                console.error('فشلت الطريقة البديلة أيضاً:', error);
                // عرض بيانات تجريبية للاختبار
                displaySampleData();
            }
        }

        // دالة عرض بيانات تجريبية للاختبار
        function displaySampleData() {
            console.log('عرض البيانات التجريبية...');
            const sampleData = [
                {
                    'اسم القناة': 'قناة الفوركس الذهبية',
                    'نسبة الربح': '85%',
                    'سعر الاشتراك الشهري': '50$',
                    'رابط القناة': 'https://t.me/example1'
                },
                {
                    'اسم القناة': 'إشارات التداول المتقدمة',
                    'نسبة الربح': '92%',
                    'سعر الاشتراك الشهري': '75$',
                    'رابط القناة': 'https://t.me/example2'
                },
                {
                    'اسم القناة': 'قناة المتداول المحترف',
                    'نسبة الربح': '78%',
                    'سعر الاشتراك الشهري': '40$',
                    'رابط القناة': 'https://t.me/example3'
                }
            ];

            displayChannels(sampleData);

            // إظهار رسالة تنبيه
            setTimeout(() => {
                const alertDiv = document.createElement('div');
                alertDiv.innerHTML = `
                    <div class="alert alert-warning" style="background: rgba(251, 191, 36, 0.1); border: 1px solid rgba(251, 191, 36, 0.3); color: #fbbf24; padding: 1rem; border-radius: 10px; margin: 1rem 0;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> يتم عرض بيانات تجريبية حالياً. يرجى التأكد من أن الجدول متاح للعامة ويحتوي على البيانات الصحيحة.
                    </div>
                `;
                document.querySelector('.container').insertBefore(alertDiv, document.getElementById('channelsContainer'));
            }, 1000);
        }

        // دالة عرض حالة التحميل
        function showLoadingState() {
            loadingState.style.display = 'flex';
            errorState.style.display = 'none';
            channelsGrid.style.display = 'none';
        }

        // دالة عرض حالة الخطأ
        function showErrorState() {
            loadingState.style.display = 'none';
            errorState.style.display = 'block';
            channelsGrid.style.display = 'none';
        }

        // دالة عرض القنوات
        function displayChannels(channels) {
            loadingState.style.display = 'none';
            errorState.style.display = 'none';
            channelsGrid.style.display = 'flex';

            // مسح المحتوى السابق
            channelsGrid.innerHTML = '';

            // التحقق من وجود قنوات
            if (!channels || channels.length === 0) {
                channelsGrid.innerHTML = `
                    <div class="col-12">
                        <div class="text-center" style="padding: 3rem; background: rgba(251, 191, 36, 0.1); border: 1px solid rgba(251, 191, 36, 0.3); border-radius: 15px;">
                            <i class="fas fa-info-circle" style="font-size: 3rem; color: #fbbf24; margin-bottom: 1rem;"></i>
                            <h3 style="color: #fbbf24; margin-bottom: 1rem;">لا توجد قنوات متاحة</h3>
                            <p style="color: #ccd6f6;">لم يتم العثور على أي قنوات في الجدول. تأكد من وجود بيانات في الجدول.</p>
                        </div>
                    </div>
                `;
                return;
            }

            // إنشاء كرت لكل قناة
            channels.forEach((channel, index) => {
                const channelCard = createChannelCard(channel, index);
                channelsGrid.appendChild(channelCard);
            });

            // تطبيق الانيميشن
            animateCards();

            // إضافة رسالة نجاح
            console.log(`تم تحميل ${channels.length} قناة بنجاح`);
        }

        // دالة إنشاء كرت القناة
        function createChannelCard(channel, index) {
            const col = document.createElement('div');
            col.className = 'col-lg-4 col-md-6 col-sm-12 mb-4';

            // استخراج البيانات مع التعامل مع الأسماء المختلفة للأعمدة
            const channelName = channel['اسم القناة'] || channel['Channel Name'] || channel['name'] || 'غير محدد';
            const profitRate = channel['نسبة الربح'] || channel['Profit Rate'] || channel['profit'] || '0%';
            const monthlyPrice = channel['سعر الاشتراك'] || channel['سعر الاشتراك الشهري'] || channel['Monthly Price'] || channel['price'] || '0';
            const channelLink = channel['رابط القناة'] || channel['Channel Link'] || channel['link'] || '#';

            // تنظيف البيانات
            const cleanChannelName = String(channelName).trim();
            const cleanProfitRate = String(profitRate).trim();
            const cleanMonthlyPrice = String(monthlyPrice).trim();
            const cleanChannelLink = String(channelLink).trim();

            col.innerHTML = `
                <div class="channel-card" style="animation-delay: ${index * 0.1}s;">
                    <div class="channel-header">
                        <div class="channel-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="channel-name">${cleanChannelName}</h3>
                    </div>

                    <div class="channel-stats">
                        <div class="stat-item">
                            <div class="stat-label">نسبة الربح</div>
                            <div class="stat-value profit-percentage">
                                <i class="fas fa-percentage"></i> ${cleanProfitRate}
                            </div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">سعر الاشتراك</div>
                            <div class="stat-value price-value">
                                <i class="fas fa-dollar-sign"></i> ${cleanMonthlyPrice}
                            </div>
                        </div>
                    </div>

                    <a href="${cleanChannelLink}" target="_blank" class="channel-button" ${cleanChannelLink === '#' || cleanChannelLink === '' ? 'style="opacity: 0.5; cursor: not-allowed;"' : ''}>
                        <i class="fas fa-external-link-alt"></i>
                        ${cleanChannelLink === '#' || cleanChannelLink === '' ? 'رابط غير متوفر' : 'زيارة القناة'}
                    </a>
                </div>
            `;

            return col;
        }

        // دالة تطبيق الانيميشن على الكروت
        function animateCards() {
            const cards = document.querySelectorAll('.channel-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // دالة تحديث مؤشر التمرير
        function updateScrollProgress() {
            const scrollProgress = document.getElementById('scrollProgress');
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrollPercent = (scrollTop / scrollHeight) * 100;
            scrollProgress.style.width = scrollPercent + '%';
        }

        // إضافة مستمع للتمرير
        window.addEventListener('scroll', updateScrollProgress);

        // دالة عرض البيانات التجريبية مباشرة
        function showSampleData() {
            displaySampleData();
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function () {
            // تحميل البيانات الفعلية من الجدول
            loadChannelsData();
        });

        // إضافة تأثيرات الانيميشن للـ CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            @keyframes slideBackground {
                0% { background-position: 0% 50%; }
                100% { background-position: 200% 50%; }
            }
        `;
        document.head.appendChild(style);
    </script>

</body>

</html>
# 🚀 دليل تشغيل الموقع - jorinvforex

## 📋 المتطلبات الأساسية

### 💻 ما تحتاجه:
- **كمبيوتر** يعمل بـ Windows/Mac/Linux
- **اتصال إنترنت** لتحميل البرامج
- **10 دقائق** من وقتك

---

## 🔧 الخطوة 1: تثبيت XAMPP

### تحميل XAMPP:
1. **اذهب إلى:** https://www.apachefriends.org/download.html
2. **اختر نسختك:**
   - Windows: XAMPP for Windows
   - Mac: XAMPP for OS X
   - Linux: XAMPP for Linux
3. **حمّل الملف** (حوالي 150 MB)

### تثبيت XAMPP:
1. **شغّل ملف التثبيت** كمدير (Run as Administrator)
2. **اختر المكونات:**
   - ✅ Apache
   - ✅ MySQL
   - ✅ PHP
   - ✅ phpMyAdmin
3. **اختر مجلد التثبيت:** C:\xampp (الافتراضي)
4. **اضغط Install** وانتظر انتهاء التثبيت

---

## 📁 الخطوة 2: نسخ ملفات الموقع

### نسخ الملفات:
1. **افتح مجلد XAMPP:** C:\xampp\htdocs
2. **أنشئ مجلد جديد:** jorinvforex
3. **انسخ جميع ملفات المشروع** إلى المجلد الجديد

### هيكل المجلدات يجب أن يكون:
```
C:\xampp\htdocs\jorinvforex\
├── config/
│   └── database.php
├── database/
│   └── database.sql
├── includes/
│   └── auth.php
├── login-sql.php
├── register-sql.php
├── setup-database.php
├── index.html
└── ... باقي الملفات
```

---

## ⚡ الخطوة 3: تشغيل الخوادم

### تشغيل XAMPP:
1. **افتح XAMPP Control Panel**
2. **اضغط Start** بجانب:
   - ✅ **Apache** (خادم الويب)
   - ✅ **MySQL** (قاعدة البيانات)
3. **تأكد من ظهور اللون الأخضر** بجانب كليهما

### إذا ظهرت أخطاء:
```
❌ Port 80 already in use
الحل: غيّر منفذ Apache إلى 8080

❌ Port 3306 already in use  
الحل: غيّر منفذ MySQL إلى 3307

❌ Apache won't start
الحل: شغّل XAMPP كمدير (Run as Administrator)
```

---

## 🗄️ الخطوة 4: إعداد قاعدة البيانات

### الطريقة السهلة (معالج الإعداد):
1. **افتح المتصفح** واذهب إلى:
   ```
   http://localhost/jorinvforex/setup-database.php
   ```

2. **اتبع الخطوات الخمس:**

#### الخطوة 1: اختبار الاتصال
```
خادم قاعدة البيانات: localhost
المنفذ: 3306
اسم المستخدم: root
كلمة المرور: (اتركها فارغة)
```

#### الخطوة 2: إنشاء قاعدة البيانات
```
اسم قاعدة البيانات: jorinvforex
```

#### الخطوة 3: استيراد الهيكل
```
اضغط "استيراد الهيكل" وانتظر
```

#### الخطوة 4: إنشاء مستخدم أدمن
```
اسم المدير: مدير النظام
البريد الإلكتروني: <EMAIL>
كلمة المرور: Admin123456
```

#### الخطوة 5: اكتمال الإعداد
```
🎉 تم! الموقع جاهز للاستخدام
```

---

## 🌐 الخطوة 5: اختبار الموقع

### افتح الموقع:
```
http://localhost/jorinvforex/
```

### جرب الصفحات:
1. **الصفحة الرئيسية:**
   ```
   http://localhost/jorinvforex/index.html
   ```

2. **تسجيل الدخول:**
   ```
   http://localhost/jorinvforex/login-sql.php
   ```

3. **إنشاء حساب:**
   ```
   http://localhost/jorinvforex/register-sql.php
   ```

---

## 🧪 الخطوة 6: اختبار الحسابات التجريبية

### الحسابات الجاهزة:

#### 👑 مدير النظام:
```
البريد: <EMAIL>
كلمة المرور: Admin123456
```

#### 📊 محلل مالي:
```
البريد: <EMAIL>
كلمة المرور: Analyst123456
```

#### 👤 مشترك:
```
البريد: <EMAIL>
كلمة المرور: Subscriber123456
```

### اختبار تسجيل الدخول:
1. **اذهب إلى:** http://localhost/jorinvforex/login-sql.php
2. **جرب أي حساب** من الحسابات أعلاه
3. **تأكد من نجاح تسجيل الدخول**

---

## 🔧 استكشاف الأخطاء

### ❌ "This site can't be reached"
**الحل:**
- تأكد من تشغيل Apache في XAMPP
- تحقق من الرابط: http://localhost/jorinvforex/

### ❌ "Database connection failed"
**الحل:**
- تأكد من تشغيل MySQL في XAMPP
- تحقق من إعدادات قاعدة البيانات
- أعد تشغيل setup-database.php

### ❌ "Page not found"
**الحل:**
- تأكد من وجود الملفات في المجلد الصحيح
- تحقق من المسار: C:\xampp\htdocs\jorinvforex\

### ❌ "Permission denied"
**الحل:**
- شغّل XAMPP كمدير
- تحقق من صلاحيات المجلدات

---

## 📱 الوصول من أجهزة أخرى

### من نفس الشبكة:
1. **اعرف IP الكمبيوتر:**
   ```
   Windows: ipconfig
   Mac/Linux: ifconfig
   ```

2. **افتح من جهاز آخر:**
   ```
   http://*************/jorinvforex/
   (استبدل بـ IP الفعلي)
   ```

---

## 🎯 الخطوات التالية

### بعد تشغيل الموقع بنجاح:
1. **جرب إنشاء حسابات جديدة**
2. **اختبر تسجيل الدخول والخروج**
3. **تصفح الصفحات المختلفة**
4. **جرب الحسابات التجريبية**

### للتطوير:
1. **تعديل الملفات** في مجلد jorinvforex
2. **إعادة تحميل الصفحة** لرؤية التغييرات
3. **استخدام phpMyAdmin** لإدارة قاعدة البيانات

---

## 📞 روابط مفيدة

### لوحات التحكم:
- **XAMPP Control Panel:** تشغيل/إيقاف الخوادم
- **phpMyAdmin:** http://localhost/phpmyadmin
- **معالج الإعداد:** http://localhost/jorinvforex/setup-database.php

### صفحات الموقع:
- **الرئيسية:** http://localhost/jorinvforex/
- **تسجيل الدخول:** http://localhost/jorinvforex/login-sql.php
- **التسجيل:** http://localhost/jorinvforex/register-sql.php

---

## ✅ قائمة مراجعة سريعة

- [ ] تم تثبيت XAMPP
- [ ] تم نسخ ملفات المشروع
- [ ] Apache يعمل (أخضر في XAMPP)
- [ ] MySQL يعمل (أخضر في XAMPP)
- [ ] تم إعداد قاعدة البيانات
- [ ] الموقع يفتح على http://localhost/jorinvforex/
- [ ] تسجيل الدخول يعمل
- [ ] الحسابات التجريبية تعمل

---

## 🎉 تهانينا!

**إذا أكملت جميع الخطوات، فموقع jorinvforex يعمل الآن! 🚀**

**يمكنك الآن:**
- ✅ تسجيل الدخول والخروج
- ✅ إنشاء حسابات جديدة
- ✅ تصفح الموقع
- ✅ اختبار الميزات

**للمساعدة أو الأسئلة، راجع الوثائق أو تواصل معي! 😊**

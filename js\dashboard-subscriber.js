// ===== لوحة تحكم المشترك =====

let currentSubscriber = null;
let allAnalysts = [];
let currentPosts = [];

// ===== تهيئة لوحة التحكم =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل لوحة تحكم المشترك');
    
    // التحقق من المصادقة والدور
    auth.onAuthStateChanged(async (user) => {
        if (user) {
            // التحقق من دور المستخدم
            const userDoc = await db.collection('users').doc(user.uid).get();
            if (userDoc.exists && userDoc.data().role === 'subscriber') {
                currentSubscriber = { uid: user.uid, ...userDoc.data() };
                initDashboard();
            } else {
                // توجيه المستخدم إذا لم يكن مشترك
                window.location.href = 'login.html';
            }
        } else {
            // توجيه إلى صفحة تسجيل الدخول
            window.location.href = 'login.html';
        }
    });
    
    // إعداد التبويبات
    setupTabs();
});

// ===== تهيئة لوحة التحكم =====
async function initDashboard() {
    try {
        // تحديث معلومات المستخدم في الواجهة
        updateUserInfo();
        
        // تحميل المحللين
        await loadAnalysts();
        
        console.log('تم تهيئة لوحة التحكم بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة لوحة التحكم:', error);
        showNotification('حدث خطأ في تحميل البيانات', 'error');
    }
}

// ===== تحديث معلومات المستخدم =====
function updateUserInfo() {
    // تحديث الصورة الشخصية
    const userAvatar = document.getElementById('userAvatar');
    if (currentSubscriber.profilePicURL) {
        userAvatar.src = currentSubscriber.profilePicURL;
    }
    
    // تحديث العنوان
    document.title = `لوحة تحكم ${currentSubscriber.name} | منصة المحللين الماليين`;
}

// ===== إعداد التبويبات =====
function setupTabs() {
    const tabLinks = document.querySelectorAll('[data-bs-toggle="tab"]');
    
    tabLinks.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const targetTab = e.target.getAttribute('href').substring(1);
            loadTabContent(targetTab);
        });
    });
}

// ===== تحميل محتوى التبويب =====
async function loadTabContent(tabName) {
    switch (tabName) {
        case 'analysts':
            if (allAnalysts.length === 0) {
                await loadAnalysts();
            }
            break;
        case 'subscriptions':
            await loadSubscriptions();
            break;
        case 'posts':
            await loadPosts();
            break;
        case 'chat':
            await loadChats();
            break;
    }
}

// ===== تحميل المحللين =====
async function loadAnalysts() {
    const analystsContainer = document.getElementById('analystsList');
    
    try {
        const analystsSnapshot = await db.collection('users')
            .where('role', '==', 'analyst')
            .get();
        
        if (analystsSnapshot.empty) {
            analystsContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-user-tie"></i>
                    <h4>لا يوجد محللين متاحين حالياً</h4>
                    <p>سيتم إضافة محللين ماليين قريباً</p>
                </div>
            `;
            return;
        }
        
        allAnalysts = [];
        let analystsHTML = '';
        
        for (const doc of analystsSnapshot.docs) {
            const analyst = { id: doc.id, ...doc.data() };
            allAnalysts.push(analyst);
            
            // حساب عدد المنشورات
            const postsSnapshot = await db.collection('posts')
                .where('analystId', '==', doc.id)
                .get();
            
            // حساب عدد المشتركين
            const subscribersCount = analyst.acceptedSubscribers ? analyst.acceptedSubscribers.length : 0;
            
            // التحقق من حالة الاشتراك
            const subscriptionStatus = await getSubscriptionStatus(doc.id);
            
            analystsHTML += createAnalystCard(analyst, postsSnapshot.size, subscribersCount, subscriptionStatus);
        }
        
        analystsContainer.innerHTML = analystsHTML;
        
    } catch (error) {
        console.error('خطأ في تحميل المحللين:', error);
        analystsContainer.innerHTML = `
            <div class="text-center py-4 text-danger">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5>حدث خطأ في تحميل المحللين</h5>
                <button class="btn btn-outline-primary" onclick="loadAnalysts()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// ===== إنشاء كرت المحلل =====
function createAnalystCard(analyst, postsCount, subscribersCount, subscriptionStatus) {
    const statusBadge = getStatusBadge(subscriptionStatus);
    const actionButton = getActionButton(analyst.id, subscriptionStatus);
    
    return `
        <div class="analyst-card">
            <div class="analyst-header">
                <img src="${analyst.profilePicURL || 'assets/img/default-avatar.png'}" 
                     alt="${analyst.name}" class="analyst-avatar">
                <div class="analyst-info flex-grow-1">
                    <h5>${analyst.name}</h5>
                    <p>${analyst.school || 'محلل مالي'}</p>
                    <p class="text-muted">${analyst.description || 'لا يوجد وصف متاح'}</p>
                </div>
                ${statusBadge}
            </div>
            
            <div class="analyst-stats">
                <div class="stat-item">
                    <div class="stat-value">${postsCount}</div>
                    <div class="stat-label">منشور</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${subscribersCount}</div>
                    <div class="stat-label">مشترك</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">4.8</div>
                    <div class="stat-label">تقييم</div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                ${actionButton}
                <button class="btn-secondary-custom" onclick="viewAnalystProfile('${analyst.id}')">
                    <i class="fas fa-eye"></i>
                    عرض الملف
                </button>
            </div>
        </div>
    `;
}

// ===== الحصول على حالة الاشتراك =====
async function getSubscriptionStatus(analystId) {
    try {
        const subscriptionSnapshot = await db.collection('subscriptions')
            .where('analystId', '==', analystId)
            .where('subscriberId', '==', currentSubscriber.uid)
            .get();
        
        if (subscriptionSnapshot.empty) {
            return 'none'; // لا يوجد اشتراك
        }
        
        const subscription = subscriptionSnapshot.docs[0].data();
        return subscription.status; // pending, accepted, rejected
        
    } catch (error) {
        console.error('خطأ في جلب حالة الاشتراك:', error);
        return 'none';
    }
}

// ===== الحصول على شارة الحالة =====
function getStatusBadge(status) {
    switch (status) {
        case 'accepted':
            return '<span class="badge bg-success"><i class="fas fa-check"></i> مشترك</span>';
        case 'pending':
            return '<span class="badge bg-warning"><i class="fas fa-clock"></i> قيد المراجعة</span>';
        case 'rejected':
            return '<span class="badge bg-danger"><i class="fas fa-times"></i> مرفوض</span>';
        default:
            return '';
    }
}

// ===== الحصول على زر الإجراء =====
function getActionButton(analystId, status) {
    switch (status) {
        case 'accepted':
            return `<button class="btn-secondary-custom" onclick="openChat('${analystId}')">
                        <i class="fas fa-comments"></i> محادثة
                    </button>`;
        case 'pending':
            return `<button class="btn btn-outline-secondary" disabled>
                        <i class="fas fa-clock"></i> قيد المراجعة
                    </button>`;
        case 'rejected':
            return `<button class="btn-primary-custom" onclick="requestSubscription('${analystId}')">
                        <i class="fas fa-redo"></i> طلب مجدداً
                    </button>`;
        default:
            return `<button class="btn-primary-custom" onclick="requestSubscription('${analystId}')">
                        <i class="fas fa-plus"></i> طلب اشتراك
                    </button>`;
    }
}

// ===== طلب اشتراك =====
async function requestSubscription(analystId) {
    try {
        // التحقق من وجود طلب سابق
        const existingRequest = await db.collection('subscriptions')
            .where('analystId', '==', analystId)
            .where('subscriberId', '==', currentSubscriber.uid)
            .get();
        
        if (!existingRequest.empty) {
            showNotification('لديك طلب اشتراك موجود بالفعل', 'warning');
            return;
        }
        
        // إنشاء طلب اشتراك جديد
        await db.collection('subscriptions').add({
            analystId: analystId,
            subscriberId: currentSubscriber.uid,
            subscriberName: currentSubscriber.name,
            subscriberEmail: currentSubscriber.email,
            status: 'pending',
            paymentConfirmed: false,
            createdAt: firebase.firestore.FieldValue.serverTimestamp()
        });
        
        showNotification('تم إرسال طلب الاشتراك بنجاح', 'success');
        
        // إعادة تحميل المحللين
        await loadAnalysts();
        
    } catch (error) {
        console.error('خطأ في إرسال طلب الاشتراك:', error);
        showNotification('حدث خطأ في إرسال الطلب', 'error');
    }
}

// ===== عرض ملف المحلل =====
function viewAnalystProfile(analystId) {
    // سيتم تنفيذها لاحقاً
    showNotification('ميزة عرض الملف الشخصي ستكون متاحة قريباً', 'info');
}

// ===== فتح المحادثة =====
function openChat(analystId) {
    // سيتم تنفيذها لاحقاً
    showNotification('ميزة المحادثة ستكون متاحة قريباً', 'info');
}

// ===== البحث عن المحللين =====
function searchAnalysts() {
    const searchTerm = document.getElementById('searchAnalysts').value.toLowerCase();
    
    if (!searchTerm) {
        loadAnalysts();
        return;
    }
    
    const filteredAnalysts = allAnalysts.filter(analyst => 
        analyst.name.toLowerCase().includes(searchTerm) ||
        (analyst.school && analyst.school.toLowerCase().includes(searchTerm)) ||
        (analyst.description && analyst.description.toLowerCase().includes(searchTerm))
    );
    
    displayFilteredAnalysts(filteredAnalysts);
}

// ===== عرض المحللين المفلترين =====
function displayFilteredAnalysts(analysts) {
    const analystsContainer = document.getElementById('analystsList');
    
    if (analysts.length === 0) {
        analystsContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h4>لا توجد نتائج</h4>
                <p>لم يتم العثور على محللين يطابقون البحث</p>
            </div>
        `;
        return;
    }
    
    let analystsHTML = '';
    analysts.forEach(analyst => {
        // هذا مبسط - في التطبيق الحقيقي نحتاج لجلب البيانات الإضافية
        analystsHTML += createAnalystCard(analyst, 0, 0, 'none');
    });
    
    analystsContainer.innerHTML = analystsHTML;
}

// ===== تحميل الاشتراكات =====
async function loadSubscriptions() {
    const subscriptionsContainer = document.getElementById('subscriptionsList');
    subscriptionsContainer.innerHTML = '<p class="text-center">قسم الاشتراكات قيد التطوير</p>';
}

// ===== تحميل المنشورات =====
async function loadPosts() {
    const postsContainer = document.getElementById('postsList');
    postsContainer.innerHTML = '<p class="text-center">قسم التحليلات قيد التطوير</p>';
}

// ===== تحميل المحادثات =====
async function loadChats() {
    const chatContainer = document.getElementById('chatList');
    chatContainer.innerHTML = '<p class="text-center">قسم المحادثات قيد التطوير</p>';
}

// تصدير الدوال للاستخدام العام
window.requestSubscription = requestSubscription;
window.viewAnalystProfile = viewAnalystProfile;
window.openChat = openChat;
window.searchAnalysts = searchAnalysts;

<?php
/**
 * معالج إعداد قاعدة البيانات المحسن - منصة المحللين الماليين
 * jorinvforex Database Setup Wizard - Enhanced Version
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// معالجة الطلبات
$step = $_GET['step'] ?? 1;
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['setup_database'])) {
        $result = setupCompleteDatabase();
        if ($result['success']) {
            $message = $result['message'];
            $step = 2;
        } else {
            $error = $result['message'];
        }
    }
}

/**
 * إعداد قاعدة البيانات الكاملة
 */
function setupCompleteDatabase() {
    try {
        // الاتصال بـ MySQL
        $pdo = new PDO(
            "mysql:host=localhost;charset=utf8mb4",
            'root',
            '',
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
        
        // حذف قاعدة البيانات إذا كانت موجودة
        $pdo->exec("DROP DATABASE IF EXISTS jorinvforex");
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE jorinvforex CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // استخدام قاعدة البيانات
        $pdo->exec("USE jorinvforex");
        
        // إنشاء الجداول واحد تلو الآخر
        createUsersTable($pdo);
        createPostsTable($pdo);
        createSubscriptionsTable($pdo);
        createChatsTable($pdo);
        createMessagesTable($pdo);
        createNotificationsTable($pdo);
        createRatingsTable($pdo);
        createDailyStatsTable($pdo);
        createUserSessionsTable($pdo);
        createActivityLogsTable($pdo);
        
        // إنشاء Views
        createViews($pdo);
        
        // إدراج البيانات التجريبية
        insertSampleData($pdo);
        
        return [
            'success' => true,
            'message' => 'تم إعداد قاعدة البيانات بنجاح! تم إنشاء جميع الجداول والبيانات التجريبية.'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage()
        ];
    }
}

function createUsersTable($pdo) {
    $sql = "CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        role ENUM('admin', 'analyst', 'subscriber') NOT NULL DEFAULT 'subscriber',
        phone VARCHAR(20),
        profile_pic_url VARCHAR(500),
        cover_pic_url VARCHAR(500),
        theme_color VARCHAR(7) DEFAULT '#66CCFF',
        bio TEXT,
        school VARCHAR(255),
        experience_years INT DEFAULT 0,
        specialization VARCHAR(255),
        is_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_uuid (uuid),
        INDEX idx_active (is_active)
    )";
    $pdo->exec($sql);
}

function createPostsTable($pdo) {
    $sql = "CREATE TABLE posts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
        analyst_id INT NOT NULL,
        title VARCHAR(500) NOT NULL,
        description TEXT NOT NULL,
        content LONGTEXT,
        image_url VARCHAR(500),
        tradingview_link VARCHAR(500),
        is_paid_content BOOLEAN DEFAULT FALSE,
        is_free_now BOOLEAN DEFAULT FALSE,
        price DECIMAL(10,2) DEFAULT 0.00,
        currency VARCHAR(3) DEFAULT 'USD',
        views_count INT DEFAULT 0,
        likes_count INT DEFAULT 0,
        comments_count INT DEFAULT 0,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        published_at TIMESTAMP NULL,
        free_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_analyst (analyst_id),
        INDEX idx_status (status),
        INDEX idx_paid (is_paid_content),
        INDEX idx_published (published_at),
        INDEX idx_uuid (uuid)
    )";
    $pdo->exec($sql);
}

function createSubscriptionsTable($pdo) {
    $sql = "CREATE TABLE subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
        analyst_id INT NOT NULL,
        subscriber_id INT NOT NULL,
        status ENUM('pending', 'accepted', 'rejected', 'cancelled') DEFAULT 'pending',
        payment_confirmed BOOLEAN DEFAULT FALSE,
        payment_method VARCHAR(50),
        payment_reference VARCHAR(255),
        amount DECIMAL(10,2) DEFAULT 0.00,
        currency VARCHAR(3) DEFAULT 'USD',
        subscription_type ENUM('monthly', 'quarterly', 'yearly') DEFAULT 'monthly',
        expires_at TIMESTAMP NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (subscriber_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_subscription (analyst_id, subscriber_id),
        INDEX idx_analyst (analyst_id),
        INDEX idx_subscriber (subscriber_id),
        INDEX idx_status (status),
        INDEX idx_uuid (uuid)
    )";
    $pdo->exec($sql);
}

function createChatsTable($pdo) {
    $sql = "CREATE TABLE chats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
        analyst_id INT NOT NULL,
        subscriber_id INT NOT NULL,
        last_message_id INT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (subscriber_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_chat (analyst_id, subscriber_id),
        INDEX idx_analyst (analyst_id),
        INDEX idx_subscriber (subscriber_id),
        INDEX idx_uuid (uuid)
    )";
    $pdo->exec($sql);
}

function createMessagesTable($pdo) {
    $sql = "CREATE TABLE messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
        chat_id INT NOT NULL,
        sender_id INT NOT NULL,
        message TEXT NOT NULL,
        message_type ENUM('text', 'image', 'file', 'link') DEFAULT 'text',
        file_url VARCHAR(500),
        is_read BOOLEAN DEFAULT FALSE,
        is_deleted BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE,
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_chat (chat_id),
        INDEX idx_sender (sender_id),
        INDEX idx_created (created_at),
        INDEX idx_uuid (uuid)
    )";
    $pdo->exec($sql);
}

function createNotificationsTable($pdo) {
    $sql = "CREATE TABLE notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
        user_id INT NOT NULL,
        type VARCHAR(50) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        data JSON,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user (user_id),
        INDEX idx_type (type),
        INDEX idx_read (is_read),
        INDEX idx_created (created_at),
        INDEX idx_uuid (uuid)
    )";
    $pdo->exec($sql);
}

function createRatingsTable($pdo) {
    $sql = "CREATE TABLE ratings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid VARCHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
        analyst_id INT NOT NULL,
        subscriber_id INT NOT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (analyst_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (subscriber_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_rating (analyst_id, subscriber_id),
        INDEX idx_analyst (analyst_id),
        INDEX idx_rating (rating),
        INDEX idx_uuid (uuid)
    )";
    $pdo->exec($sql);
}

function createDailyStatsTable($pdo) {
    $sql = "CREATE TABLE daily_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE NOT NULL,
        total_users INT DEFAULT 0,
        new_users INT DEFAULT 0,
        total_analysts INT DEFAULT 0,
        total_subscribers INT DEFAULT 0,
        total_posts INT DEFAULT 0,
        new_posts INT DEFAULT 0,
        total_subscriptions INT DEFAULT 0,
        new_subscriptions INT DEFAULT 0,
        total_messages INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_date (date),
        INDEX idx_date (date)
    )";
    $pdo->exec($sql);
}

function createUserSessionsTable($pdo) {
    $sql = "CREATE TABLE user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user (user_id),
        INDEX idx_token (session_token),
        INDEX idx_expires (expires_at)
    )";
    $pdo->exec($sql);
}

function createViews($pdo) {
    // عرض المحللين مع إحصائياتهم
    $sql = "CREATE VIEW analysts_stats AS
    SELECT
        u.id,
        u.uuid,
        u.name,
        u.email,
        u.bio,
        u.school,
        u.experience_years,
        u.specialization,
        u.profile_pic_url,
        u.theme_color,
        COUNT(DISTINCT s.id) as subscribers_count,
        COUNT(DISTINCT p.id) as posts_count,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(DISTINCT r.id) as ratings_count,
        u.created_at
    FROM users u
    LEFT JOIN subscriptions s ON u.id = s.analyst_id AND s.status = 'accepted'
    LEFT JOIN posts p ON u.id = p.analyst_id AND p.status = 'published'
    LEFT JOIN ratings r ON u.id = r.analyst_id
    WHERE u.role = 'analyst' AND u.is_active = TRUE
    GROUP BY u.id";
    $pdo->exec($sql);

    // عرض المنشورات مع تفاصيل المحلل
    $sql = "CREATE VIEW posts_with_analyst AS
    SELECT
        p.*,
        u.name as analyst_name,
        u.profile_pic_url as analyst_pic,
        u.theme_color as analyst_theme
    FROM posts p
    JOIN users u ON p.analyst_id = u.id
    WHERE p.status = 'published'";
    $pdo->exec($sql);
}

function insertSampleData($pdo) {
    // إدراج مستخدم أدمن (كلمة المرور: Admin123456)
    $sql = "INSERT INTO users (email, password_hash, name, role, bio, is_verified, is_active, email_verified) VALUES
    ('<EMAIL>', '$2y$10\$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'مدير النظام', 'admin', 'مدير منصة المحللين الماليين', TRUE, TRUE, TRUE)";
    $pdo->exec($sql);

    // إدراج محلل تجريبي (كلمة المرور: Analyst123456)
    $sql = "INSERT INTO users (email, password_hash, name, role, bio, school, experience_years, specialization, is_verified, is_active, email_verified) VALUES
    ('<EMAIL>', '$2y$10\$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'أحمد محمد', 'analyst', 'محلل مالي معتمد مع خبرة 5 سنوات في أسواق الفوركس والأسهم', 'جامعة الأردن - كلية الاقتصاد', 5, 'تحليل الفوركس والأسهم', TRUE, TRUE, TRUE)";
    $pdo->exec($sql);

    // إدراج مشترك تجريبي (كلمة المرور: Subscriber123456)
    $sql = "INSERT INTO users (email, password_hash, name, role, bio, is_active, email_verified) VALUES
    ('<EMAIL>', '$2y$10\$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'سارة أحمد', 'subscriber', 'مستثمرة مهتمة بأسواق المال والعملات', TRUE, TRUE)";
    $pdo->exec($sql);

    // إدراج منشور تجريبي
    $sql = "INSERT INTO posts (analyst_id, title, description, content, is_paid_content, status, published_at) VALUES
    (2, 'تحليل زوج EUR/USD - فرصة شراء قوية', 'بناءً على التحليل الفني والأساسي، نتوقع ارتفاع زوج اليورو دولار خلال الأسبوع القادم...', 'تحليل مفصل للزوج مع نقاط الدخول والخروج والأهداف المتوقعة.', TRUE, 'published', NOW())";
    $pdo->exec($sql);

    // إدراج طلب اشتراك تجريبي
    $sql = "INSERT INTO subscriptions (analyst_id, subscriber_id, status) VALUES (2, 3, 'pending')";
    $pdo->exec($sql);

    // إدراج إحصائيات يومية
    $sql = "INSERT INTO daily_stats (date, total_users, new_users, total_analysts, total_subscribers, total_posts, new_posts) VALUES
    (CURDATE(), 3, 3, 1, 1, 1, 1)";
    $pdo->exec($sql);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات المحسن | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .setup-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .setup-content {
            padding: 2rem;
        }

        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .step-indicator {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-database"></i> إعداد قاعدة البيانات المحسن</h1>
            <p>إعداد قاعدة بيانات منصة المحللين الماليين بطريقة محسنة</p>
        </div>

        <div class="setup-content">
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <div class="step-indicator">
                    <h4><i class="fas fa-info-circle"></i> الخطوة 1: إعداد قاعدة البيانات</h4>
                    <p>سيتم إنشاء قاعدة بيانات جديدة مع جميع الجداول والبيانات التجريبية.</p>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم:</h6>
                    <ul class="mb-0">
                        <li>سيتم حذف قاعدة البيانات الحالية إذا كانت موجودة</li>
                        <li>تأكد من تشغيل MySQL في XAMPP</li>
                        <li>سيتم إنشاء حسابات تجريبية للاختبار</li>
                    </ul>
                </div>

                <form method="POST" class="text-center">
                    <button type="submit" name="setup_database" class="btn btn-custom btn-lg">
                        <i class="fas fa-rocket"></i> إعداد قاعدة البيانات الآن
                    </button>
                </form>

            <?php elseif ($step == 2): ?>
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle"></i> تم الإعداد بنجاح!</h4>
                    <p>تم إنشاء قاعدة البيانات وجميع الجداول والبيانات التجريبية بنجاح.</p>
                </div>

                <div class="step-indicator">
                    <h5>الحسابات التجريبية المتاحة:</h5>
                    <ul>
                        <li><strong>مدير:</strong> <EMAIL> / Admin123456</li>
                        <li><strong>محلل:</strong> <EMAIL> / Analyst123456</li>
                        <li><strong>مشترك:</strong> <EMAIL> / Subscriber123456</li>
                    </ul>
                </div>

                <div class="text-center">
                    <a href="index-sql.php" class="btn btn-custom me-2">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                    <a href="login-sql.php" class="btn btn-custom me-2">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                    <a href="system-check.php" class="btn btn-custom">
                        <i class="fas fa-stethoscope"></i> فحص النظام
                    </a>
                </div>
            <?php endif; ?>

            <div class="text-center mt-4">
                <small class="text-muted">
                    <a href="setup-database.php">العودة للمعالج العادي</a> |
                    <a href="system-check.php">فحص النظام</a>
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

function createActivityLogsTable($pdo) {
    $sql = "CREATE TABLE activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(50),
        record_id INT,
        old_data JSON,
        new_data JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user (user_id),
        INDEX idx_action (action),
        INDEX idx_table (table_name),
        INDEX idx_created (created_at)
    )";
    $pdo->exec($sql);
}

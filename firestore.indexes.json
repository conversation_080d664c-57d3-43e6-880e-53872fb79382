{"indexes": [{"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "analystId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPaid<PERSON><PERSON>nt", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPaid<PERSON><PERSON>nt", "order": "ASCENDING"}, {"fieldPath": "isFreeNow", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "analystId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriberId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "analystId", "order": "ASCENDING"}, {"fieldPath": "subscriberId", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lastMessageAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "ratings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "analystId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "ratings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriberId", "order": "ASCENDING"}, {"fieldPath": "analystId", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "posts", "fieldPath": "createdAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "subscriptions", "fieldPath": "createdAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "notifications", "fieldPath": "createdAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users", "fieldPath": "createdAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}]}
@echo off
chcp 65001 >nul
title تشغيل موقع jorinvforex

echo.
echo ═══════════════════════════════════════════════════════════
echo                    🚀 تشغيل موقع jorinvforex
echo ═══════════════════════════════════════════════════════════
echo.

:: التحقق من وجود XAMPP
if not exist "C:\xampp\xampp-control.exe" (
    echo ❌ XAMPP غير مثبت!
    echo.
    echo يرجى تثبيت XAMPP أولاً من:
    echo https://www.apachefriends.org/download.html
    echo.
    pause
    exit /b 1
)

:: التحقق من وجود ملفات المشروع
if not exist "C:\xampp\htdocs\jorinvforex" (
    echo ❌ ملفات المشروع غير موجودة!
    echo.
    echo يرجى نسخ ملفات المشروع إلى:
    echo C:\xampp\htdocs\jorinvforex\
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على XAMPP
echo ✅ تم العثور على ملفات المشروع
echo.

:: تشغيل XAMPP Control Panel
echo 🔄 تشغيل XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"

:: انتظار قليل
timeout /t 3 /nobreak >nul

echo.
echo 📋 تعليمات مهمة:
echo.
echo 1. في XAMPP Control Panel:
echo    - اضغط Start بجانب Apache
echo    - اضغط Start بجانب MySQL
echo    - تأكد من ظهور اللون الأخضر
echo.
echo 2. بعد تشغيل الخوادم، اضغط أي مفتاح للمتابعة...
pause >nul

:: فتح صفحة فحص النظام
echo.
echo 🌐 فتح صفحة فحص النظام...
start "" "http://localhost/jorinvforex/system-check.php"

:: انتظار قليل
timeout /t 2 /nobreak >nul

:: فتح معالج الإعداد
echo 🛠️ فتح معالج إعداد قاعدة البيانات...
start "" "http://localhost/jorinvforex/setup-database.php"

:: انتظار قليل
timeout /t 2 /nobreak >nul

:: فتح الصفحة الرئيسية
echo 🏠 فتح الصفحة الرئيسية...
start "" "http://localhost/jorinvforex/"

echo.
echo ═══════════════════════════════════════════════════════════
echo                        🎉 تم التشغيل!
echo ═══════════════════════════════════════════════════════════
echo.
echo 📱 الروابط المفيدة:
echo.
echo 🔍 فحص النظام:
echo http://localhost/jorinvforex/system-check.php
echo.
echo 🛠️ إعداد قاعدة البيانات:
echo http://localhost/jorinvforex/setup-database.php
echo.
echo 🏠 الصفحة الرئيسية:
echo http://localhost/jorinvforex/
echo.
echo 🔐 تسجيل الدخول:
echo http://localhost/jorinvforex/login-sql.php
echo.
echo 📝 إنشاء حساب:
echo http://localhost/jorinvforex/register-sql.php
echo.
echo ═══════════════════════════════════════════════════════════
echo                    🧪 حسابات تجريبية
echo ═══════════════════════════════════════════════════════════
echo.
echo 👑 مدير النظام:
echo    البريد: <EMAIL>
echo    كلمة المرور: Admin123456
echo.
echo 📊 محلل مالي:
echo    البريد: <EMAIL>
echo    كلمة المرور: Analyst123456
echo.
echo 👤 مشترك:
echo    البريد: <EMAIL>
echo    كلمة المرور: Subscriber123456
echo.
echo ═══════════════════════════════════════════════════════════

echo.
echo اضغط أي مفتاح للخروج...
pause >nul

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // ===== قواعد المستخدمين =====
    match /users/{userId} {
      // السماح للمستخدم بقراءة وتعديل بياناته الخاصة
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // السماح لجميع المستخدمين المسجلين بقراءة البيانات العامة للمستخدمين الآخرين
      allow read: if request.auth != null;
      
      // السماح للأدمن بالوصول الكامل
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد المنشورات =====
    match /posts/{postId} {
      // السماح لجميع المستخدمين المسجلين بقراءة المنشورات
      allow read: if request.auth != null;
      
      // السماح للمحلل بإنشاء وتعديل منشوراته فقط
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.analystId &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'analyst';
      
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.analystId &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'analyst';
      
      // السماح للأدمن بالوصول الكامل
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد الاشتراكات =====
    match /subscriptions/{subscriptionId} {
      // السماح للمحلل والمشترك المعنيين بقراءة وتعديل الاشتراك
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.analystId || 
         request.auth.uid == resource.data.subscriberId);
      
      // السماح بإنشاء اشتراك جديد للمشتركين
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.subscriberId &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'subscriber';
      
      // السماح للمحلل بتحديث حالة الاشتراك
      allow update: if request.auth != null && 
        request.auth.uid == resource.data.analystId &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'analyst';
      
      // السماح للأدمن بالوصول الكامل
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد المحادثات =====
    match /chats/{chatId} {
      // استخراج معرفات المحلل والمشترك من معرف المحادثة
      // تنسيق chatId: analystId_subscriberId
      function getAnalystId() {
        return chatId.split('_')[0];
      }
      
      function getSubscriberId() {
        return chatId.split('_')[1];
      }
      
      // السماح للمحلل والمشترك المعنيين فقط
      allow read, write: if request.auth != null && 
        (request.auth.uid == getAnalystId() || request.auth.uid == getSubscriberId());
      
      // التأكد من أن المحادثة بين محلل ومشترك مقبول
      allow read, write: if request.auth != null && 
        request.auth.uid == getSubscriberId() &&
        getSubscriberId() in get(/databases/$(database)/documents/users/$(getAnalystId())).data.acceptedSubscribers;
      
      // السماح للأدمن بالوصول الكامل
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد الإعلانات =====
    match /ads/{adId} {
      // السماح لجميع المستخدمين بقراءة الإعلانات النشطة
      allow read: if request.auth != null && resource.data.active == true;
      
      // السماح للأدمن فقط بإدارة الإعلانات
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد الإحصائيات =====
    match /analytics/{docId} {
      // السماح للأدمن فقط بالوصول للإحصائيات
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد التقييمات =====
    match /ratings/{ratingId} {
      // السماح لجميع المستخدمين بقراءة التقييمات
      allow read: if request.auth != null;
      
      // السماح للمشترك بإضافة تقييم للمحلل الذي يشترك معه
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.subscriberId &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'subscriber';
      
      // السماح للمستخدم بتعديل تقييمه الخاص
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.subscriberId;
      
      // السماح للأدمن بالوصول الكامل
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد الإشعارات =====
    match /notifications/{notificationId} {
      // السماح للمستخدم بقراءة إشعاراته الخاصة
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      
      // السماح بإنشاء إشعارات للمستخدمين
      allow create: if request.auth != null;
      
      // السماح للمستخدم بتحديث حالة إشعاراته (مقروء/غير مقروء)
      allow update: if request.auth != null && 
        request.auth.uid == resource.data.userId &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read', 'readAt']);
      
      // السماح للأدمن بالوصول الكامل
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== قواعد عامة للمجموعات الأخرى =====
    match /{document=**} {
      // منع الوصول لأي مجموعات أخرى غير محددة
      allow read, write: if false;
    }
  }
}

// ===== ملاحظات مهمة =====
// 1. تأكد من أن جميع المستخدمين لديهم حقل 'role' في وثيقة المستخدم
// 2. استخدم هذه القواعد كنقطة بداية وقم بتخصيصها حسب احتياجاتك
// 3. اختبر القواعد جيداً قبل النشر في الإنتاج
// 4. راجع القواعد بانتظام وحدثها حسب الحاجة
// 5. استخدم Firebase Emulator للاختبار المحلي

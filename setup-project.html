<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد مشروع jorinvforex | منصة المحللين الماليين</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .setup-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .setup-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .setup-header p {
            opacity: 0.9;
            margin: 0;
        }

        .setup-content {
            padding: 2rem;
        }

        .step-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .step-card:hover {
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .step-title {
            color: var(--primary-blue);
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .status-warning {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }

        .status-danger {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }

        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .project-info {
            background: rgba(102, 204, 255, 0.1);
            border: 1px solid var(--accent-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-cogs"></i> إعداد مشروع jorinvforex</h1>
            <p>منصة المحللين الماليين - دليل الإعداد الشامل</p>
        </div>

        <div class="setup-content">
            <!-- معلومات المشروع -->
            <div class="project-info">
                <h5><i class="fas fa-info-circle"></i> معلومات المشروع</h5>
                <div class="row">
                    <div class="col-md-6">
                        <strong>اسم المشروع:</strong> jorinvforex<br>
                        <strong>معرف المشروع:</strong> jorinvforex
                    </div>
                    <div class="col-md-6">
                        <strong>رقم المشروع:</strong> 862703765675<br>
                        <strong>الحالة:</strong> <span id="projectStatus" class="status-indicator status-warning">
                            <i class="fas fa-clock"></i> يحتاج إعداد
                        </span>
                    </div>
                </div>
            </div>

            <!-- الخطوة 1: الحصول على Web API Key -->
            <div class="step-card">
                <div class="step-number">1</div>
                <h4 class="step-title">الحصول على Web API Key</h4>
                <p>تحتاج للحصول على Web API Key من Firebase Console:</p>
                <ol>
                    <li>اذهب إلى <a href="https://console.firebase.google.com/u/0/project/jorinvforex/settings/general" target="_blank">Firebase Console - Project Settings</a></li>
                    <li>انتقل إلى تبويب "General"</li>
                    <li>في قسم "Your apps"، اضغط على "Add app" واختر "Web"</li>
                    <li>أدخل اسم التطبيق: "منصة المحللين الماليين"</li>
                    <li>انسخ الإعدادات التي ستظهر</li>
                </ol>
                
                <div class="alert alert-info alert-custom">
                    <i class="fas fa-lightbulb"></i>
                    <strong>نصيحة:</strong> احفظ هذه الإعدادات في مكان آمن - ستحتاجها في الخطوة التالية
                </div>
            </div>

            <!-- الخطوة 2: تحديث إعدادات Firebase -->
            <div class="step-card">
                <div class="step-number">2</div>
                <h4 class="step-title">تحديث إعدادات Firebase</h4>
                <p>استبدل الإعدادات في جميع ملفات HTML:</p>
                
                <div class="code-block">
const firebaseConfig = {
    apiKey: "YOUR_API_KEY_HERE",
    authDomain: "jorinvforex.firebaseapp.com",
    projectId: "jorinvforex",
    storageBucket: "jorinvforex.appspot.com",
    messagingSenderId: "862703765675",
    appId: "YOUR_APP_ID_HERE"
};
                </div>
                
                <p>الملفات التي تحتاج تحديث:</p>
                <ul>
                    <li>index-new.html</li>
                    <li>login.html</li>
                    <li>register.html</li>
                    <li>dashboard-analyst.html</li>
                    <li>dashboard-subscriber.html</li>
                </ul>
            </div>

            <!-- الخطوة 3: تفعيل خدمات Firebase -->
            <div class="step-card">
                <div class="step-number">3</div>
                <h4 class="step-title">تفعيل خدمات Firebase</h4>
                
                <h6><i class="fas fa-shield-alt"></i> Authentication</h6>
                <p>1. اذهب إلى <a href="https://console.firebase.google.com/u/0/project/jorinvforex/authentication/providers" target="_blank">Authentication</a></p>
                <p>2. فعّل "Email/Password" في Sign-in method</p>
                
                <h6><i class="fas fa-database"></i> Firestore Database</h6>
                <p>1. اذهب إلى <a href="https://console.firebase.google.com/u/0/project/jorinvforex/firestore" target="_blank">Firestore</a></p>
                <p>2. أنشئ قاعدة بيانات في وضع الإنتاج</p>
                <p>3. اختر المنطقة: us-central1 (أو الأقرب لك)</p>
                
                <h6><i class="fas fa-cloud"></i> Storage</h6>
                <p>1. اذهب إلى <a href="https://console.firebase.google.com/u/0/project/jorinvforex/storage" target="_blank">Storage</a></p>
                <p>2. ابدأ في وضع الإنتاج</p>
            </div>

            <!-- الخطوة 4: نشر القواعد -->
            <div class="step-card">
                <div class="step-number">4</div>
                <h4 class="step-title">نشر قواعد الأمان</h4>
                <p>استخدم Firebase CLI لنشر القواعد:</p>
                
                <div class="code-block">
# تسجيل الدخول
firebase login

# ربط المشروع
firebase use jorinvforex

# نشر قواعد Firestore
firebase deploy --only firestore:rules

# نشر قواعد Storage
firebase deploy --only storage

# نشر الفهارس
firebase deploy --only firestore:indexes
                </div>
            </div>

            <!-- الخطوة 5: إنشاء مستخدم أدمن -->
            <div class="step-card">
                <div class="step-number">5</div>
                <h4 class="step-title">إنشاء مستخدم الأدمن الأول</h4>
                <p>بعد تحديث الإعدادات، استخدم هذا النموذج لإنشاء أول مستخدم أدمن:</p>
                
                <form id="adminForm" class="mt-3">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="adminName" value="مدير النظام" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="adminEmail" value="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="adminPassword" placeholder="كلمة مرور قوية" required>
                    </div>
                    <button type="submit" class="btn btn-custom">
                        <i class="fas fa-user-plus"></i> إنشاء مستخدم الأدمن
                    </button>
                </form>
                
                <div id="adminResult" class="mt-3" style="display: none;"></div>
            </div>

            <!-- الخطوة 6: إنشاء بيانات تجريبية -->
            <div class="step-card">
                <div class="step-number">6</div>
                <h4 class="step-title">إنشاء بيانات تجريبية</h4>
                <p>لاختبار النظام، يمكنك إنشاء بيانات تجريبية:</p>
                
                <button id="createSampleBtn" class="btn btn-custom">
                    <i class="fas fa-database"></i> إنشاء بيانات تجريبية
                </button>
                
                <div id="sampleResult" class="mt-3" style="display: none;"></div>
            </div>

            <!-- حالة الإعداد -->
            <div class="step-card">
                <h4 class="step-title"><i class="fas fa-check-circle"></i> حالة الإعداد</h4>
                <div id="setupStatus">
                    <div class="status-indicator status-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        يرجى إكمال الخطوات أعلاه لبدء استخدام المنصة
                    </div>
                </div>
                
                <div class="mt-3">
                    <button id="checkStatusBtn" class="btn btn-custom">
                        <i class="fas fa-sync"></i> فحص حالة الإعداد
                    </button>
                    
                    <a href="index-new.html" class="btn btn-outline-primary ms-2">
                        <i class="fas fa-home"></i> الذهاب للمنصة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="firebase-config.js"></script>
    
    <script>
        // معالج إنشاء مستخدم الأدمن
        document.getElementById('adminForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('adminName').value;
            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('adminPassword').value;
            const resultDiv = document.getElementById('adminResult');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="alert alert-info">جاري إنشاء مستخدم الأدمن...</div>';
            
            try {
                const result = await createFirstAdmin(email, password, name);
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            تم إنشاء مستخدم الأدمن بنجاح! يمكنك الآن تسجيل الدخول.
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            خطأ: ${result.error}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        تأكد من تحديث إعدادات Firebase أولاً
                    </div>
                `;
            }
        });
        
        // معالج إنشاء البيانات التجريبية
        document.getElementById('createSampleBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('sampleResult');
            const btn = document.getElementById('createSampleBtn');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="alert alert-info">جاري إنشاء البيانات التجريبية...</div>';
            
            try {
                const success = await createSampleData();
                
                if (success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            تم إنشاء البيانات التجريبية بنجاح!
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            حدث خطأ في إنشاء البيانات التجريبية
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        تأكد من تحديث إعدادات Firebase أولاً
                    </div>
                `;
            }
            
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-database"></i> إنشاء بيانات تجريبية';
        });
        
        // فحص حالة الإعداد
        document.getElementById('checkStatusBtn').addEventListener('click', () => {
            const status = checkFirebaseStatus();
            const statusDiv = document.getElementById('setupStatus');
            
            let statusHTML = '';
            
            if (status.initialized && status.auth && status.firestore && status.storage) {
                statusHTML = `
                    <div class="status-indicator status-success">
                        <i class="fas fa-check-circle"></i>
                        تم إعداد Firebase بنجاح! المنصة جاهزة للاستخدام
                    </div>
                `;
            } else {
                statusHTML = `
                    <div class="status-indicator status-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        يرجى إكمال إعداد Firebase
                    </div>
                    <div class="mt-2">
                        <small>
                            Firebase: ${status.initialized ? '✅' : '❌'} |
                            Auth: ${status.auth ? '✅' : '❌'} |
                            Firestore: ${status.firestore ? '✅' : '❌'} |
                            Storage: ${status.storage ? '✅' : '❌'}
                        </small>
                    </div>
                `;
            }
            
            statusDiv.innerHTML = statusHTML;
        });
    </script>
</body>
</html>

<?php
/**
 * إعداد قاعدة البيانات - منصة المحللين الماليين
 * jorinvforex Database Setup
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

$message = '';
$error = '';
$step = $_GET['step'] ?? 1;

// معالجة الخطوات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($_POST['action']) {
        case 'test_connection':
            $result = testDatabaseConnection($_POST);
            break;
        case 'create_database':
            $result = createDatabase($_POST);
            break;
        case 'import_schema':
            $result = importDatabaseSchema($_POST);
            break;
        case 'create_admin':
            $result = createAdminUser($_POST);
            break;
    }
    
    if ($result['success']) {
        $message = $result['message'];
        $step = $result['next_step'] ?? $step;
    } else {
        $error = $result['message'];
    }
}

/**
 * اختبار الاتصال بقاعدة البيانات
 */
function testDatabaseConnection($data) {
    try {
        $dsn = "mysql:host={$data['host']};port={$data['port']};charset=utf8mb4";
        $pdo = new PDO($dsn, $data['username'], $data['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        return [
            'success' => true,
            'message' => 'تم الاتصال بقاعدة البيانات بنجاح!',
            'next_step' => 2
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'فشل الاتصال: ' . $e->getMessage()
        ];
    }
}

/**
 * إنشاء قاعدة البيانات
 */
function createDatabase($data) {
    try {
        $dsn = "mysql:host={$data['host']};port={$data['port']};charset=utf8mb4";
        $pdo = new PDO($dsn, $data['username'], $data['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS {$data['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // حفظ إعدادات الاتصال
        saveConnectionConfig($data);
        
        return [
            'success' => true,
            'message' => 'تم إنشاء قاعدة البيانات بنجاح!',
            'next_step' => 3
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'فشل إنشاء قاعدة البيانات: ' . $e->getMessage()
        ];
    }
}

/**
 * استيراد هيكل قاعدة البيانات
 */
function importDatabaseSchema($data) {
    try {
        // قراءة ملف SQL المبسط
        $sqlFile = 'database/simple_database.sql';
        if (!file_exists($sqlFile)) {
            // جرب الملف الأصلي
            $sqlFile = 'database/database.sql';
            if (!file_exists($sqlFile)) {
                return [
                    'success' => false,
                    'message' => 'ملف قاعدة البيانات غير موجود: ' . $sqlFile
                ];
            }
        }
        
        $sql = file_get_contents($sqlFile);
        
        // الاتصال بقاعدة البيانات
        require_once 'config/database.php';
        $pdo = DatabaseConfig::getConnection();
        
        // تنفيذ الاستعلامات مع معالجة أفضل للأخطاء
        $statements = explode(';', $sql);
        $executed = 0;
        $errors = [];

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^--/', $statement) && !preg_match('/^\s*DELIMITER/', $statement)) {
                try {
                    $pdo->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    $errors[] = $e->getMessage();
                }
            }
        }

        // إذا تم تنفيذ معظم الاستعلامات، اعتبر العملية ناجحة
        if ($executed > 10) { // على الأقل 10 استعلامات أساسية
            // تجاهل الأخطاء البسيطة
        }
        
        return [
            'success' => true,
            'message' => 'تم استيراد هيكل قاعدة البيانات بنجاح!',
            'next_step' => 4
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'فشل استيراد قاعدة البيانات: ' . $e->getMessage()
        ];
    }
}

/**
 * إنشاء مستخدم أدمن
 */
function createAdminUser($data) {
    try {
        require_once 'config/database.php';
        require_once 'includes/auth.php';
        
        $adminData = [
            'name' => $data['admin_name'],
            'email' => $data['admin_email'],
            'password' => $data['admin_password'],
            'role' => 'admin'
        ];
        
        $result = getAuth()->register($adminData);
        
        if ($result['success']) {
            return [
                'success' => true,
                'message' => 'تم إنشاء مستخدم الأدمن بنجاح! يمكنك الآن تسجيل الدخول.',
                'next_step' => 5
            ];
        } else {
            return [
                'success' => false,
                'message' => $result['message']
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'فشل إنشاء مستخدم الأدمن: ' . $e->getMessage()
        ];
    }
}

/**
 * حفظ إعدادات الاتصال
 */
function saveConnectionConfig($data) {
    $config = "<?php
/**
 * إعدادات قاعدة البيانات - منصة المحللين الماليين
 * تم إنشاؤها تلقائياً
 */

if (!defined('JORINVFOREX_APP')) {
    die('Access Denied');
}

class DatabaseConfig {
    const DB_HOST = '{$data['host']}';
    const DB_NAME = '{$data['database']}';
    const DB_USER = '{$data['username']}';
    const DB_PASS = '{$data['password']}';
    const DB_CHARSET = 'utf8mb4';
    const DB_PORT = {$data['port']};
    
    // باقي الكود...
}
?>";
    
    file_put_contents('config/database_config.php', $config);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .setup-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .setup-content {
            padding: 2rem;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin: 0 10px;
            position: relative;
        }

        .step.active {
            background: var(--accent-blue);
            color: var(--primary-white);
        }

        .step.completed {
            background: var(--success-color);
            color: var(--primary-white);
        }

        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }

        .step:last-child::after {
            display: none;
        }

        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 1rem;
        }

        .form-control:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 0.2rem rgba(102, 204, 255, 0.25);
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-database"></i> إعداد قاعدة البيانات</h1>
            <p>منصة المحللين الماليين - jorinvforex</p>
        </div>

        <div class="setup-content">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? 'active' : ''; ?> <?php echo $step > 1 ? 'completed' : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? 'active' : ''; ?> <?php echo $step > 2 ? 'completed' : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? 'active' : ''; ?> <?php echo $step > 3 ? 'completed' : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? 'active' : ''; ?> <?php echo $step > 4 ? 'completed' : ''; ?>">4</div>
                <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5</div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <!-- الخطوة 1: اختبار الاتصال -->
                <h3><i class="fas fa-plug"></i> الخطوة 1: اختبار الاتصال بقاعدة البيانات</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="test_connection">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">خادم قاعدة البيانات</label>
                            <input type="text" name="host" class="form-control" value="localhost" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المنفذ</label>
                            <input type="number" name="port" class="form-control" value="3306" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" name="username" class="form-control" value="root" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" name="password" class="form-control">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-custom">
                        <i class="fas fa-check"></i> اختبار الاتصال
                    </button>
                </form>

            <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: إنشاء قاعدة البيانات -->
                <h3><i class="fas fa-database"></i> الخطوة 2: إنشاء قاعدة البيانات</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="create_database">
                    <input type="hidden" name="host" value="<?php echo htmlspecialchars($_POST['host'] ?? 'localhost'); ?>">
                    <input type="hidden" name="port" value="<?php echo htmlspecialchars($_POST['port'] ?? '3306'); ?>">
                    <input type="hidden" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? 'root'); ?>">
                    <input type="hidden" name="password" value="<?php echo htmlspecialchars($_POST['password'] ?? ''); ?>">
                    
                    <label class="form-label">اسم قاعدة البيانات</label>
                    <input type="text" name="database" class="form-control" value="jorinvforex" required>
                    
                    <button type="submit" class="btn btn-custom">
                        <i class="fas fa-plus"></i> إنشاء قاعدة البيانات
                    </button>
                </form>

            <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: استيراد الهيكل -->
                <h3><i class="fas fa-download"></i> الخطوة 3: استيراد هيكل قاعدة البيانات</h3>
                <p>سيتم استيراد الجداول والبيانات الأساسية...</p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="import_schema">
                    
                    <button type="submit" class="btn btn-custom">
                        <i class="fas fa-download"></i> استيراد الهيكل
                    </button>
                </form>

            <?php elseif ($step == 4): ?>
                <!-- الخطوة 4: إنشاء مستخدم أدمن -->
                <h3><i class="fas fa-user-shield"></i> الخطوة 4: إنشاء مستخدم الأدمن</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="create_admin">
                    
                    <label class="form-label">اسم المدير</label>
                    <input type="text" name="admin_name" class="form-control" value="مدير النظام" required>
                    
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" name="admin_email" class="form-control" value="<EMAIL>" required>
                    
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" name="admin_password" class="form-control" placeholder="كلمة مرور قوية" required>
                    
                    <button type="submit" class="btn btn-custom">
                        <i class="fas fa-user-plus"></i> إنشاء مستخدم الأدمن
                    </button>
                </form>

            <?php elseif ($step == 5): ?>
                <!-- الخطوة 5: اكتمال الإعداد -->
                <div class="text-center">
                    <h3><i class="fas fa-check-circle text-success"></i> تم إعداد قاعدة البيانات بنجاح!</h3>
                    <p class="lead">منصة المحللين الماليين جاهزة للاستخدام</p>
                    
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="login-sql.php" class="btn btn-custom">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </a>
                        <a href="index-sql.php" class="btn btn-outline-primary">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

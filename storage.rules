rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // ===== قواعد الصور الشخصية =====
    match /profile-pictures/{userId}/{allPaths=**} {
      // السماح للمستخدم برفع وقراءة صوره الشخصية
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // السماح لجميع المستخدمين بقراءة الصور الشخصية
      allow read: if request.auth != null;
      
      // التحقق من نوع وحجم الملف عند الرفع
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        request.resource.size < 5 * 1024 * 1024 && // أقل من 5 ميجابايت
        request.resource.contentType.matches('image/.*');
    }
    
    // ===== قواعد صور الغلاف =====
    match /cover-pictures/{userId}/{allPaths=**} {
      // السماح للمستخدم برفع وقراءة صور الغلاف الخاصة به
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // السماح لجميع المستخدمين بقراءة صور الغلاف
      allow read: if request.auth != null;
      
      // التحقق من نوع وحجم الملف عند الرفع
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        request.resource.size < 10 * 1024 * 1024 && // أقل من 10 ميجابايت
        request.resource.contentType.matches('image/.*');
    }
    
    // ===== قواعد صور المنشورات =====
    match /post-images/{userId}/{allPaths=**} {
      // السماح للمحلل برفع صور منشوراته
      allow read, write: if request.auth != null && 
        request.auth.uid == userId;
      
      // السماح لجميع المستخدمين بقراءة صور المنشورات
      allow read: if request.auth != null;
      
      // التحقق من نوع وحجم الملف عند الرفع
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        request.resource.size < 15 * 1024 * 1024 && // أقل من 15 ميجابايت
        request.resource.contentType.matches('image/.*');
    }
    
    // ===== قواعد ملفات المحادثات =====
    match /chat-files/{chatId}/{allPaths=**} {
      // استخراج معرفات المحلل والمشترك من معرف المحادثة
      function getAnalystId() {
        return chatId.split('_')[0];
      }
      
      function getSubscriberId() {
        return chatId.split('_')[1];
      }
      
      // السماح للمحلل والمشترك المعنيين فقط
      allow read, write: if request.auth != null && 
        (request.auth.uid == getAnalystId() || request.auth.uid == getSubscriberId());
      
      // التحقق من نوع وحجم الملف عند الرفع
      allow write: if request.auth != null && 
        (request.auth.uid == getAnalystId() || request.auth.uid == getSubscriberId()) &&
        request.resource.size < 20 * 1024 * 1024 && // أقل من 20 ميجابايت
        (request.resource.contentType.matches('image/.*') || 
         request.resource.contentType.matches('application/pdf') ||
         request.resource.contentType.matches('text/.*'));
    }
    
    // ===== قواعد الإعلانات =====
    match /ad-images/{adId}/{allPaths=**} {
      // السماح لجميع المستخدمين بقراءة صور الإعلانات
      allow read: if request.auth != null;
      
      // السماح للأدمن فقط برفع صور الإعلانات
      allow write: if request.auth != null && 
        isAdmin() &&
        request.resource.size < 10 * 1024 * 1024 && // أقل من 10 ميجابايت
        request.resource.contentType.matches('image/.*');
    }
    
    // ===== قواعد ملفات النظام =====
    match /system/{allPaths=**} {
      // السماح للأدمن فقط بالوصول لملفات النظام
      allow read, write: if request.auth != null && isAdmin();
    }
    
    // ===== قواعد النسخ الاحتياطية =====
    match /backups/{allPaths=**} {
      // السماح للأدمن فقط بالوصول للنسخ الاحتياطية
      allow read, write: if request.auth != null && isAdmin();
    }
    
    // ===== قواعد التقارير =====
    match /reports/{userId}/{allPaths=**} {
      // السماح للمستخدم بقراءة تقاريره الخاصة
      allow read: if request.auth != null && request.auth.uid == userId;
      
      // السماح للأدمن بالوصول لجميع التقارير
      allow read, write: if request.auth != null && isAdmin();
    }
    
    // ===== دالة مساعدة للتحقق من دور الأدمن =====
    function isAdmin() {
      return request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // ===== دالة مساعدة للتحقق من دور المحلل =====
    function isAnalyst() {
      return request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'analyst';
    }
    
    // ===== دالة مساعدة للتحقق من دور المشترك =====
    function isSubscriber() {
      return request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'subscriber';
    }
    
    // ===== قواعد عامة - منع الوصول لأي مسارات أخرى =====
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}

// ===== ملاحظات مهمة =====
// 1. تأكد من أن جميع المستخدمين لديهم حقل 'role' في وثيقة المستخدم في Firestore
// 2. قم بتخصيص أحجام الملفات المسموحة حسب احتياجاتك
// 3. أضف أنواع ملفات إضافية حسب الحاجة
// 4. اختبر القواعد جيداً قبل النشر في الإنتاج
// 5. راجع القواعد بانتظام وحدثها حسب الحاجة
// 6. استخدم Firebase Emulator للاختبار المحلي

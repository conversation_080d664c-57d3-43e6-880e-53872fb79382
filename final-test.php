<?php
/**
 * اختبار نهائي شامل - منصة المحللين الماليين
 * jorinvforex Final Comprehensive Test
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// اختبار الاتصال بقاعدة البيانات
$dbTest = testDatabaseConnection();

// اختبار الجداول
$tablesTest = testDatabaseTables();

// اختبار نظام المصادقة
$authTest = testAuthSystem();

// اختبار البيانات التجريبية
$dataTest = testSampleData();

function testDatabaseConnection() {
    try {
        require_once 'config/database.php';
        $connection = DatabaseConfig::getConnection();
        return [
            'status' => 'success',
            'message' => 'الاتصال بقاعدة البيانات ناجح'
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage()
        ];
    }
}

function testDatabaseTables() {
    try {
        require_once 'config/database.php';
        $db = getDB();
        
        $requiredTables = [
            'users', 'posts', 'subscriptions', 'chats', 'messages',
            'notifications', 'ratings', 'daily_stats', 'user_sessions', 'activity_logs'
        ];
        
        $existingTables = [];
        $missingTables = [];
        
        foreach ($requiredTables as $table) {
            try {
                $result = $db->selectOne("SELECT 1 FROM {$table} LIMIT 1");
                $existingTables[] = $table;
            } catch (Exception $e) {
                $missingTables[] = $table;
            }
        }
        
        if (empty($missingTables)) {
            return [
                'status' => 'success',
                'message' => 'جميع الجداول موجودة (' . count($existingTables) . ' جدول)',
                'details' => $existingTables
            ];
        } else {
            return [
                'status' => 'warning',
                'message' => 'بعض الجداول مفقودة: ' . implode(', ', $missingTables),
                'details' => ['existing' => $existingTables, 'missing' => $missingTables]
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => 'خطأ في فحص الجداول: ' . $e->getMessage()
        ];
    }
}

function testAuthSystem() {
    try {
        require_once 'includes/auth.php';
        $auth = getAuth();
        
        // اختبار تسجيل الدخول بحساب تجريبي
        $loginResult = $auth->login('<EMAIL>', 'Admin123456');
        
        if ($loginResult['success']) {
            // تسجيل الخروج فوراً
            $auth->logout();
            return [
                'status' => 'success',
                'message' => 'نظام المصادقة يعمل بشكل صحيح'
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'فشل اختبار نظام المصادقة: ' . $loginResult['message']
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => 'خطأ في اختبار نظام المصادقة: ' . $e->getMessage()
        ];
    }
}

function testSampleData() {
    try {
        require_once 'config/database.php';
        $db = getDB();
        
        // فحص المستخدمين التجريبيين
        $users = $db->select("SELECT email, role FROM users WHERE email IN (?, ?, ?)", [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]);
        
        if (count($users) >= 3) {
            return [
                'status' => 'success',
                'message' => 'البيانات التجريبية موجودة (' . count($users) . ' مستخدمين)',
                'details' => $users
            ];
        } else {
            return [
                'status' => 'warning',
                'message' => 'بعض البيانات التجريبية مفقودة (موجود ' . count($users) . ' من 3)',
                'details' => $users
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'message' => 'خطأ في فحص البيانات التجريبية: ' . $e->getMessage()
        ];
    }
}

// حساب النتيجة الإجمالية
$overallStatus = 'success';
$tests = [$dbTest, $tablesTest, $authTest, $dataTest];
foreach ($tests as $test) {
    if ($test['status'] === 'error') {
        $overallStatus = 'error';
        break;
    } elseif ($test['status'] === 'warning' && $overallStatus !== 'error') {
        $overallStatus = 'warning';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي شامل | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .test-content {
            padding: 2rem;
        }

        .test-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #dee2e6;
        }

        .test-item.success {
            border-left-color: var(--success-color);
            background: rgba(40, 167, 69, 0.05);
        }

        .test-item.warning {
            border-left-color: var(--warning-color);
            background: rgba(255, 193, 7, 0.05);
        }

        .test-item.error {
            border-left-color: var(--danger-color);
            background: rgba(220, 53, 69, 0.05);
        }

        .overall-result {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .overall-result.success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 2px solid var(--success-color);
        }

        .overall-result.warning {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
            border: 2px solid var(--warning-color);
        }

        .overall-result.error {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 2px solid var(--danger-color);
        }

        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .status-icon {
            font-size: 1.5rem;
            margin-left: 10px;
        }

        .details {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-clipboard-check"></i> اختبار نهائي شامل</h1>
            <p>فحص جميع مكونات منصة المحللين الماليين</p>
        </div>

        <div class="test-content">
            <!-- النتيجة الإجمالية -->
            <div class="overall-result <?php echo $overallStatus; ?>">
                <?php if ($overallStatus === 'success'): ?>
                    <i class="fas fa-check-circle"></i>
                    🎉 ممتاز! جميع الاختبارات نجحت - المنصة جاهزة للعمل!
                <?php elseif ($overallStatus === 'warning'): ?>
                    <i class="fas fa-exclamation-triangle"></i>
                    ⚠️ تحذير: بعض المشاكل البسيطة - المنصة تعمل مع تحذيرات
                <?php else: ?>
                    <i class="fas fa-times-circle"></i>
                    ❌ خطأ: يحتاج إصلاح - المنصة غير جاهزة
                <?php endif; ?>
            </div>

            <!-- اختبار قاعدة البيانات -->
            <div class="test-item <?php echo $dbTest['status']; ?>">
                <h4>
                    <?php if ($dbTest['status'] === 'success'): ?>
                        <i class="fas fa-check-circle status-icon text-success"></i>
                    <?php elseif ($dbTest['status'] === 'warning'): ?>
                        <i class="fas fa-exclamation-triangle status-icon text-warning"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle status-icon text-danger"></i>
                    <?php endif; ?>
                    اختبار الاتصال بقاعدة البيانات
                </h4>
                <p><?php echo htmlspecialchars($dbTest['message']); ?></p>
            </div>

            <!-- اختبار الجداول -->
            <div class="test-item <?php echo $tablesTest['status']; ?>">
                <h4>
                    <?php if ($tablesTest['status'] === 'success'): ?>
                        <i class="fas fa-check-circle status-icon text-success"></i>
                    <?php elseif ($tablesTest['status'] === 'warning'): ?>
                        <i class="fas fa-exclamation-triangle status-icon text-warning"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle status-icon text-danger"></i>
                    <?php endif; ?>
                    اختبار جداول قاعدة البيانات
                </h4>
                <p><?php echo htmlspecialchars($tablesTest['message']); ?></p>
                <?php if (isset($tablesTest['details'])): ?>
                    <div class="details">
                        <?php if (isset($tablesTest['details']['existing'])): ?>
                            <strong>الجداول الموجودة:</strong> <?php echo implode(', ', $tablesTest['details']['existing']); ?>
                        <?php else: ?>
                            <strong>الجداول:</strong> <?php echo implode(', ', $tablesTest['details']); ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- اختبار نظام المصادقة -->
            <div class="test-item <?php echo $authTest['status']; ?>">
                <h4>
                    <?php if ($authTest['status'] === 'success'): ?>
                        <i class="fas fa-check-circle status-icon text-success"></i>
                    <?php elseif ($authTest['status'] === 'warning'): ?>
                        <i class="fas fa-exclamation-triangle status-icon text-warning"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle status-icon text-danger"></i>
                    <?php endif; ?>
                    اختبار نظام المصادقة
                </h4>
                <p><?php echo htmlspecialchars($authTest['message']); ?></p>
            </div>

            <!-- اختبار البيانات التجريبية -->
            <div class="test-item <?php echo $dataTest['status']; ?>">
                <h4>
                    <?php if ($dataTest['status'] === 'success'): ?>
                        <i class="fas fa-check-circle status-icon text-success"></i>
                    <?php elseif ($dataTest['status'] === 'warning'): ?>
                        <i class="fas fa-exclamation-triangle status-icon text-warning"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle status-icon text-danger"></i>
                    <?php endif; ?>
                    اختبار البيانات التجريبية
                </h4>
                <p><?php echo htmlspecialchars($dataTest['message']); ?></p>
                <?php if (isset($dataTest['details'])): ?>
                    <div class="details">
                        <strong>المستخدمين التجريبيين:</strong><br>
                        <?php foreach ($dataTest['details'] as $user): ?>
                            - <?php echo htmlspecialchars($user['email']); ?> (<?php echo htmlspecialchars($user['role']); ?>)<br>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <?php if ($overallStatus === 'success'): ?>
                    <a href="index-sql.php" class="btn-custom">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                    <a href="login-sql.php" class="btn-custom">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                <?php else: ?>
                    <a href="setup-database-new.php" class="btn-custom">
                        <i class="fas fa-tools"></i> إعادة إعداد قاعدة البيانات
                    </a>
                <?php endif; ?>
                
                <a href="system-check.php" class="btn-custom">
                    <i class="fas fa-stethoscope"></i> فحص النظام
                </a>
                
                <button onclick="location.reload()" class="btn-custom">
                    <i class="fas fa-sync"></i> إعادة الاختبار
                </button>
            </div>

            <!-- معلومات إضافية -->
            <?php if ($overallStatus === 'success'): ?>
                <div class="alert alert-success mt-4">
                    <h6><i class="fas fa-info-circle"></i> المنصة جاهزة للاستخدام!</h6>
                    <ul class="mb-0">
                        <li>جميع الاختبارات نجحت</li>
                        <li>قاعدة البيانات تعمل بشكل صحيح</li>
                        <li>نظام المصادقة آمن وفعال</li>
                        <li>البيانات التجريبية متاحة للاختبار</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

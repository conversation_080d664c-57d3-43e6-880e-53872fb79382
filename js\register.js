// ===== صفحة التسجيل =====

document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة التسجيل');
    
    // التحقق من حالة المصادقة
    auth.onAuthStateChanged((user) => {
        if (user) {
            console.log('المستخدم مسجل الدخول بالفعل');
            window.location.href = 'index-new.html';
        }
    });
    
    // إعداد نموذج التسجيل
    setupRegisterForm();
    
    // إعداد اختيار نوع الحساب
    setupRoleSelection();
    
    // التحقق من نوع الحساب من URL
    checkURLParams();
});

// ===== إعداد اختيار نوع الحساب =====
function setupRoleSelection() {
    const roleOptions = document.querySelectorAll('.role-option');
    const analystFields = document.getElementById('analystFields');
    
    roleOptions.forEach(option => {
        option.addEventListener('click', function() {
            // إزالة التحديد من جميع الخيارات
            roleOptions.forEach(opt => opt.classList.remove('selected'));
            
            // تحديد الخيار المختار
            this.classList.add('selected');
            
            // تحديد الراديو بوتن
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // إظهار/إخفاء حقول المحلل
            const role = this.dataset.role;
            if (role === 'analyst') {
                analystFields.style.display = 'block';
            } else {
                analystFields.style.display = 'none';
            }
        });
    });
}

// ===== التحقق من معاملات URL =====
function checkURLParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    
    if (type === 'analyst' || type === 'subscriber') {
        const roleOption = document.querySelector(`[data-role="${type}"]`);
        if (roleOption) {
            roleOption.click();
        }
    }
}

// ===== إعداد نموذج التسجيل =====
function setupRegisterForm() {
    const registerForm = document.getElementById('registerForm');
    const fullNameInput = document.getElementById('fullName');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const schoolInput = document.getElementById('school');
    const descriptionInput = document.getElementById('description');

    // إضافة مستمع للنموذج
    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = {
            fullName: fullNameInput.value.trim(),
            email: emailInput.value.trim(),
            password: passwordInput.value,
            confirmPassword: confirmPasswordInput.value,
            role: document.querySelector('input[name="userRole"]:checked')?.value,
            school: schoolInput.value.trim(),
            description: descriptionInput.value.trim()
        };
        
        // التحقق من صحة البيانات
        if (!validateRegisterData(formData)) {
            return;
        }
        
        // إظهار حالة التحميل
        showRegisterLoading(true);
        hideMessages();
        
        try {
            // إنشاء حساب جديد
            const userCredential = await auth.createUserWithEmailAndPassword(formData.email, formData.password);
            const user = userCredential.user;
            
            console.log('تم إنشاء الحساب بنجاح:', user.email);
            
            // تحديث ملف المستخدم
            await user.updateProfile({
                displayName: formData.fullName
            });
            
            // حفظ بيانات المستخدم في Firestore
            await saveUserData(user.uid, formData);
            
            // إظهار رسالة نجاح
            showSuccess('تم إنشاء الحساب بنجاح! جاري توجيهك...');
            
            // توجيه المستخدم إلى لوحة التحكم
            setTimeout(() => {
                redirectToDashboard(formData.role);
            }, 2000);
            
        } catch (error) {
            console.error('خطأ في إنشاء الحساب:', error);
            showError(getErrorMessage(error.code));
        } finally {
            showRegisterLoading(false);
        }
    });

    // تحسين تجربة المستخدم
    const inputs = [fullNameInput, emailInput, passwordInput, confirmPasswordInput];
    inputs.forEach(input => {
        input.addEventListener('input', hideMessages);
    });
    
    // التركيز على حقل الاسم
    fullNameInput.focus();
}

// ===== التحقق من صحة بيانات التسجيل =====
function validateRegisterData(data) {
    // التحقق من الاسم
    if (!data.fullName) {
        showError('يرجى إدخال الاسم الكامل');
        document.getElementById('fullName').focus();
        return false;
    }
    
    if (data.fullName.length < 2) {
        showError('الاسم يجب أن يكون حرفين على الأقل');
        document.getElementById('fullName').focus();
        return false;
    }
    
    // التحقق من البريد الإلكتروني
    if (!data.email) {
        showError('يرجى إدخال البريد الإلكتروني');
        document.getElementById('email').focus();
        return false;
    }
    
    if (!isValidEmail(data.email)) {
        showError('يرجى إدخال بريد إلكتروني صحيح');
        document.getElementById('email').focus();
        return false;
    }
    
    // التحقق من كلمة المرور
    if (!data.password) {
        showError('يرجى إدخال كلمة المرور');
        document.getElementById('password').focus();
        return false;
    }
    
    if (data.password.length < 6) {
        showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        document.getElementById('password').focus();
        return false;
    }
    
    // التحقق من تأكيد كلمة المرور
    if (data.password !== data.confirmPassword) {
        showError('كلمة المرور وتأكيدها غير متطابقتين');
        document.getElementById('confirmPassword').focus();
        return false;
    }
    
    // التحقق من نوع الحساب
    if (!data.role) {
        showError('يرجى اختيار نوع الحساب');
        return false;
    }
    
    // التحقق من حقول المحلل (اختيارية)
    if (data.role === 'analyst') {
        if (data.description && data.description.length > 500) {
            showError('الوصف يجب أن يكون أقل من 500 حرف');
            document.getElementById('description').focus();
            return false;
        }
    }
    
    return true;
}

// ===== حفظ بيانات المستخدم في Firestore =====
async function saveUserData(userId, formData) {
    const userData = {
        name: formData.fullName,
        email: formData.email,
        role: formData.role,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        profilePicURL: '',
        coverPicURL: '',
        themeColor: '#66CCFF' // اللون الافتراضي
    };
    
    // إضافة حقول المحلل إذا كان الدور محلل
    if (formData.role === 'analyst') {
        userData.school = formData.school || '';
        userData.description = formData.description || '';
        userData.acceptedSubscribers = [];
    }
    
    // حفظ البيانات في Firestore
    await db.collection('users').doc(userId).set(userData);
    
    console.log('تم حفظ بيانات المستخدم في Firestore');
}

// ===== التحقق من صحة البريد الإلكتروني =====
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// ===== توجيه المستخدم إلى لوحة التحكم =====
function redirectToDashboard(role) {
    switch (role) {
        case 'analyst':
            window.location.href = 'dashboard-analyst.html';
            break;
        case 'subscriber':
            window.location.href = 'dashboard-subscriber.html';
            break;
        default:
            window.location.href = 'index-new.html';
    }
}

// ===== إظهار/إخفاء حالة التحميل =====
function showRegisterLoading(show) {
    const registerBtn = document.getElementById('registerBtn');
    const registerBtnText = document.getElementById('registerBtnText');
    const registerSpinner = document.getElementById('registerSpinner');
    
    if (show) {
        registerBtn.disabled = true;
        registerBtnText.textContent = 'جاري إنشاء الحساب...';
        registerSpinner.style.display = 'inline-block';
    } else {
        registerBtn.disabled = false;
        registerBtnText.textContent = 'إنشاء الحساب';
        registerSpinner.style.display = 'none';
    }
}

// ===== إظهار رسالة الخطأ =====
function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    
    // التمرير إلى رسالة الخطأ
    errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// ===== إظهار رسالة النجاح =====
function showSuccess(message) {
    const successMessage = document.getElementById('successMessage');
    successMessage.textContent = message;
    successMessage.style.display = 'block';
    
    // التمرير إلى رسالة النجاح
    successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// ===== إخفاء الرسائل =====
function hideMessages() {
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('successMessage').style.display = 'none';
}

// ===== الحصول على رسالة الخطأ المناسبة =====
function getErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/email-already-in-use':
            return 'هذا البريد الإلكتروني مستخدم بالفعل';
        case 'auth/invalid-email':
            return 'البريد الإلكتروني غير صحيح';
        case 'auth/weak-password':
            return 'كلمة المرور ضعيفة جداً';
        case 'auth/network-request-failed':
            return 'خطأ في الاتصال بالإنترنت';
        default:
            return 'حدث خطأ في إنشاء الحساب. يرجى المحاولة مرة أخرى';
    }
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطة تداول فوركس</title>
    <!-- تحسين تحميل CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" crossorigin="anonymous">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet" crossorigin="anonymous">
    <!-- تأجيل تحميل مكتبات PDF -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js" as="script">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js" as="script">
    <style>
        :root {
            --primary-bg: #0a192f;
            --accent-color: #64ffda;
            --main-text: #fff;
            --secondary-text: #ffffff;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            background-color: var(--primary-bg);
            color: var(--main-text);
            font-family: 'Cairo', sans-serif;
            padding-top: 80px;
        }

        /* تحسين التنقل السلس */
        a[href^="#"] {
            scroll-behavior: smooth;
        }

        .card {
            background-color: rgba(10, 25, 47, 0.7);
            border: 1px solid rgba(100, 255, 218, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .card:hover {
            border-color: rgba(100, 255, 218, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
        }

        .card-header {
            background-color: rgba(100, 255, 218, 0.1);
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
            color: var(--accent-color);
        }

        .form-control,
        .form-select {
            background-color: rgba(10, 25, 47, 0.5);
            border: 1px solid rgba(100, 255, 218, 0.2);
            color: var(--main-text);
            transition: all 0.3s ease;
        }

        .form-control:hover,
        .form-select:hover {
            border-color: rgba(100, 255, 218, 0.4);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.1);
        }

        .form-control:focus,
        .form-select:focus {
            background-color: rgba(10, 25, 47, 0.7);
            border-color: var(--accent-color);
            color: var(--main-text);
            box-shadow: 0 0 0 0.25rem rgba(100, 255, 218, 0.25);
        }

        .btn-primary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: var(--primary-bg);
            font-weight: 500;
        }

        .btn-primary:hover {
            background-color: transparent;
            color: var(--accent-color);
            border-color: var(--accent-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.3);
        }

        .btn-outline-primary {
            color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--accent-color);
            color: var(--primary-bg);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.3);
        }

        table {
            color: var(--main-text);
        }

        .table {
            --bs-table-bg: transparent;
            --bs-table-striped-bg: rgba(255, 255, 255, 0.05);
            --bs-table-hover-bg: rgba(100, 255, 218, 0.1);
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background-color: rgba(100, 255, 218, 0.1) !important;
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.2);
        }

        .table th {
            color: var(--accent-color);
            border-color: rgba(100, 255, 218, 0.2);
        }

        .table td {
            border-color: rgba(100, 255, 218, 0.1);
        }

        .nav-link {
            color: var(--secondary-text) !important;
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 12px 18px !important;
            border-radius: 8px;
            margin: 0 5px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            transition: all 0.4s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 85%;
        }

        .nav-link:hover {
            color: var(--accent-color) !important;
            transform: translateY(-3px);
            background: rgba(100, 255, 218, 0.1);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.2);
        }

        .nav-link.active {
            color: var(--accent-color) !important;
        }

        /* تصميم الهيدر الموحد */
        .navbar {
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 900;
            color: #64ffda !important;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 10px rgba(100, 255, 218, 0.5));
        }

        /* تصميم القائمة المنسدلة للأدوات */
        .dropdown-menu {
            border: none !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6) !important;
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px) !important;
            border-radius: 10px !important;
            border: 1px solid rgba(100, 255, 218, 0.3) !important;
        }

        .dropdown-item {
            border: none !important;
            background: transparent !important;
            color: #ccd6f6 !important;
            transition: all 0.3s ease !important;
            border-radius: 6px !important;
            margin: 2px 8px !important;
        }

        .dropdown-item:hover {
            background: rgba(100, 255, 218, 0.1) !important;
            color: #64ffda !important;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.1) !important;
        }

        .dropdown-item:hover i {
            color: #64ffda !important;
            transform: scale(1.1);
            filter: drop-shadow(0 0 5px rgba(100, 255, 218, 0.5));
        }

        .dropdown-toggle::after {
            border-top: 4px solid;
            border-right: 3px solid transparent;
            border-left: 3px solid transparent;
            margin-right: 3px;
        }

        /* تأثيرات انتقالية للقائمة المنسدلة */
        .dropdown-menu {
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            display: block !important;
            visibility: hidden;
        }

        .dropdown:hover .dropdown-menu,
        .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* تحسينات للهواتف - الهيدر */
        @media (max-width: 768px) {
            .dropdown-menu {
                position: static !important;
                transform: none !important;
                opacity: 1 !important;
                visibility: visible !important;
                background: rgba(10, 25, 47, 0.98) !important;
                border-radius: 8px !important;
                margin: 5px 0 !important;
            }

            .dropdown-item {
                padding: 12px 20px !important;
                font-size: 0.9rem !important;
            }
        }

        .navbar-toggler {
            border-color: var(--accent-color) !important;
            color: var(--accent-color) !important;
        }

        .profit {
            color: #4CAF50;
        }

        .loss {
            color: #F44336;
        }

        .result-container {
            display: none;
        }

        .footer {
            background-color: rgba(10, 25, 47, 0.9);
            border-top: 1px solid rgba(100, 255, 218, 0.1);
            padding: 20px 0;
            margin-top: 50px;
        }

        /* تحسينات للطباعة */
        @media print {
            body * {
                visibility: hidden;
            }

            .result-container,
            .result-container * {
                visibility: visible;
            }

            .result-container {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                background-color: white;
                color: black;
            }

            .no-print {
                display: none !important;
            }
        }

        /* تحسينات جديدة */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 25, 47, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            display: none;
        }

        .spinner {
            width: 70px;
            height: 70px;
            border: 8px solid rgba(100, 255, 218, 0.3);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .chart-container {
            height: 300px;
            margin-bottom: 30px;
        }

        .saved-plans {
            max-height: 300px;
            overflow-y: auto;
        }

        .plan-item {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .plan-item:hover {
            background-color: rgba(100, 255, 218, 0.1) !important;
        }

        .advanced-options {
            overflow: hidden;
            max-height: 0;
            transition: max-height 0.5s ease-out;
        }

        .advanced-options.show {
            max-height: 500px;
        }

        .tooltip-inner {
            background-color: var(--primary-bg);
            border: 1px solid var(--accent-color);
        }

        .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before,
        .bs-tooltip-top .tooltip-arrow::before {
            border-top-color: var(--accent-color);
        }

        .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before,
        .bs-tooltip-right .tooltip-arrow::before {
            border-right-color: var(--accent-color);
        }

        .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before,
        .bs-tooltip-bottom .tooltip-arrow::before {
            border-bottom-color: var(--accent-color);
        }

        .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before,
        .bs-tooltip-left .tooltip-arrow::before {
            border-left-color: var(--accent-color);
        }
    </style>
</head>

<body>
    <!-- شاشة التحميل -->
    <div class="loading-overlay">
        <div class="spinner"></div>
    </div>

    <!-- الهيدر الموحد -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                فوركس الأردن
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                style="border: 1px solid rgba(100, 255, 218, 0.3); padding: 8px 12px;">
                <i class="fas fa-bars" style="color: #64ffda; font-size: 1.2rem;"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" style="font-weight: 500;">
                    <li class="nav-item">
                        <a class="nav-link" href="../#home"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#about"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">من
                            نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#services"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            خدماتنا</a>
                    </li>

                    <!-- قائمة الأدوات المنسدلة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                            style="color: #64ffda; margin: 0 10px; position: relative; padding: 8px 15px !important; display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-tools" style="font-size: 0.9rem;"></i>
                            الأدوات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                            <li>
                                <a class="dropdown-item" href="tools/Risk-management.html"
                                    style="color: #64ffda; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px; background: rgba(100, 255, 218, 0.1);">
                                    <i class="fas fa-chart-pie" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    خطط إدارة رأس المال
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Lotcalculator.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calculator" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    حاسبة اللوت
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Economic-news.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calendar-alt" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    التقويم الاقتصادي
                                </a>
                            </li>

                            
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="../#plans"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">باقات
                            الاشتراك</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#partners"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الشركاء</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#contact"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            تواصل معنا
                        </a>
                    </li>
                </ul>
            </div>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-brain"></i> حاسبة إدارة رأس المال الذكية</h4>
                        <p class="mb-0 mt-2" style="color: #ccd6f6; font-size: 0.9rem;">
                            <i class="fas fa-info-circle"></i>
                            نظام ذكي لحساب خطط إدارة رأس المال مع نمو تدريجي واقعي للوت ونسبة مخاطرة ديناميكية
                        </p>
                    </div>
                    <div class="card-body">
                        <form id="capitalForm">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="capital" class="form-label" style="color: var(--accent-color); font-weight: 600;">
                                        <i class="fas fa-dollar-sign"></i> رأس المال الكلي ($)
                                    </label>
                                    <input type="number" class="form-control" id="capital" required min="50" step="0.01" placeholder="أدخل رأس المال" value="1000">
                                    <small class="text-muted">الحد الأدنى: $50</small>
                                    <div id="capitalWarning" class="mt-2" style="display: none;"></div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="riskLevel" class="form-label" style="color: #ffffff;">
                                        <i class="fas fa-shield-alt"></i> مستوى المخاطرة
                                    </label>
                                    <select class="form-select" id="riskLevel" required>
                                        <option value="conservative">محافظ (1-3%)</option>
                                        <option value="moderate" selected>متوسط (4-8%)</option>
                                        <option value="aggressive">عالي (8-25%)</option>
                                        <option value="margin">هامشي (26-55%)</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="days" class="form-label" style="color: #ffffff;">
                                        <i class="fas fa-calendar-alt"></i> مدة الخطة (أيام)
                                    </label>
                                    <input type="number" class="form-control" id="days" required min="1" max="22" placeholder="1-22 يوم" value="10">
                                    <small class="text-muted">الحد الأقصى: 22 يوم</small>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="pipsPerTrade" class="form-label" style="color: #ffffff;">
                                        <i class="fas fa-chart-line"></i> هدف النقاط لكل صفقة
                                    </label>
                                    <input type="number" class="form-control" id="pipsPerTrade" required min="5" max="100" step="1" placeholder="5-100 نقطة" value="20">
                                    <small class="text-muted">النقاط المستهدفة يومياً</small>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" style="color: #ffffff;">
                                        <i class="fas fa-info-circle"></i> نسبة المخاطرة التلقائية
                                    </label>
                                    <div class="form-control" id="autoRiskDisplay" style="background: rgba(100, 255, 218, 0.1); color: #64ffda; font-weight: bold;">
                                        سيتم حسابها تلقائياً
                                    </div>
                                    <small class="text-muted">تحسب تلقائياً حسب مستوى المخاطرة</small>
                                </div>
                                <div class="col-md-6 mb-3 d-flex align-items-end">
                                    <button type="button" id="toggleAdvanced" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-cog"></i> خيارات متقدمة
                                    </button>
                                </div>
                            </div>
                            
                            <!-- الخيارات المتقدمة -->
                            <div class="advanced-options" id="advancedOptions">
                                <div class="row mt-3">
                                    <div class="col-md-4 mb-3">
                                        <label for="initialLot" class="form-label" style="color: #ffffff;">
                                            <i class="fas fa-arrows-alt-v"></i> حجم اللوت الأولي
                                        </label>
                                        <input type="number" class="form-control" id="initialLot" min="0.01" max="2" step="0.01" placeholder="سيتم حسابه تلقائياً" disabled>
                                        <small class="text-muted">سيتم حسابها حسب رأس المال</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="lotIncrement" class="form-label" style="color: #ffffff;">
                                            <i class="fas fa-plus-circle"></i> زيادة اللوت اليومية
                                        </label>
                                        <select class="form-select" id="lotIncrement">
                                            <option value="0.01">بطيء (0.01 يومياً)</option>
                                            <option value="0.05" selected>متوسط (0.05 يومياً)</option>
                                            <option value="0.10">سريع (0.10 يومياً)</option>
                                            <option value="custom">مخصص</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3" id="customIncrementContainer" style="display: none;">
                                        <label for="customIncrement" class="form-label" style="color: #ffffff;">
                                            <i class="fas fa-edit"></i> قيمة الزيادة المخصصة
                                        </label>
                                        <input type="number" class="form-control" id="customIncrement" min="0.01" max="0.5" step="0.01" placeholder="أدخل قيمة الزيادة">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="maxRiskReduction" class="form-label" style="color: #ffffff;">
                                            <i class="fas fa-arrow-down"></i> الحد الأقصى لتقليل المخاطرة
                                        </label>
                                        <select class="form-select" id="maxRiskReduction">
                                            <option value="0.3">30% من المخاطرة الأولية</option>
                                            <option value="0.5" selected>50% من المخاطرة الأولية</option>
                                            <option value="0.7">70% من المخاطرة الأولية</option>
                                            <option value="1">لا يوجد حد</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="maxLotSize" class="form-label" style="color: #ffffff;">
                                            <i class="fas fa-lock"></i> الحد الأقصى للوت
                                        </label>
                                        <select class="form-select" id="maxLotSize">
                                            <option value="0.5">0.50 لوت</option>
                                            <option value="1.0">1.00 لوت</option>
                                            <option value="2.0" selected>2.00 لوت</option>
                                            <option value="5.0">5.00 لوت</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="button" id="savePlanBtn" class="btn btn-outline-info me-md-2">
                                    <i class="fas fa-save"></i> حفظ الخطة
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-calculator"></i> إنشاء خطة إدارة رأس المال الذكية
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card result-container" id="resultContainer">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><i class="fas fa-chart-line"></i> خطة إدارة رأس المال الواقعية</h4>
                            <small style="color: #ccd6f6;">الربح محسوب بناءً على: اللوت × النقاط × قيمة النقطة ($10 للوت الكامل)</small>
                        </div>
                        <div>
                            <button id="printPlan" class="btn btn-outline-primary btn-sm me-2">
                                <i class="fas fa-print me-1"></i> طباعة
                            </button>
                            <button id="exportPdf" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-file-pdf me-1"></i> تصدير إلى PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Summary -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div style="background: rgba(100, 255, 218, 0.1); padding: 15px; border-radius: 10px; border: 1px solid rgba(100, 255, 218, 0.3);">
                                    <strong style="color: var(--accent-color);">رأس المال الأولي:</strong> <span id="initialCapital" style="color: #ffffff;">$0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="background: rgba(100, 255, 218, 0.1); padding: 15px; border-radius: 10px; border: 1px solid rgba(100, 255, 218, 0.3);">
                                    <strong style="color: var(--accent-color);">رأس المال النهائي:</strong> <span id="finalCapital" style="color: #ffffff;">$0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="background: rgba(100, 255, 218, 0.1); padding: 15px; border-radius: 10px; border: 1px solid rgba(100, 255, 218, 0.3);">
                                    <strong style="color: var(--accent-color);">إجمالي الربح:</strong> <span id="totalProfit" style="color: #ffffff;">$0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="background: rgba(100, 255, 218, 0.1); padding: 15px; border-radius: 10px; border: 1px solid rgba(100, 255, 218, 0.3);">
                                    <strong style="color: var(--accent-color);">نسبة النمو:</strong> <span id="growthPercentage" style="color: #ffffff;">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- الرسوم البيانية -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="balanceChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="lotSizeChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="capitalTable">
                                <thead>
                                    <tr>
                                        <th style="color: var(--accent-color);">اليوم</th>
                                        <th style="color: var(--accent-color);">الرصيد الأولي ($)</th>
                                        <th style="color: var(--accent-color);">نسبة المخاطرة (%)</th>
                                        <th style="color: var(--accent-color);">هدف الربح ($)</th>
                                        <th style="color: var(--accent-color);">حجم اللوت</th>
                                        <th style="color: var(--accent-color);">النقاط</th>
                                        <th style="color: var(--accent-color);">الرصيد النهائي ($)</th>
                                    </tr>
                                </thead>
                                <tbody id="capitalTableBody">
                                    <!-- سيتم ملؤها بالجافاسكريبت -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Export to Excel Button -->
                        <div class="mt-3 d-flex justify-content-between">
                            <button id="exportExcel" class="btn btn-success">
                                <i class="fas fa-file-excel me-1"></i> تصدير إلى Excel
                            </button>
                            <button id="showSavedPlans" class="btn btn-info">
                                <i class="fas fa-history me-1"></i> عرض الخطط المحفوظة
                            </button>
                        </div>

                        <!-- قائمة الخطط المحفوظة -->
                        <div class="mt-4" id="savedPlansContainer" style="display: none;">
                            <h5 style="color: var(--accent-color); font-weight: 600;"><i class="fas fa-history"></i> الخطط المحفوظة</h5>
                            <div class="card saved-plans">
                                <div class="card-body" id="savedPlansList">
                                    <!-- سيتم ملؤها بالجافاسكريبت -->
                                </div>
                            </div>
                        </div>

                        <!-- Tips Section -->
                        <div class="mt-4">
                            <h5 style="color: var(--accent-color); font-weight: 600;"><i class="fas fa-lightbulb"></i> نصائح إدارة رأس المال الذكية</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div style="background: rgba(76, 175, 80, 0.1); padding: 20px; border-radius: 10px; border: 1px solid rgba(76, 175, 80, 0.3); margin-bottom: 15px;">
                                        <h6 style="color: #4CAF50;"><i class="fas fa-shield-alt"></i> قواعد الحماية الذكية:</h6>
                                        <ul class="mb-0" style="color: #ffffff; font-size: 0.9rem;">
                                            <li>اللوت يزيد تدريجياً (0.05 يومياً)</li>
                                            <li>المخاطرة تقل مع نمو رأس المال</li>
                                            <li>حد أقصى 2.00 لوت للحماية</li>
                                            <li>خطة محدودة بـ 22 يوم للواقعية</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div style="background: rgba(33, 150, 243, 0.1); padding: 20px; border-radius: 10px; border: 1px solid rgba(33, 150, 243, 0.3); margin-bottom: 15px;">
                                        <h6 style="color: #2196F3;"><i class="fas fa-brain"></i> حساب الربح الواقعي:</h6>
                                        <ul class="mb-0" style="color: #ffffff; font-size: 0.9rem;">
                                            <li>0.01 لوت = $0.10 للنقطة</li>
                                            <li>0.1 لوت = $1.00 للنقطة</li>
                                            <li>1.0 لوت = $10.00 للنقطة</li>
                                            <li>الربح = اللوت × النقاط × $10</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div style="background: rgba(255, 152, 0, 0.1); padding: 20px; border-radius: 10px; border: 1px solid rgba(255, 152, 0, 0.3); margin-bottom: 15px;">
                                        <h6 style="color: #FF9800;"><i class="fas fa-exclamation-triangle"></i> تحذيرات مهمة:</h6>
                                        <ul class="mb-0" style="color: #ffffff; font-size: 0.9rem;">
                                            <li>النتائج تفترض تحقيق الهدف يومياً</li>
                                            <li>في الواقع قد تحدث خسائر</li>
                                            <li>استخدم وقف الخسارة دائماً</li>
                                            <li>لا تتداول بكامل رأس المال</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- مثال توضيحي للحساب -->
                            <div class="mt-3">
                                <h6 style="color: var(--accent-color);"><i class="fas fa-calculator"></i> مثال على الحساب الصحيح:</h6>
                                <div class="alert" style="background: rgba(100, 255, 218, 0.1); border: 1px solid rgba(100, 255, 218, 0.3); color: #ffffff;">
                                    <strong>مثال:</strong> لوت 0.10 × 75 نقطة = 0.10 × 75 × $10 = <span style="color: #4CAF50; font-weight: bold;">$75 ربح</span>
                                    <br>
                                    <small>قيمة النقطة للوت 0.10 = $1.00، لذلك 75 نقطة = 75 × $1.00 = $75</small>
                                </div>
                            </div>

                            <!-- إضافة معلومات عن مستويات المخاطرة -->
                            <div class="mt-3">
                                <h6 style="color: var(--accent-color);"><i class="fas fa-info-circle"></i> شرح مستويات المخاطرة:</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div style="background: rgba(76, 175, 80, 0.05); padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                                            <strong style="color: #4CAF50;">محافظ (1-3%)</strong>
                                            <p style="color: #ffffff; font-size: 0.85rem; margin: 5px 0 0 0;">للمبتدئين والحسابات الصغيرة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div style="background: rgba(33, 150, 243, 0.05); padding: 15px; border-radius: 8px; border-left: 4px solid #2196F3;">
                                            <strong style="color: #2196F3;">متوسط (4-8%)</strong>
                                            <p style="color: #ffffff; font-size: 0.85rem; margin: 5px 0 0 0;">للمتداولين ذوي الخبرة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div style="background: rgba(255, 152, 0, 0.05); padding: 15px; border-radius: 8px; border-left: 4px solid #FF9800;">
                                            <strong style="color: #FF9800;">عالي (8-25%)</strong>
                                            <p style="color: #ffffff; font-size: 0.85rem; margin: 5px 0 0 0;">للخبراء والحسابات الكبيرة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div style="background: rgba(244, 67, 54, 0.05); padding: 15px; border-radius: 8px; border-left: 4px solid #F44336;">
                                            <strong style="color: #F44336;">هامشي (26-55%)</strong>
                                            <p style="color: #ffffff; font-size: 0.85rem; margin: 5px 0 0 0;">مخاطرة عالية جداً</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container text-center">
            <p class="mb-0" style="color: var(--secondary-text);">© 2023 فوركس الأردن. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- تحميل مكتبات JS بشكل متأخر -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // تأجيل تحميل مكتبات PDF حتى الحاجة إليها
        function loadPDFLibrary(callback) {
            if (window.jspdf) {
                callback();
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = function() {
                const script2 = document.createElement('script');
                script2.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js';
                script2.onload = callback;
                document.body.appendChild(script2);
            };
            document.body.appendChild(script);
        }

        // JavaScript لحاسبة إدارة رأس المال
        document.addEventListener('DOMContentLoaded', function () {
            // العناصر الرئيسية في الصفحة
            const form = document.getElementById('capitalForm');
            const resultContainer = document.getElementById('resultContainer');
            const tableBody = document.getElementById('capitalTableBody');
            const exportPdfBtn = document.getElementById('exportPdf');
            const printPlanBtn = document.getElementById('printPlan');
            const exportExcelBtn = document.getElementById('exportExcel');
            const riskLevelSelect = document.getElementById('riskLevel');
            const autoRiskDisplay = document.getElementById('autoRiskDisplay');
            const toggleAdvancedBtn = document.getElementById('toggleAdvanced');
            const advancedOptions = document.getElementById('advancedOptions');
            const lotIncrementSelect = document.getElementById('lotIncrement');
            const customIncrementContainer = document.getElementById('customIncrementContainer');
            const savePlanBtn = document.getElementById('savePlanBtn');
            const showSavedPlansBtn = document.getElementById('showSavedPlans');
            const savedPlansContainer = document.getElementById('savedPlansContainer');
            const savedPlansList = document.getElementById('savedPlansList');
            const loadingOverlay = document.querySelector('.loading-overlay');
            
            // الرسوم البيانية
            let balanceChart, lotSizeChart;
            
            // إظهار/إخفاء الخيارات المتقدمة
            toggleAdvancedBtn.addEventListener('click', function() {
                advancedOptions.classList.toggle('show');
                this.innerHTML = advancedOptions.classList.contains('show') ? 
                    '<i class="fas fa-cog"></i> إخفاء الخيارات المتقدمة' : 
                    '<i class="fas fa-cog"></i> خيارات متقدمة';
            });
            
            // التحكم في خيارات زيادة اللوت
            lotIncrementSelect.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customIncrementContainer.style.display = 'block';
                } else {
                    customIncrementContainer.style.display = 'none';
                }
            });
            
            // إضافة مستمع للنموذج
            form.addEventListener('submit', function (e) {
                e.preventDefault();
                showLoading();
                setTimeout(() => {
                    calculateCapitalPlan();
                    hideLoading();
                }, 500);
            });
            
            // تحديث عرض نسبة المخاطرة عند تغيير أي قيمة
            riskLevelSelect.addEventListener('change', updateRiskDisplay);
            document.getElementById('capital').addEventListener('input', function() {
                updateRiskDisplay();
                showCapitalWarning();
            });
            document.getElementById('pipsPerTrade').addEventListener('input', updateRiskDisplay);
            
            // تحديث العرض عند تحميل الصفحة
            updateRiskDisplay();
            
            // إعداد القائمة المنسدلة للأدوات
            const toolsDropdown = document.getElementById('toolsDropdown');
            const dropdownMenu = toolsDropdown?.nextElementSibling;
            
            if (toolsDropdown && dropdownMenu) {
                // إظهار القائمة عند hover
                toolsDropdown.parentElement.addEventListener('mouseenter', function() {
                    dropdownMenu.classList.add('show');
                });
                
                // إخفاء القائمة عند مغادرة المنطقة
                toolsDropdown.parentElement.addEventListener('mouseleave', function() {
                    dropdownMenu.classList.remove('show');
                });
                
                // منع إغلاق القائمة عند النقر عليها
                dropdownMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
            
            // إدارة تصدير PDF
            exportPdfBtn.addEventListener('click', function() {
                showLoading();
                loadPDFLibrary(function() {
                    exportToPdf();
                    hideLoading();
                });
            });
            
            printPlanBtn.addEventListener('click', printPlan);
            exportExcelBtn.addEventListener('click', exportToExcel);
            
            // إدارة حفظ الخطط
            savePlanBtn.addEventListener('click', saveCurrentPlan);
            showSavedPlansBtn.addEventListener('click', toggleSavedPlans);
            
            // دالة عرض شاشة التحميل
            function showLoading() {
                loadingOverlay.style.display = 'flex';
            }
            
            // دالة إخفاء شاشة التحميل
            function hideLoading() {
                loadingOverlay.style.display = 'none';
            }
            
            /**
             * دالة تحديث عرض نسبة المخاطرة
             */
            function updateRiskDisplay() {
                const riskLevel = riskLevelSelect.value;
                const capital = parseFloat(document.getElementById('capital').value) || 1000;
                const pips = parseInt(document.getElementById('pipsPerTrade').value) || 20;
                
                // حساب اللوت الأساسي
                const baseLot = calculateInitialLotSize(capital);
                document.getElementById('initialLot').value = baseLot.toFixed(2);
                
                // حساب الربح المتوقع لليوم الأول
                const pipValue = baseLot * 10;
                const expectedProfit = pipValue * pips;
                const actualRiskPercentage = (expectedProfit / capital) * 100;
                
                const riskRanges = {
                    'conservative': `~${actualRiskPercentage.toFixed(1)}% (محافظ)`,
                    'moderate': `~${actualRiskPercentage.toFixed(1)}% (متوسط)`,
                    'aggressive': `~${actualRiskPercentage.toFixed(1)}% (عالي)`,
                    'margin': `~${actualRiskPercentage.toFixed(1)}% (هامشي)`
                };
                
                autoRiskDisplay.innerHTML = `
                    ${riskRanges[riskLevel]}<br>
                    <small style="color: #ccd6f6;">لوت ${baseLot.toFixed(2)} × ${pips} نقطة = $${expectedProfit.toFixed(2)}</small>
                `;
                
                // تغيير لون العرض حسب مستوى المخاطرة
                if (actualRiskPercentage <= 5) {
                    autoRiskDisplay.style.color = '#4CAF50';
                } else if (actualRiskPercentage <= 15) {
                    autoRiskDisplay.style.color = '#FF9800';
                } else if (actualRiskPercentage <= 30) {
                    autoRiskDisplay.style.color = '#F44336';
                } else {
                    autoRiskDisplay.style.color = '#9C27B0';
                }
            }
            
            /**
             * دالة عرض تحذيرات رأس المال
             */
            function showCapitalWarning() {
                const capital = parseFloat(document.getElementById('capital').value) || 0;
                const warningDiv = document.getElementById('capitalWarning') || createWarningDiv();
                
                if (capital < 100) {
                    warningDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> رأس المال منخفض. يُنصح بـ $100 على الأقل للتداول الآمن.
                    `;
                    warningDiv.className = 'alert alert-warning mt-2';
                    warningDiv.style.display = 'block';
                } else if (capital < 200) {
                    warningDiv.innerHTML = `
                        <i class="fas fa-info-circle"></i>
                        <strong>نصيحة:</strong> رأس مال جيد للبداية. ستبدأ بأقل لوت (0.01) للحماية.
                    `;
                    warningDiv.className = 'alert alert-info mt-2';
                    warningDiv.style.display = 'block';
                } else {
                    warningDiv.style.display = 'none';
                }
            }
            
            /**
             * دالة إنشاء div التحذير
             */
            function createWarningDiv() {
                const warningDiv = document.createElement('div');
                warningDiv.id = 'capitalWarning';
                warningDiv.style.display = 'none';
                document.getElementById('capital').parentNode.appendChild(warningDiv);
                return warningDiv;
            }
            
            /**
             * دالة تنسيق الأرقام بالفواصل للآلاف
             * تضيف فواصل للأرقام الكبيرة لسهولة القراءة
             */
            function formatNumber(number) {
                return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
            
            /**
             * دالة حساب خطة إدارة رأس المال الذكية
             * تحسب اللوت التدريجي والنمو المركب بشكل واقعي
             */
            function calculateCapitalPlan() {
                // الحصول على القيم من النموذج
                const initialCapital = parseFloat(document.getElementById('capital').value);
                const riskLevel = document.getElementById('riskLevel').value;
                const days = parseInt(document.getElementById('days').value);
                const pipsPerTrade = parseInt(document.getElementById('pipsPerTrade').value);
                const lotIncrement = getLotIncrementValue();
                const maxRiskReduction = parseFloat(document.getElementById('maxRiskReduction').value);
                const maxLotSize = parseFloat(document.getElementById('maxLotSize').value);
                
                // التحقق من صحة البيانات
                if (isNaN(initialCapital) || isNaN(days) || isNaN(pipsPerTrade) ||
                    initialCapital < 50 || days <= 0 || days > 22 || pipsPerTrade <= 0) {
                    alert('يرجى إدخال جميع البيانات بشكل صحيح\nالحد الأدنى لرأس المال: $50\nالحد الأقصى للأيام: 22 يوم');
                    return;
                }
                
                // حساب نسبة المخاطرة حسب المستوى
                const riskPercentage = calculateRiskPercentage(riskLevel, initialCapital);
                
                // التحقق من إمكانية التداول
                if (!validateTradingCapability(initialCapital, riskPercentage)) {
                    return;
                }
                
                // إنشاء الجدول مع النمو المركب الذكي
                generateSmartCompoundTable(initialCapital, riskPercentage, days, pipsPerTrade, riskLevel, lotIncrement, maxRiskReduction, maxLotSize);
                
                // إظهار النتائج
                resultContainer.style.display = 'block';
                
                // إنشاء الرسوم البيانية
                createCharts();
            }
            
            /**
             * دالة حساب نسبة المخاطرة حسب المستوى ورأس المال
             */
            function calculateRiskPercentage(riskLevel, capital) {
                const riskRanges = {
                    'conservative': { min: 1, max: 3 },
                    'moderate': { min: 4, max: 8 },
                    'aggressive': { min: 8, max: 25 },
                    'margin': { min: 26, max: 55 }
                };
                
                const range = riskRanges[riskLevel];
                
                // حساب نسبة ديناميكية حسب رأس المال
                if (capital < 200) {
                    return range.min; // نسبة منخفضة للحسابات الصغيرة
                } else if (capital < 1000) {
                    return range.min + (range.max - range.min) * 0.3;
                } else if (capital < 5000) {
                    return range.min + (range.max - range.min) * 0.6;
                } else {
                    return range.max; // نسبة عالية للحسابات الكبيرة
                }
            }
            
            /**
             * دالة الحصول على قيمة زيادة اللوت
             */
            function getLotIncrementValue() {
                if (lotIncrementSelect.value === 'custom') {
                    return parseFloat(document.getElementById('customIncrement').value) || 0.05;
                }
                return parseFloat(lotIncrementSelect.value);
            }
            
            /**
             * دالة التحقق من إمكانية التداول
             */
            function validateTradingCapability(capital, riskPercentage) {
                const minLotValue = 1000; // قيمة أقل لوت (0.01) تقريباً
                const riskAmount = capital * (riskPercentage / 100);
                
                if (riskAmount < 10) {
                    alert(`تحذير: مبلغ المخاطرة ($${riskAmount.toFixed(2)}) منخفض جداً.\nيُنصح برأس مال أكبر أو نسبة مخاطرة أعلى.`);
                    return false;
                }
                
                return true;
            }
            
            /**
             * دالة إنشاء جدول النمو المركب الذكي
             * تحسب اللوت التدريجي والنمو المركب بشكل واقعي
             */
            function generateSmartCompoundTable(initialCapital, riskPercentage, days, pipsPerTrade, riskLevel, lotIncrement, maxRiskReduction, maxLotSize) {
                tableBody.innerHTML = '';
                let currentCapital = initialCapital;
                let totalProfit = 0;
                let baseLotSize = calculateInitialLotSize(initialCapital); // اللوت الأساسي
                const dayData = [];
                
                for (let day = 1; day <= days; day++) {
                    // الرصيد الأولي لليوم
                    const startingBalance = currentCapital;
                    
                    // حساب حجم اللوت التدريجي (يزيد حسب القيمة المحددة)
                    const lotSize = calculateProgressiveLotSize(baseLotSize, day, currentCapital, lotIncrement, maxLotSize);
                    
                    // حساب المخاطرة الديناميكية
                    const dynamicRisk = calculateDynamicRisk(riskPercentage, day, riskLevel, maxRiskReduction);
                    
                    // حساب الربح الفعلي من اللوت والنقاط
                    // قيمة النقطة: 0.01 لوت = $0.10 للنقطة، 0.1 لوت = $1 للنقطة، 1 لوت = $10 للنقطة
                    const pipValue = lotSize * 10; // قيمة النقطة بالدولار
                    const actualProfit = pipValue * pipsPerTrade;
                    
                    // حساب نسبة المخاطرة الفعلية (للعرض فقط)
                    const actualRiskPercentage = (actualProfit / currentCapital) * 100;
                    
                    // الرصيد النهائي = الرصيد الأولي + الربح الفعلي
                    const endingBalance = startingBalance + actualProfit;
                    
                    // إضافة للربح الإجمالي
                    totalProfit += actualProfit;
                    
                    // حفظ بيانات اليوم للرسم البياني
                    dayData.push({
                        day,
                        startingBalance,
                        lotSize,
                        actualRiskPercentage,
                        endingBalance
                    });
                    
                    // إنشاء صف الجدول مع ألوان ديناميكية
                    const row = document.createElement('tr');
                    const riskColor = getRiskColor(actualRiskPercentage);
                    
                    row.innerHTML = `
                        <td style="color: #ffffff; font-weight: bold; background: rgba(100, 255, 218, 0.1);">${day}</td>
                        <td style="color: var(--accent-color); font-weight: 600;">$${formatNumber(startingBalance)}</td>
                        <td style="color: ${riskColor}; font-weight: bold;">${actualRiskPercentage.toFixed(1)}%</td>
                        <td style="color: #4CAF50; font-weight: 600;">$${formatNumber(actualProfit)}</td>
                        <td style="color: #ffffff; font-weight: bold; background: rgba(255, 193, 7, 0.1);">${lotSize.toFixed(2)}</td>
                        <td style="color: #ffffff;">${pipsPerTrade}</td>
                        <td style="color: var(--accent-color); font-weight: bold; background: rgba(76, 175, 80, 0.1);">$${formatNumber(endingBalance)}</td>
                    `;
                    
                    tableBody.appendChild(row);
                    
                    // تحديث رأس المال لليوم التالي
                    currentCapital = endingBalance;
                }
                
                // تحديث الملخص
                updateSummary(initialCapital, currentCapital, totalProfit);
                
                // إرجاع بيانات الأيام للرسم البياني
                return dayData;
            }
            
            /**
             * دالة حساب اللوت الأساسي حسب رأس المال
             */
            function calculateInitialLotSize(capital) {
                if (capital < 100) return 0.01;
                else if (capital < 500) return 0.02;
                else if (capital < 1000) return 0.05;
                else if (capital < 2000) return 0.10;
                else if (capital < 5000) return 0.15;
                else if (capital < 10000) return 0.25;
                else return 0.50;
            }
            
            /**
             * دالة حساب اللوت التدريجي (يزيد حسب القيمة المحددة)
             */
            function calculateProgressiveLotSize(baseLotSize, day, currentCapital, increment, maxLotSize) {
                // الزيادة حسب القيمة المحددة
                let lotSize = baseLotSize + ((day - 1) * increment);
                
                // التأكد من الحدود
                if (lotSize < 0.01) lotSize = 0.01;
                if (lotSize > maxLotSize) lotSize = maxLotSize;
                
                // تقييد إضافي حسب رأس المال
                const maxAllowedLot = getMaxLotForCapital(currentCapital);
                if (lotSize > maxAllowedLot) {
                    lotSize = maxAllowedLot;
                }
                
                return Math.round(lotSize * 100) / 100;
            }
            
            /**
             * دالة حساب أقصى لوت مسموح حسب رأس المال
             */
            function getMaxLotForCapital(capital) {
                if (capital < 200) return 0.01;
                else if (capital < 500) return 0.05;
                else if (capital < 1000) return 0.10;
                else if (capital < 2000) return 0.25;
                else if (capital < 5000) return 0.50;
                else if (capital < 10000) return 1.00;
                else if (capital < 20000) return 1.50;
                else return 2.00;
            }
            
            /**
             * دالة التحقق من واقعية حجم اللوت
             */
            function validateLotSize(lotSize, capital) {
                const maxLot = getMaxLotForCapital(capital);
                return lotSize <= maxLot;
            }
            
            /**
             * دالة حساب المخاطرة الديناميكية
             */
            function calculateDynamicRisk(baseRisk, day, riskLevel, maxReduction) {
                // تقليل المخاطرة تدريجياً مع الوقت (استراتيجية ذكية)
                let riskReduction = 0;
                
                if (riskLevel === 'conservative') {
                    riskReduction = (day - 1) * 0.05; // تقليل بطيء
                } else if (riskLevel === 'moderate') {
                    riskReduction = (day - 1) * 0.1;
                } else if (riskLevel === 'aggressive') {
                    riskReduction = (day - 1) * 0.2;
                } else { // margin
                    riskReduction = (day - 1) * 0.5; // تقليل سريع للمخاطرة العالية
                }
                
                const dynamicRisk = Math.max(baseRisk - riskReduction, baseRisk * maxReduction);
                return Math.round(dynamicRisk * 10) / 10;
            }
            
            /**
             * دالة تحديد لون المخاطرة
             */
            function getRiskColor(riskPercentage) {
                if (riskPercentage <= 5) return '#4CAF50'; // أخضر
                else if (riskPercentage <= 15) return '#FF9800'; // برتقالي
                else if (riskPercentage <= 30) return '#F44336'; // أحمر
                else return '#9C27B0'; // بنفسجي للمخاطرة العالية جداً
            }
            
            /**
             * دالة تحديث ملخص النتائج مع إحصائيات ذكية
             */
            function updateSummary(initialCapital, finalCapital, totalProfit) {
                document.getElementById('initialCapital').textContent = `$${formatNumber(initialCapital)}`;
                document.getElementById('finalCapital').textContent = `$${formatNumber(finalCapital)}`;
                document.getElementById('totalProfit').textContent = `$${formatNumber(totalProfit)}`;
                
                // حساب نسبة النمو
                const growthPercentage = ((finalCapital - initialCapital) / initialCapital * 100).toFixed(1);
                document.getElementById('growthPercentage').textContent = `${growthPercentage}%`;
            }
            
            /**
             * دالة إنشاء الرسوم البيانية
             */
            function createCharts() {
                const rows = document.querySelectorAll('#capitalTable tbody tr');
                const days = [];
                const balances = [];
                const lotSizes = [];
                const riskPercentages = [];
                
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    days.push(cells[0].textContent);
                    balances.push(parseFloat(cells[6].textContent.replace(/[$,]/g, '')));
                    lotSizes.push(parseFloat(cells[4].textContent));
                    riskPercentages.push(parseFloat(cells[2].textContent));
                });
                
                // تدمير الرسوم البيانية القديمة إذا كانت موجودة
                if (balanceChart) balanceChart.destroy();
                if (lotSizeChart) lotSizeChart.destroy();
                
                // إنشاء رسم بياني للرصيد
                const balanceCtx = document.getElementById('balanceChart').getContext('2d');
                balanceChart = new Chart(balanceCtx, {
                    type: 'line',
                    data: {
                        labels: days,
                        datasets: [{
                            label: 'الرصيد ($)',
                            data: balances,
                            borderColor: '#64ffda',
                            backgroundColor: 'rgba(100, 255, 218, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                rtl: true,
                                labels: {
                                    color: '#ffffff',
                                    font: {
                                        family: 'Cairo'
                                    }
                                }
                            },
                            tooltip: {
                                rtl: true,
                                callbacks: {
                                    label: function(context) {
                                        return `الرصيد: $${context.raw.toLocaleString()}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: false,
                                ticks: {
                                    color: '#ffffff',
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                },
                                grid: {
                                    color: 'rgba(100, 255, 218, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#ffffff'
                                },
                                grid: {
                                    color: 'rgba(100, 255, 218, 0.1)'
                                }
                            }
                        }
                    }
                });
                
                // إنشاء رسم بياني لحجم اللوت
                const lotSizeCtx = document.getElementById('lotSizeChart').getContext('2d');
                lotSizeChart = new Chart(lotSizeCtx, {
                    type: 'bar',
                    data: {
                        labels: days,
                        datasets: [{
                            label: 'حجم اللوت',
                            data: lotSizes,
                            backgroundColor: 'rgba(255, 193, 7, 0.7)',
                            borderColor: 'rgba(255, 193, 7, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                rtl: true,
                                labels: {
                                    color: '#ffffff',
                                    font: {
                                        family: 'Cairo'
                                    }
                                }
                            },
                            tooltip: {
                                rtl: true,
                                callbacks: {
                                    label: function(context) {
                                        return `حجم اللوت: ${context.raw}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: '#ffffff'
                                },
                                grid: {
                                    color: 'rgba(100, 255, 218, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#ffffff'
                                },
                                grid: {
                                    color: 'rgba(100, 255, 218, 0.1)'
                                }
                            }
                        }
                    }
                });
            }
            
            /**
             * دالة تصدير البيانات إلى Excel
             */
            function exportToExcel() {
                // إنشاء البيانات
                const data = [];
                
                // إضافة العناوين
                data.push(['اليوم', 'الرصيد الأولي ($)', 'نسبة المخاطرة (%)', 'هدف الربح ($)', 'حجم اللوت', 'النقاط', 'الرصيد النهائي ($)']);
                
                // إضافة بيانات الجدول
                const rows = document.querySelectorAll('#capitalTable tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    const rowData = Array.from(cells).map(cell => cell.textContent.replace(/[$,]/g, ''));
                    data.push(rowData);
                });
                
                // إنشاء ملف CSV
                let csvContent = "data:text/csv;charset=utf-8,\uFEFF";
                data.forEach(row => {
                    csvContent += row.join(",") + "\n";
                });
                
                // تحميل الملف
                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", "خطة_إدارة_رأس_المال.csv");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
            
            function exportToPdf() {
    // تحميل مكتبة jsPDF مع التأكد من دعم العربية
    const { jsPDF } = window.jspdf;
    
    // إنشاء مستند PDF مع إعدادات خاصة بالعربية
    const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        hotfixes: ["px_scaling"]
    });

    // إضافة خط يدعم العربية (أميري)
    doc.addFont('https://cdn.jsdelivr.net/gh/arabic-fonts/arabic-fonts@latest/fonts/Amiri/Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');

    // خلفية مائية
    doc.setFontSize(60);
    doc.setTextColor(200, 200, 200);
    doc.text('فوركس الأردن', doc.internal.pageSize.width / 2, doc.internal.pageSize.height / 2, {
        angle: 45,
        align: 'center'
    });

    // العنوان الرئيسي
    doc.setFontSize(18);
    doc.setTextColor(100, 255, 218);
    doc.text('خطة إدارة رأس المال الذكية - فوركس الأردن', doc.internal.pageSize.width / 2, 15, {
        align: 'center'
    });

    // معلومات الخطة (مع تعديل المحاذاة للعربية)
    doc.setFontSize(12);
    doc.setTextColor(10, 25, 47);
    
    const capital = document.getElementById('capital').value;
    const days = document.getElementById('days').value;
    const riskLevel = document.getElementById('riskLevel').value;
    const pipsPerTrade = document.getElementById('pipsPerTrade').value;
    const initialCapital = document.getElementById('initialCapital').textContent;
    const finalCapital = document.getElementById('finalCapital').textContent;
    const totalProfit = document.getElementById('totalProfit').textContent;

    // كتابة النصوص مع محاذاة لليمين
    doc.text(`رأس المال الأولي: $${capital}`, 190, 25, { align: 'right' });
    doc.text(`مستوى المخاطرة: ${riskLevel}`, 190, 32, { align: 'right' });
    doc.text(`عدد الأيام: ${days}`, 190, 39, { align: 'right' });
    doc.text(`النقاط لكل صفقة: ${pipsPerTrade}`, 190, 46, { align: 'right' });
    doc.text(`رأس المال النهائي: ${finalCapital}`, 60, 25, { align: 'right' });
    doc.text(`إجمالي الربح: ${totalProfit}`, 60, 32, { align: 'right' });

    // جدول البيانات مع تعديلات العربية
    const headers = [
        { title: "اليوم", dataKey: "day" },
        { title: "الرصيد الأولي ($)", dataKey: "start" },
        { title: "نسبة المخاطرة (%)", dataKey: "risk" },
        { title: "هدف الربح ($)", dataKey: "profit" },
        { title: "حجم اللوت", dataKey: "lot" },
        { title: "النقاط", dataKey: "pips" },
        { title: "الرصيد النهائي ($)", dataKey: "end" }
    ];

    const rows = [];
    const tableRows = document.querySelectorAll('#capitalTable tbody tr');
    
    tableRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        rows.push({
            day: cells[0].textContent,
            start: cells[1].textContent,
            risk: cells[2].textContent,
            profit: cells[3].textContent,
            lot: cells[4].textContent,
            pips: cells[5].textContent,
            end: cells[6].textContent
        });
    });

    // إنشاء الجدول مع إعدادات العربية
    doc.autoTable({
        head: [headers.map(h => h.title)],
        body: rows.map(row => headers.map(h => row[h.dataKey])),
        startY: 55,
        styles: {
            font: 'Amiri',
            fontStyle: 'normal',
            textColor: [10, 25, 47],
            cellPadding: 3,
            fontSize: 8,
            halign: 'right' // محاذاة النص لليمين
        },
        headStyles: {
            fillColor: [100, 255, 218],
            textColor: [10, 25, 47],
            fontStyle: 'bold',
            halign: 'right' // محاذاة العناوين لليمين
        },
        alternateRowStyles: {
            fillColor: [242, 242, 242],
            halign: 'right'
        },
        margin: { right: 10, left: 10 }
    });

    // ملخص النتائج (مع محاذاة عربية)
    const finalY = doc.lastAutoTable.finalY + 10;
    doc.setFontSize(14);
    doc.setTextColor(100, 255, 218);
    doc.text('ملخص النتائج', 190, finalY, { align: 'right' });

    doc.setFontSize(10);
    doc.setTextColor(10, 25, 47);
    
    doc.text(`رأس المال الأولي: $${capital}`, 190, finalY + 10, { align: 'right' });
    doc.text(`رأس المال النهائي: ${finalCapital}`, 190, finalY + 17, { align: 'right' });
    doc.text(`إجمالي الربح المحقق: ${totalProfit}`, 190, finalY + 24, { align: 'right' });

    doc.text('نصائح النظام الواقعي:', 60, finalY + 10, { align: 'right' });
    doc.text('• الربح = اللوت × النقاط × قيمة النقطة', 60, finalY + 17, { align: 'right' });
    doc.text('• 0.1 لوت × 75 نقطة = $75 ربح', 60, finalY + 24, { align: 'right' });
    doc.text('• اللوت يزيد تدريجياً كل يوم', 60, finalY + 31, { align: 'right' });

    // إضافة تحذير
    doc.setFontSize(8);
    doc.setTextColor(200, 0, 0);
    doc.text('تحذير: هذه الحسابات تفترض تحقيق الربح المستهدف يومياً. في الواقع قد تحدث خسائر.', 190, finalY + 45, { align: 'right' });
    doc.text('التزم بوقف الخسارة ولا تتداول بأكثر من النسبة المحددة من رأس المال.', 190, finalY + 52, { align: 'right' });

    // حفظ الملف
    doc.save('خطة_إدارة_رأس_المال_الذكية_فوركس_الأردن.pdf');
}
            
            /**
             * دالة حفظ الخطة الحالية
             */
            function saveCurrentPlan() {
                const capital = parseFloat(document.getElementById('capital').value);
                const days = parseInt(document.getElementById('days').value);
                const riskLevel = document.getElementById('riskLevel').value;
                const pipsPerTrade = parseInt(document.getElementById('pipsPerTrade').value);
                
                if (isNaN(capital) || isNaN(days) || isNaN(pipsPerTrade)) {
                    alert('يرجى إنشاء خطة صالحة أولاً قبل حفظها');
                    return;
                }
                
                // إنشاء كائن الخطة
                const plan = {
                    id: Date.now(),
                    date: new Date().toLocaleString(),
                    capital,
                    days,
                    riskLevel,
                    pipsPerTrade,
                    initialLot: calculateInitialLotSize(capital),
                    lotIncrement: getLotIncrementValue(),
                    maxRiskReduction: parseFloat(document.getElementById('maxRiskReduction').value),
                    maxLotSize: parseFloat(document.getElementById('maxLotSize').value)
                };
                
                // الحصول على الخطط المحفوظة من localStorage
                let savedPlans = JSON.parse(localStorage.getItem('fxPlans')) || [];
                
                // إضافة الخطة الجديدة
                savedPlans.unshift(plan);
                
                // حفظ في localStorage
                localStorage.setItem('fxPlans', JSON.stringify(savedPlans));
                
                // عرض رسالة نجاح
                alert('تم حفظ الخطة بنجاح! يمكنك الوصول إليها من قسم "الخطط المحفوظة"');
                
                // تحديث قائمة الخطط المحفوظة
                loadSavedPlans();
            }
            
            /**
             * دالة تحميل الخطط المحفوظة
             */
            function loadSavedPlans() {
                const savedPlans = JSON.parse(localStorage.getItem('fxPlans')) || [];
                savedPlansList.innerHTML = '';
                
                if (savedPlans.length === 0) {
                    savedPlansList.innerHTML = '<p class="text-center text-muted">لا توجد خطط محفوظة</p>';
                    return;
                }
                
                savedPlans.forEach(plan => {
                    const planElement = document.createElement('div');
                    planElement.className = 'plan-item p-3 mb-2 rounded';
                    planElement.style.border = '1px solid rgba(100, 255, 218, 0.3)';
                    planElement.style.background = 'rgba(10, 25, 47, 0.5)';
                    planElement.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 style="color: var(--accent-color);">خطة ${plan.id.toString().slice(-4)}</h6>
                                <small class="text-muted">${plan.date}</small>
                            </div>
                            <div class="text-end">
                                <p class="mb-1">رأس المال: $${plan.capital.toLocaleString()}</p>
                                <p class="mb-1">الأيام: ${plan.days} يوم</p>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end mt-2">
                            <button class="btn btn-sm btn-outline-primary me-2 load-plan-btn" data-id="${plan.id}">
                                <i class="fas fa-upload"></i> تحميل
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-plan-btn" data-id="${plan.id}">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    `;
                    
                    savedPlansList.appendChild(planElement);
                });
                
                // إضافة مستمعين للأزرار
                document.querySelectorAll('.load-plan-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        loadPlan(this.getAttribute('data-id'));
                    });
                });
                
                document.querySelectorAll('.delete-plan-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        deletePlan(this.getAttribute('data-id'));
                    });
                });
            }
            
            /**
             * دالة تحميل خطة محفوظة
             */
            function loadPlan(planId) {
                const savedPlans = JSON.parse(localStorage.getItem('fxPlans')) || [];
                const plan = savedPlans.find(p => p.id == planId);
                
                if (!plan) {
                    alert('الخطة غير موجودة');
                    return;
                }
                
                // تعبئة النموذج بقيم الخطة
                document.getElementById('capital').value = plan.capital;
                document.getElementById('days').value = plan.days;
                document.getElementById('riskLevel').value = plan.riskLevel;
                document.getElementById('pipsPerTrade').value = plan.pipsPerTrade;
                document.getElementById('lotIncrement').value = plan.lotIncrement === 0.01 ? '0.01' : 
                                                               plan.lotIncrement === 0.05 ? '0.05' : 
                                                               plan.lotIncrement === 0.10 ? '0.10' : 'custom';
                
                if (plan.lotIncrement !== 0.01 && plan.lotIncrement !== 0.05 && plan.lotIncrement !== 0.10) {
                    document.getElementById('customIncrement').value = plan.lotIncrement;
                    document.getElementById('customIncrementContainer').style.display = 'block';
                }
                
                document.getElementById('maxRiskReduction').value = plan.maxRiskReduction;
                document.getElementById('maxLotSize').value = plan.maxLotSize;
                
                // تحديث العرض
                updateRiskDisplay();
                
                // إظهار رسالة نجاح
                alert('تم تحميل الخطة بنجاح! اضغط على "إنشاء خطة" لتطبيقها');
            }
            
            /**
             * دالة حذف خطة محفوظة
             */
            function deletePlan(planId) {
                if (!confirm('هل أنت متأكد من رغبتك في حذف هذه الخطة؟')) return;
                
                let savedPlans = JSON.parse(localStorage.getItem('fxPlans')) || [];
                savedPlans = savedPlans.filter(p => p.id != planId);
                
                localStorage.setItem('fxPlans', JSON.stringify(savedPlans));
                loadSavedPlans();
            }
            
            /**
             * دالة إظهار/إخفاء الخطط المحفوظة
             */
            function toggleSavedPlans() {
                if (savedPlansContainer.style.display === 'none') {
                    loadSavedPlans();
                    savedPlansContainer.style.display = 'block';
                    showSavedPlansBtn.innerHTML = '<i class="fas fa-eye-slash"></i> إخفاء الخطط المحفوظة';
                } else {
                    savedPlansContainer.style.display = 'none';
                    showSavedPlansBtn.innerHTML = '<i class="fas fa-history"></i> عرض الخطط المحفوظة';
                }
            }
        });
    </script>
</body>
</html>
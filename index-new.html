<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة المحللين الماليين | فوركس الأردن</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --border-light: #e9ecef;
            --shadow-light: rgba(0, 51, 102, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background-color: var(--primary-white);
            color: var(--text-dark);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header Styles */
        .main-header {
            background: var(--primary-white);
            box-shadow: 0 2px 20px var(--shadow-light);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            color: var(--primary-blue) !important;
            font-weight: 700;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .navbar-brand img {
            height: 40px;
            border-radius: 50%;
        }

        .nav-link {
            color: var(--text-dark) !important;
            font-weight: 500;
            padding: 10px 15px !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--primary-blue) !important;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--accent-blue);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 80%;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23003366" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: var(--text-light);
            margin-bottom: 2rem;
            max-width: 600px;
        }

        .btn-primary-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .btn-secondary-custom {
            background: transparent;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            padding: 13px 28px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-secondary-custom:hover {
            background: var(--primary-blue);
            color: var(--primary-white);
            transform: translateY(-2px);
        }

        /* Features Section */
        .features-section {
            padding: 100px 0;
            background: var(--primary-white);
        }

        .feature-card {
            background: var(--primary-white);
            border: 1px solid var(--border-light);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            box-shadow: 0 5px 15px var(--shadow-light);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px var(--shadow-light);
            border-color: var(--accent-blue);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, var(--accent-blue), var(--light-blue));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: var(--primary-white);
        }

        .feature-title {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .feature-description {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Loading Animation */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
            flex-direction: column;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border-light);
            border-top: 4px solid var(--accent-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .btn-primary-custom,
            .btn-secondary-custom {
                padding: 12px 24px;
                font-size: 1rem;
                width: 100%;
                max-width: 280px;
                justify-content: center;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg main-header">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <img src="assets/img/logo.png" alt="فوركس الأردن">
                منصة المحللين الماليين
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#analysts">المحللين</a>
                    </li>
                </ul>
                
                <div class="d-flex gap-2">
                    <a href="login.html" class="btn btn-secondary-custom">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </a>
                    <a href="register.html" class="btn btn-primary-custom">
                        <i class="fas fa-user-plus"></i>
                        إنشاء حساب
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            منصة المحللين الماليين
                            <span style="color: var(--accent-blue);">المتقدمة</span>
                        </h1>
                        <p class="hero-subtitle">
                            اربط بين المحللين الماليين المحترفين والمستثمرين الباحثين عن التحليلات الدقيقة.
                            منصة شاملة للتحليل المالي والاستثمار الذكي.
                        </p>

                        <div class="d-flex gap-3 flex-wrap">
                            <a href="register.html?type=analyst" class="btn-primary-custom">
                                <i class="fas fa-chart-line"></i>
                                انضم كمحلل مالي
                            </a>
                            <a href="register.html?type=subscriber" class="btn-secondary-custom">
                                <i class="fas fa-users"></i>
                                اشترك في التحليلات
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="text-center">
                        <img src="assets/img/hero-illustration.svg" alt="منصة التحليل المالي"
                             style="max-width: 100%; height: auto;"
                             onerror="this.style.display='none'">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 style="color: var(--primary-blue); font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">
                    مميزات المنصة
                </h2>
                <p style="color: var(--text-light); font-size: 1.2rem; max-width: 600px; margin: 0 auto;">
                    منصة متكاملة تجمع بين المحللين الماليين والمستثمرين في بيئة آمنة ومتطورة
                </p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <h3 class="feature-title">محللين محترفين</h3>
                        <p class="feature-description">
                            محللين ماليين معتمدين مع سنوات من الخبرة في الأسواق المالية
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="feature-title">دردشة مباشرة</h3>
                        <p class="feature-description">
                            تواصل مباشر مع المحللين عبر نظام دردشة آمن داخل المنصة
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">أمان متقدم</h3>
                        <p class="feature-description">
                            حماية عالية للبيانات مع نظام مصادقة متطور وتشفير شامل
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">تحليلات متقدمة</h3>
                        <p class="feature-description">
                            تحليلات مالية شاملة مع رسوم بيانية تفاعلية ومؤشرات دقيقة
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">متوافق مع الجوال</h3>
                        <p class="feature-description">
                            تصميم متجاوب يعمل بسلاسة على جميع الأجهزة والشاشات
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3 class="feature-title">متاح 24/7</h3>
                        <p class="feature-description">
                            خدمة متاحة على مدار الساعة مع تحديثات فورية للتحليلات
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-container" style="display: none;">
        <div class="loading-spinner"></div>
        <p style="color: var(--text-light);">جاري التحميل...</p>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase Configuration -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "jorinvforex.firebaseapp.com",
            projectId: "jorinvforex",
            storageBucket: "jorinvforex.appspot.com",
            messagingSenderId: "your-sender-id",
            appId: "your-app-id"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Initialize Firebase services
        const auth = firebase.auth();
        const db = firebase.firestore();
        const storage = firebase.storage();

        console.log('Firebase initialized successfully');
    </script>

    <!-- Main Application Script -->
    <script src="js/app.js"></script>
</body>
</html>

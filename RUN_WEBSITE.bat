@echo off
chcp 65001 >nul
title 🚀 تشغيل موقع jorinvforex

echo.
echo ═══════════════════════════════════════════════════════════
echo                🚀 تشغيل موقع jorinvforex
echo ═══════════════════════════════════════════════════════════
echo.

:: تشغيل XAMPP
echo 🔄 تشغيل XAMPP...
start "" "C:\xampp\xampp-control.exe"
timeout /t 2 /nobreak >nul

:: فتح معالج الإعداد المحسن
echo 🛠️ فتح معالج الإعداد...
start "" "http://localhost/jorinvforex/setup-database-new.php"
timeout /t 2 /nobreak >nul

:: فتح الصفحة الرئيسية
echo 🏠 فتح الصفحة الرئيسية...
start "" "http://localhost/jorinvforex/index-sql.php"

echo.
echo ═══════════════════════════════════════════════════════════
echo                    ✅ تم التشغيل!
echo ═══════════════════════════════════════════════════════════
echo.
echo 📋 خطوات سريعة:
echo.
echo 1. في XAMPP: شغّل Apache + MySQL
echo 2. في معالج الإعداد: اضغط "إعداد قاعدة البيانات الآن"
echo 3. انتظر انتهاء الإعداد
echo 4. استمتع بالموقع!
echo.
echo 🧪 حسابات تجريبية:
echo.
echo 👑 مدير: <EMAIL> / Admin123456
echo 📊 محلل: <EMAIL> / Analyst123456
echo 👤 مشترك: <EMAIL> / Subscriber123456
echo.
echo ═══════════════════════════════════════════════════════════

pause

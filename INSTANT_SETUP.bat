@echo off
chcp 65001 >nul
title إعداد سريع لموقع jorinvforex

echo.
echo ═══════════════════════════════════════════════════════════
echo                🚀 إعداد سريع لموقع jorinvforex
echo ═══════════════════════════════════════════════════════════
echo.

:: التحقق من وجود XAMPP
if not exist "C:\xampp\xampp-control.exe" (
    echo ❌ XAMPP غير مثبت!
    echo.
    echo يرجى تثبيت XAMPP أولاً من:
    echo https://www.apachefriends.org/download.html
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على XAMPP
echo.

:: تشغيل XAMPP Control Panel
echo 🔄 تشغيل XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"

:: انتظار قليل
timeout /t 3 /nobreak >nul

echo.
echo 📋 تعليمات سريعة:
echo.
echo 1. في XAMPP Control Panel:
echo    - اضغط Start بجانب Apache
echo    - اضغط Start بجانب MySQL
echo    - انتظر حتى يصبح اللون أخضر
echo.
echo 2. اضغط أي مفتاح للمتابعة...
pause >nul

:: فتح معالج الإعداد المحسن
echo.
echo 🛠️ فتح معالج الإعداد المحسن...
start "" "http://localhost/jorinvforex/setup-database-new.php"

:: انتظار قليل
timeout /t 3 /nobreak >nul

echo.
echo ═══════════════════════════════════════════════════════════
echo                    🎯 خطوات الإعداد
echo ═══════════════════════════════════════════════════════════
echo.
echo 1. في الصفحة التي فتحت:
echo    - اضغط "إعداد قاعدة البيانات الآن"
echo    - انتظر حتى ينتهي الإعداد
echo.
echo 2. بعد انتهاء الإعداد:
echo    - اضغط "الصفحة الرئيسية"
echo    - أو "تسجيل الدخول"
echo.
echo ═══════════════════════════════════════════════════════════
echo                    🧪 حسابات تجريبية
echo ═══════════════════════════════════════════════════════════
echo.
echo 👑 مدير النظام:
echo    البريد: <EMAIL>
echo    كلمة المرور: Admin123456
echo.
echo 📊 محلل مالي:
echo    البريد: <EMAIL>
echo    كلمة المرور: Analyst123456
echo.
echo 👤 مشترك:
echo    البريد: <EMAIL>
echo    كلمة المرور: Subscriber123456
echo.
echo ═══════════════════════════════════════════════════════════
echo                    🔗 روابط مفيدة
echo ═══════════════════════════════════════════════════════════
echo.
echo 🏠 الصفحة الرئيسية:
echo http://localhost/jorinvforex/index-sql.php
echo.
echo 🔐 تسجيل الدخول:
echo http://localhost/jorinvforex/login-sql.php
echo.
echo 🔍 فحص النظام:
echo http://localhost/jorinvforex/system-check.php
echo.
echo 🛠️ إعداد قاعدة البيانات:
echo http://localhost/jorinvforex/setup-database-new.php
echo.
echo ═══════════════════════════════════════════════════════════

echo.
echo 🎉 الإعداد السريع مكتمل!
echo.
echo اضغط أي مفتاح للخروج...
pause >nul

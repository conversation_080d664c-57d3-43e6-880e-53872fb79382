/* ===== تحسينات إضافية للتجاوب الكامل ===== */

/* تحسينات عامة للأداء */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* السماح بالتحديد للنصوص المهمة */
p, h1, h2, h3, h4, h5, h6, span, div[id="typed-about"] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* تحسينات الخطوط للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}

/* تحسينات للوضع الأفقي على الهواتف */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .hero-section {
        min-height: 100vh !important;
        padding: 20px 0 !important;
    }
    
    .hero-title {
        font-size: 2rem !important;
        margin-bottom: 0.8rem !important;
    }
    
    .hero-description {
        font-size: 0.9rem !important;
        margin-bottom: 1rem !important;
    }
    
    .hero-buttons {
        gap: 10px !important;
        margin-top: 1rem !important;
    }
    
    .hero-btn-primary,
    .hero-btn-secondary {
        padding: 8px 20px !important;
        font-size: 0.8rem !important;
    }
    
    .splash-content {
        max-height: 90vh !important;
        overflow-y: auto !important;
        padding: 15px !important;
    }
    
    .navbar {
        padding: 0.3rem 1rem !important;
    }
}

/* تحسينات للشاشات فائقة العرض */
@media (min-width: 2560px) {
    .container {
        max-width: 1800px !important;
    }
    
    .hero-title {
        font-size: 6rem !important;
    }
    
    .hero-description {
        font-size: 2.2rem !important;
    }
    
    .service-card {
        padding: 4rem !important;
    }
    
    .section-title {
        font-size: 4rem !important;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 320px) {
    html {
        font-size: 12px;
    }
    
    .container {
        padding: 0 5px !important;
    }
    
    .hero-title {
        font-size: 1.5rem !important;
        line-height: 1 !important;
    }
    
    .hero-description {
        font-size: 0.8rem !important;
    }
    
    .hero-btn-primary,
    .hero-btn-secondary {
        padding: 8px 16px !important;
        font-size: 0.75rem !important;
        max-width: 200px !important;
    }
    
    .navbar-brand {
        font-size: 1rem !important;
    }
    
    .navbar-brand img {
        height: 25px !important;
    }
    
    .service-card {
        padding: 0.8rem !important;
    }
    
    .service-icon {
        width: 40px !important;
        height: 40px !important;
    }
    
    .service-title {
        font-size: 0.9rem !important;
    }
    
    .service-text {
        font-size: 0.7rem !important;
    }
}

/* تحسينات للطباعة */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .navbar,
    .splash-screen,
    video,
    .scroll-progress,
    #market-sessions,
    .hero-section video {
        display: none !important;
    }
    
    .hero-section {
        background: white !important;
        color: black !important;
        padding: 20px !important;
    }
    
    .container {
        max-width: 100% !important;
        padding: 0 !important;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: black !important;
        page-break-after: avoid;
    }
    
    .service-card,
    .three-d-plan-card,
    .contact-card-3d {
        border: 1px solid #ccc !important;
        page-break-inside: avoid;
        margin-bottom: 20px !important;
    }
    
    a {
        color: black !important;
        text-decoration: underline !important;
    }
    
    .hero-title {
        font-size: 24pt !important;
    }
    
    .hero-description {
        font-size: 14pt !important;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: dark;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    .service-card,
    .three-d-plan-card,
    .contact-card-3d,
    .glass-card,
    .splash-content {
        border: 2px solid #64ffda !important;
        background: rgba(10, 25, 47, 0.95) !important;
    }
    
    .nav-link,
    .btn,
    .hero-btn-primary,
    .hero-btn-secondary,
    .splash-btn {
        border: 1px solid #64ffda !important;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: #ffffff !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
    }
    
    p, span, div {
        color: #e6f1ff !important;
    }
}

/* تحسينات لتوفير البيانات */
@media (prefers-reduced-data: reduce) {
    video {
        display: none !important;
    }
    
    .hero-section {
        background: linear-gradient(135deg, #0a192f 0%, #112240 100%) !important;
    }
    
    * {
        animation: none !important;
        transition: none !important;
    }
    
    .luxury-bg-effects,
    .three-d-elements::before,
    .three-d-elements::after {
        display: none !important;
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    /* تم إزالة تأثيرات الآلة الكاتبة */
    
    .hero-section svg {
        animation: none !important;
    }
}

/* تحسينات للشاشات اللمسية */
@media (hover: none) and (pointer: coarse) {
    .service-card:hover,
    .feature-card:hover,
    .btn:hover,
    .nav-link:hover {
        transform: none !important;
    }
    
    .service-card:active,
    .feature-card:active,
    .btn:active {
        transform: scale(0.98) !important;
        transition: transform 0.1s ease !important;
    }
}

/* تحسينات إضافية للأداء */
.hero-section,
.service-card,
.three-d-plan-card,
.contact-card-3d {
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تحسينات للتمرير السلس */
@supports (scroll-behavior: smooth) {
    html {
        scroll-behavior: smooth;
    }
}

/* تحسينات للمتصفحات القديمة */
@supports not (backdrop-filter: blur(10px)) {
    .navbar,
    .splash-content,
    .glass-card,
    .market-session-card {
        background: rgba(10, 25, 47, 0.95) !important;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 3840px) {
    .container {
        max-width: 2400px !important;
    }
    
    .hero-title {
        font-size: 8rem !important;
    }
    
    .hero-description {
        font-size: 3rem !important;
    }
}

// ===== منصة المحللين الماليين - الملف الرئيسي =====

// متغيرات عامة
let currentUser = null;
let userRole = null;

// ===== تهيئة التطبيق =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل منصة المحللين الماليين');
    
    // تهيئة Firebase Auth State Listener
    initAuthStateListener();
    
    // تهيئة التفاعلات
    initInteractions();
    
    // تهيئة التنقل السلس
    initSmoothScrolling();
});

// ===== مراقب حالة المصادقة =====
function initAuthStateListener() {
    auth.onAuthStateChanged(async (user) => {
        if (user) {
            currentUser = user;
            console.log('المستخدم مسجل الدخول:', user.email);
            
            // جلب دور المستخدم من Firestore
            try {
                const userDoc = await db.collection('users').doc(user.uid).get();
                if (userDoc.exists) {
                    userRole = userDoc.data().role;
                    console.log('دور المستخدم:', userRole);
                    
                    // توجيه المستخدم حسب دوره
                    redirectUserBasedOnRole();
                } else {
                    console.log('لم يتم العثور على بيانات المستخدم');
                }
            } catch (error) {
                console.error('خطأ في جلب بيانات المستخدم:', error);
            }
        } else {
            currentUser = null;
            userRole = null;
            console.log('المستخدم غير مسجل الدخول');
        }
        
        // تحديث واجهة المستخدم
        updateUIBasedOnAuth();
    });
}

// ===== توجيه المستخدم حسب الدور =====
function redirectUserBasedOnRole() {
    // تحقق من الصفحة الحالية
    const currentPage = window.location.pathname;
    
    // إذا كان المستخدم في الصفحة الرئيسية، وجهه لوحة التحكم
    if (currentPage === '/' || currentPage === '/index.html' || currentPage === '/index-new.html') {
        switch (userRole) {
            case 'analyst':
                window.location.href = 'dashboard-analyst.html';
                break;
            case 'subscriber':
                window.location.href = 'dashboard-subscriber.html';
                break;
            case 'admin':
                window.location.href = 'dashboard-admin.html';
                break;
            default:
                console.log('دور غير معروف:', userRole);
        }
    }
}

// ===== تحديث واجهة المستخدم حسب حالة المصادقة =====
function updateUIBasedOnAuth() {
    const loginBtn = document.querySelector('a[href="login.html"]');
    const registerBtn = document.querySelector('a[href="register.html"]');
    const navbarNav = document.querySelector('#navbarNav .d-flex');
    
    if (currentUser && navbarNav) {
        // إخفاء أزرار تسجيل الدخول والتسجيل
        if (loginBtn) loginBtn.style.display = 'none';
        if (registerBtn) registerBtn.style.display = 'none';
        
        // إضافة قائمة المستخدم
        const userMenu = createUserMenu();
        navbarNav.innerHTML = userMenu;
    }
}

// ===== إنشاء قائمة المستخدم =====
function createUserMenu() {
    const dashboardLink = getDashboardLink();
    
    return `
        <div class="dropdown">
            <button class="btn btn-secondary-custom dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-user"></i>
                ${currentUser.displayName || currentUser.email}
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="${dashboardLink}">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>
                <li><a class="dropdown-item" href="profile.html">
                    <i class="fas fa-user-edit"></i> الملف الشخصي
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="signOut()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a></li>
            </ul>
        </div>
    `;
}

// ===== الحصول على رابط لوحة التحكم =====
function getDashboardLink() {
    switch (userRole) {
        case 'analyst':
            return 'dashboard-analyst.html';
        case 'subscriber':
            return 'dashboard-subscriber.html';
        case 'admin':
            return 'dashboard-admin.html';
        default:
            return '#';
    }
}

// ===== تسجيل الخروج =====
async function signOut() {
    try {
        await auth.signOut();
        console.log('تم تسجيل الخروج بنجاح');
        window.location.href = 'index-new.html';
    } catch (error) {
        console.error('خطأ في تسجيل الخروج:', error);
        showNotification('حدث خطأ في تسجيل الخروج', 'error');
    }
}

// ===== تهيئة التفاعلات =====
function initInteractions() {
    // تأثيرات الكروت
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // تأثيرات الأزرار
    const customButtons = document.querySelectorAll('.btn-primary-custom, .btn-secondary-custom');
    customButtons.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// ===== التنقل السلس =====
function initSmoothScrolling() {
    // التنقل السلس للروابط الداخلية
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // تحديث الروابط النشطة في شريط التنقل
    window.addEventListener('scroll', updateActiveNavLink);
}

// ===== تحديث الرابط النشط في شريط التنقل =====
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        if (window.pageYOffset >= sectionTop) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
}

// ===== عرض الإشعارات =====
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// ===== دوال مساعدة =====

// تحويل التاريخ إلى تنسيق عربي
function formatDateArabic(date) {
    return new Date(date).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// إظهار/إخفاء شاشة التحميل
function showLoading() {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        loadingScreen.style.display = 'flex';
    }
}

function hideLoading() {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        loadingScreen.style.display = 'none';
    }
}

// تصدير الدوال للاستخدام العام
window.signOut = signOut;
window.showNotification = showNotification;
window.showLoading = showLoading;
window.hideLoading = hideLoading;

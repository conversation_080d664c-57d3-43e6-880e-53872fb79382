<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأخبار المالية والتقويم الاقتصادي - فوركس الأردن</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        /* تحسين التنقل السلس */
        a[href^="#"] {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0a192f 0%, #112240 50%, #1e3a8a 100%);
            color: #fff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* تأثيرات خلفية متحركة */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(100, 255, 218, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(87, 203, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(100, 255, 218, 0.05) 0%, transparent 50%);
            animation: backgroundMove 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-20px) translateY(-10px); }
            50% { transform: translateX(20px) translateY(10px); }
            75% { transform: translateX(-10px) translateY(20px); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(100, 255, 218, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: headerGlow 4s ease-in-out infinite alternate;
        }

        @keyframes headerGlow {
            0% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
            100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
        }

        .header h1 {
            font-size: 3.2rem;
            font-weight: 900;
            color: #e6f1ff;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
        }

        .header span {
            background: linear-gradient(45deg, #64ffda, #57cbff, #64ffda);
            background-size: 200% 100%;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: gradientShift 3s ease-in-out infinite;
            text-shadow: none;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header p {
            font-size: 1.3rem;
            color: #ccd6f6;
            position: relative;
            z-index: 2;
            font-weight: 500;
            opacity: 0.9;
        }

        .tabs-container {
            margin-bottom: 40px;
            position: relative;
        }

        .nav-tabs {
            border: none;
            justify-content: center;
            background: rgba(10, 25, 47, 0.8);
            border-radius: 20px;
            padding: 15px;
            backdrop-filter: blur(20px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(100, 255, 218, 0.2);
            position: relative;
            overflow: hidden;
        }

        .nav-tabs::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(100, 255, 218, 0.05), rgba(87, 203, 255, 0.05));
            z-index: 0;
        }

        .nav-tabs .nav-link {
            background: transparent;
            color: #ccd6f6;
            border: 2px solid rgba(100, 255, 218, 0.3);
            border-radius: 15px;
            padding: 15px 30px;
            font-weight: 700;
            font-size: 1rem;
            margin: 0 8px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(45deg, #64ffda, #57cbff);
            color: #0a192f;
            border-color: transparent;
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.3);
        }

        .nav-tabs .nav-link:hover:not(.active) {
            background: rgba(100, 255, 218, 0.15);
            color: #64ffda;
            border-color: rgba(100, 255, 218, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.2);
        }

        .nav-tabs .nav-link i {
            margin-left: 8px;
            font-size: 1.1rem;
        }

        .widget-container {
            background: rgba(10, 25, 47, 0.8);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(100, 255, 218, 0.3);
            padding: 30px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            min-height: 500px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .widget-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(100, 255, 218, 0.03) 0%, rgba(87, 203, 255, 0.03) 100%);
            z-index: 0;
        }

        .widget-container:hover {
            border-color: rgba(100, 255, 218, 0.5);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
            transform: translateY(-5px);
        }

        .widget-title {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            font-size: 1.6rem;
            font-weight: 800;
            color: #e6f1ff;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        }

        .widget-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
        }

        .widget-title i {
            color: #64ffda;
            margin-left: 12px;
            font-size: 1.8rem;
            text-shadow: 0 0 15px rgba(100, 255, 218, 0.5);
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .header {
                padding: 25px 0;
                margin-bottom: 30px;
            }

            .header h1 {
                font-size: 2.2rem;
                margin-bottom: 15px;
                line-height: 1.2;
            }

            .header p {
                font-size: 1.1rem;
                padding: 0 10px;
            }

            .nav-tabs {
                flex-direction: column;
                gap: 12px;
                padding: 12px;
                border-radius: 15px;
            }

            .nav-tabs .nav-link {
                width: 100%;
                text-align: center;
                margin: 0;
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .widget-container {
                padding: 25px;
                min-height: 450px;
                border-radius: 20px;
            }

            .widget-title {
                font-size: 1.4rem;
                margin-bottom: 20px;
                text-align: center;
                justify-content: center;
            }

            .widget-title i {
                font-size: 1.6rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .header p {
                font-size: 1rem;
            }

            .nav-tabs .nav-link {
                padding: 10px 15px;
                font-size: 0.85rem;
            }

            .widget-container {
                padding: 20px;
                min-height: 400px;
            }

            .widget-title {
                font-size: 1.2rem;
                flex-direction: column;
                gap: 8px;
            }

            .widget-title i {
                font-size: 1.4rem;
                margin-left: 0;
            }
        }

        /* تحسينات إضافية للتفاعل */
        .tradingview-widget-container {
            position: relative;
            z-index: 1;
        }

        /* تأثيرات تحميل */
        .widget-container.loading {
            opacity: 0.7;
        }

        .widget-container.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 3px solid rgba(100, 255, 218, 0.3);
            border-top: 3px solid #64ffda;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 10;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* تصميم الأسعار المباشرة مثل البابيل */
        .live-prices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .price-card {
            background: rgba(16, 36, 70, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .price-card:hover {
            border-color: rgba(100, 255, 218, 0.4);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .price-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .price-card:hover::before {
            opacity: 1;
        }

        .price-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .currency-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .currency-icon {
            width: 40px;
            height: 40px;
            background: rgba(100, 255, 218, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #64ffda;
        }

        .currency-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: #e6f1ff;
            margin-bottom: 3px;
        }

        .currency-pair {
            font-size: 0.85rem;
            color: #ccd6f6;
            opacity: 0.8;
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.75rem;
            color: #4ade80;
            font-weight: 600;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: #4ade80;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .price-display {
            display: flex;
            align-items: baseline;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .current-price {
            font-size: 1.8rem;
            font-weight: 800;
            color: #e6f1ff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .price-change {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .price-change.positive {
            color: #4ade80;
        }

        .price-change.negative {
            color: #ef4444;
        }

        .price-stats {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #ccd6f6;
            opacity: 0.7;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        /* تصميم الهيدر الموحد */
        .navbar {
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 900;
            color: #64ffda !important;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 10px rgba(100, 255, 218, 0.5));
        }

        .nav-link {
            color: #ccd6f6 !important;
            font-weight: 500;
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 12px 18px !important;
            border-radius: 8px;
            margin: 0 5px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            transition: all 0.4s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 85%;
        }

        .nav-link:hover {
            color: #64ffda !important;
            transform: translateY(-3px);
            background: rgba(100, 255, 218, 0.1);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.2);
        }

        .nav-link.active {
            color: #64ffda !important;
        }

        /* تصميم القائمة المنسدلة للأدوات */
        .dropdown-menu {
            border: none !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6) !important;
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px) !important;
            border-radius: 10px !important;
            border: 1px solid rgba(100, 255, 218, 0.3) !important;
        }

        .dropdown-item {
            border: none !important;
            background: transparent !important;
            color: #ccd6f6 !important;
            transition: all 0.3s ease !important;
            border-radius: 6px !important;
            margin: 2px 8px !important;
        }

        .dropdown-item:hover {
            background: rgba(100, 255, 218, 0.1) !important;
            color: #64ffda !important;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.1) !important;
        }

        .dropdown-item:hover i {
            color: #64ffda !important;
            transform: scale(1.1);
            filter: drop-shadow(0 0 5px rgba(100, 255, 218, 0.5));
        }

        .dropdown-toggle::after {
            border-top: 4px solid;
            border-right: 3px solid transparent;
            border-left: 3px solid transparent;
            margin-right: 3px;
        }

        /* تأثيرات انتقالية للقائمة المنسدلة */
        .dropdown-menu {
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            display: block !important;
            visibility: hidden;
        }

        .dropdown:hover .dropdown-menu,
        .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* تحسينات للهواتف - الهيدر */
        @media (max-width: 768px) {
            .dropdown-menu {
                position: static !important;
                transform: none !important;
                opacity: 1 !important;
                visibility: visible !important;
                background: rgba(10, 25, 47, 0.98) !important;
                border-radius: 8px !important;
                margin: 5px 0 !important;
            }

            .dropdown-item {
                padding: 12px 20px !important;
                font-size: 0.9rem !important;
            }
        }
    </style>
</head>

<body>
    <!-- الهيدر -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../fxjordan.html">
                <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                فوركس الأردن
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                style="border: 1px solid rgba(100, 255, 218, 0.3); padding: 8px 12px;">
                <i class="fas fa-bars" style="color: #64ffda; font-size: 1.2rem;"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" style="font-weight: 500;">
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#home"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#about"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">من
                            نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#services"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            خدماتنا</a>
                    </li>

                    <!-- قائمة الأدوات المنسدلة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                            style="color: #64ffda; margin: 0 10px; position: relative; padding: 8px 15px !important; display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-tools" style="font-size: 0.9rem;"></i>
                            الأدوات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                            <li>
                                <a class="dropdown-item" href="Risk-management.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-chart-pie" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    خطط إدارة رأس المال
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Lotcalculator.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calculator" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    حاسبة اللوت
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Economic-news.html"
                                    style="color: #64ffda; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px; background: rgba(100, 255, 218, 0.1);">
                                    <i class="fas fa-calendar-alt" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    التقويم الاقتصادي
                                </a>
                            </li>

                            
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#plans"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">باقات
                            الاشتراك</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#partners"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الشركاء</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fxjordan.html#contact"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            تواصل معنا
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container" style="margin-top: 100px;">
        <!-- رأس الصفحة -->
        <div class="header">
            <h1>الأخبار المالية <span>المباشرة</span></h1>
            <p style="font-size: 1.1rem; color: #ccd6f6;">بيانات حقيقية ومحدثة لحظياً من TradingView</p>
        </div>

        <!-- التبويبات -->
        <div class="tabs-container">
            <ul class="nav nav-tabs" id="newsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar" type="button" role="tab">
                        <i class="fas fa-calendar-alt"></i> التقويم الاقتصادي
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="news-tab" data-bs-toggle="tab" data-bs-target="#news" type="button" role="tab">
                        <i class="fas fa-newspaper"></i> آخر الأخبار
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab">
                        <i class="fas fa-chart-line"></i> التحليلات
                    </button>
                </li>
            </ul>
        </div>

        <!-- محتوى التبويبات -->
        <div class="tab-content" id="newsTabsContent">
            
            <!-- تبويب التقويم الاقتصادي -->
            <div class="tab-pane fade show active" id="calendar" role="tabpanel">
                <div class="widget-container">
                    <h3 class="widget-title">
                        <i class="fas fa-calendar-alt"></i>
                        التقويم الاقتصادي المباشر
                    </h3>
                    <!-- TradingView Economic Calendar Widget -->
                    <div class="investing-widget-container">
                        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-events.js" async>
                        {
                            "colorTheme": "dark",
                            "isTransparent": true,
                            "width": "100%",
                            "height": "450",
                            "locale": "ar",
                            "importanceFilter": "-1,0,1"
                        }
                        </script>
                    </div>
                </div>
            </div>

            <!-- تبويب الأخبار -->
            <div class="tab-pane fade" id="news" role="tabpanel">
                <div class="widget-container">
                    <h3 class="widget-title">
                        <i class="fas fa-newspaper"></i>
                        آخر الأخبار المالية
                    </h3>
                    <!-- TradingView News Timeline Widget -->
                    <div class="tradingview-widget-container">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-timeline.js" async>
                        {
                            "feedMode": "market",
                            "market": "forex",
                            "colorTheme": "dark",
                            "isTransparent": true,
                            "displayMode": "regular",
                            "width": "100%",
                            "height": "450",
                            "locale": "ar"
                        }
                        </script>
                    </div>
                </div>
            </div>

            <!-- تبويب التحليلات -->
            <div class="tab-pane fade" id="analysis" role="tabpanel">
                <div class="widget-container">
                    <h3 class="widget-title">
                        <i class="fas fa-chart-line"></i>
                        التحليلات الفنية المباشرة
                    </h3>
                    <!-- TradingView Technical Analysis Widget -->
                    <div class="tradingview-widget-container">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-technical-analysis.js" async>
                        {
                            "interval": "1m",
                            "width": "100%",
                            "isTransparent": true,
                            "height": "450",
                            "symbol": "OANDA:EURUSD",
                            "showIntervalTabs": true,
                            "locale": "ar",
                            "colorTheme": "dark"
                        }
                        </script>
                    </div>

                    <!-- إضافة ويدجت تحليل إضافي للعملات الرئيسية -->
                    <div class="mt-4">
                        <h4 style="color: #e6f1ff; font-size: 1.2rem; margin-bottom: 15px;">
                            <i class="fas fa-chart-bar" style="color: #64ffda; margin-left: 8px;"></i>
                            نظرة عامة على السوق
                        </h4>
                        <div class="tradingview-widget-container">
                            <div class="tradingview-widget-container__widget"></div>
                            <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-market-overview.js" async>
                            {
                                "colorTheme": "dark",
                                "dateRange": "12M",
                                "showChart": true,
                                "locale": "ar",
                                "width": "100%",
                                "height": "400",
                                "largeChartUrl": "",
                                "isTransparent": true,
                                "showSymbolLogo": true,
                                "tabs": [
                                    {
                                        "title": "الفوركس",
                                        "symbols": [
                                            {"s": "OANDA:EURUSD", "d": "EUR/USD"},
                                            {"s": "OANDA:GBPUSD", "d": "GBP/USD"},
                                            {"s": "OANDA:USDJPY", "d": "USD/JPY"},
                                            {"s": "OANDA:USDCHF", "d": "USD/CHF"},
                                            {"s": "OANDA:AUDUSD", "d": "AUD/USD"},
                                            {"s": "OANDA:USDCAD", "d": "USD/CAD"}
                                        ]
                                    },
                                    {
                                        "title": "المعادن",
                                        "symbols": [
                                            {"s": "OANDA:XAUUSD", "d": "الذهب"},
                                            {"s": "OANDA:XAGUSD", "d": "الفضة"},
                                            {"s": "OANDA:XPTUSD", "d": "البلاتين"},
                                            {"s": "OANDA:XPDUSD", "d": "البلاديوم"}
                                        ]
                                    }
                                ]
                            }
                            </script>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل الأخبار المالية المباشرة بنجاح');

            // تأثيرات بصرية متقدمة عند التحميل
            initializeAnimations();
            setupTabInteractions();
            addLoadingEffects();
        });

        // إعداد القائمة المنسدلة للأدوات
        const toolsDropdown = document.getElementById('toolsDropdown');
        const dropdownMenu = toolsDropdown?.nextElementSibling;

        if (toolsDropdown && dropdownMenu) {
            // إظهار القائمة عند hover
            toolsDropdown.parentElement.addEventListener('mouseenter', function() {
                dropdownMenu.classList.add('show');
            });

            // إخفاء القائمة عند مغادرة المنطقة
            toolsDropdown.parentElement.addEventListener('mouseleave', function() {
                dropdownMenu.classList.remove('show');
            });

            // منع إغلاق القائمة عند النقر عليها
            dropdownMenu.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        function initializeAnimations() {
            // تأثير تحميل العنوان الرئيسي
            const header = document.querySelector('.header');
            header.style.opacity = '0';
            header.style.transform = 'translateY(-20px)';
            header.style.transition = 'all 0.6s ease';

            setTimeout(() => {
                header.style.opacity = '1';
                header.style.transform = 'translateY(0)';
            }, 200);

            // تأثير تحميل التبويبات
            const tabsContainer = document.querySelector('.tabs-container');
            tabsContainer.style.opacity = '0';
            tabsContainer.style.transform = 'translateY(20px)';
            tabsContainer.style.transition = 'all 0.5s ease';

            setTimeout(() => {
                tabsContainer.style.opacity = '1';
                tabsContainer.style.transform = 'translateY(0)';
            }, 400);

            // تأثير تحميل البطاقات
            const containers = document.querySelectorAll('.widget-container');
            containers.forEach((container, index) => {
                container.style.opacity = '0';
                container.style.transform = 'translateY(30px)';
                container.style.transition = 'all 0.6s ease';

                setTimeout(() => {
                    container.style.opacity = '1';
                    container.style.transform = 'translateY(0)';
                }, 600 + (index * 150));
            });
        }

        function setupTabInteractions() {
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // تأثير تحميل المحتوى الجديد
                    const targetId = this.getAttribute('data-bs-target');
                    const targetPane = document.querySelector(targetId);

                    if (targetPane) {
                        setTimeout(() => {
                            const widgetContainer = targetPane.querySelector('.widget-container');
                            if (widgetContainer) {
                                widgetContainer.classList.add('loading');
                                setTimeout(() => {
                                    widgetContainer.classList.remove('loading');
                                }, 1000);
                            }
                        }, 100);
                    }
                });
            });
        }

        function addLoadingEffects() {
            // مراقبة تحميل ويدجت TradingView
            const widgets = document.querySelectorAll('.tradingview-widget-container');

            widgets.forEach(widget => {
                const container = widget.closest('.widget-container');
                if (container) {
                    container.classList.add('loading');

                    // إزالة تأثير التحميل بعد فترة
                    setTimeout(() => {
                        container.classList.remove('loading');
                    }, 3000);
                }
            });
        }



        // إضافة مؤشر الحالة المباشرة
        function addLiveIndicator() {
            const indicators = document.querySelectorAll('.widget-title');

            indicators.forEach(indicator => {
                const liveIcon = document.createElement('span');
                liveIcon.innerHTML = '<i class="fas fa-circle" style="color: #4ade80; font-size: 0.6rem; margin-right: 8px; animation: pulse 2s infinite;"></i>';
                liveIcon.style.cssText = 'font-size: 0.8rem; color: #4ade80; font-weight: 500;';
                indicator.appendChild(liveIcon);
            });
        }

        // تشغيل مؤشر الحالة المباشرة بعد التحميل
        setTimeout(addLiveIndicator, 2000);
    </script>
</body>

</html>

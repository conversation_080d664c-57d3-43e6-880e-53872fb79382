🚀 تشغيل سريع لموقع jorinvforex

═══════════════════════════════════════════════════════════

📥 الخطوة 1: تحميل XAMPP
الرابط: https://www.apachefriends.org/download.html
اختر: XAMPP for Windows (أو نظام التشغيل الخاص بك)

═══════════════════════════════════════════════════════════

📁 الخطوة 2: نسخ الملفات
1. ثبّت XAMPP في: C:\xampp
2. اذهب إلى: C:\xampp\htdocs
3. أنشئ مجلد: jorinvforex
4. انسخ جميع ملفات المشروع إلى المجلد

═══════════════════════════════════════════════════════════

⚡ الخطوة 3: تشغيل الخوادم
1. افتح XAMPP Control Panel
2. اضغط Start بجانب Apache
3. اضغط Start بجانب MySQL
4. تأكد من اللون الأخضر

═══════════════════════════════════════════════════════════

🗄️ الخطوة 4: إعداد قاعدة البيانات
افتح في المتصفح: http://localhost/jorinvforex/setup-database.php

الخطوة 1 - اختبار الاتصال:
خادم قاعدة البيانات: localhost
المنفذ: 3306
اسم المستخدم: root
كلمة المرور: (فارغة)

الخطوة 2 - إنشاء قاعدة البيانات:
اسم قاعدة البيانات: jorinvforex

الخطوة 3 - استيراد الهيكل:
اضغط "استيراد الهيكل"

الخطوة 4 - إنشاء مستخدم أدمن:
اسم المدير: مدير النظام
البريد الإلكتروني: <EMAIL>
كلمة المرور: Admin123456

الخطوة 5 - اكتمال الإعداد:
🎉 تم!

═══════════════════════════════════════════════════════════

🌐 الخطوة 5: اختبار الموقع

الصفحة الرئيسية:
http://localhost/jorinvforex/

تسجيل الدخول:
http://localhost/jorinvforex/login-sql.php

إنشاء حساب:
http://localhost/jorinvforex/register-sql.php

═══════════════════════════════════════════════════════════

🧪 حسابات تجريبية للاختبار:

👑 مدير النظام:
البريد: <EMAIL>
كلمة المرور: Admin123456

📊 محلل مالي:
البريد: <EMAIL>
كلمة المرور: Analyst123456

👤 مشترك:
البريد: <EMAIL>
كلمة المرور: Subscriber123456

═══════════════════════════════════════════════════════════

🔧 حل المشاكل الشائعة:

❌ الموقع لا يفتح:
- تأكد من تشغيل Apache في XAMPP
- تحقق من الرابط: http://localhost/jorinvforex/

❌ خطأ في قاعدة البيانات:
- تأكد من تشغيل MySQL في XAMPP
- أعد تشغيل setup-database.php

❌ صفحة غير موجودة:
- تأكد من نسخ الملفات في المجلد الصحيح
- المسار: C:\xampp\htdocs\jorinvforex\

═══════════════════════════════════════════════════════════

✅ قائمة مراجعة:
□ تم تثبيت XAMPP
□ تم نسخ ملفات المشروع
□ Apache يعمل (أخضر)
□ MySQL يعمل (أخضر)
□ تم إعداد قاعدة البيانات
□ الموقع يفتح
□ تسجيل الدخول يعمل

═══════════════════════════════════════════════════════════

🎉 تهانينا! موقع jorinvforex يعمل الآن!

للمساعدة: راجع ملف START_WEBSITE.md

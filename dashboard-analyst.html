<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المحلل | منصة المحللين الماليين</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --border-light: #e9ecef;
            --shadow-light: rgba(0, 51, 102, 0.1);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background-color: #f8f9fa;
            color: var(--text-dark);
            line-height: 1.6;
        }

        /* Sidebar */
        .sidebar {
            background: var(--primary-white);
            box-shadow: 2px 0 10px var(--shadow-light);
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-light);
            text-align: center;
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 1rem;
        }

        .sidebar-title {
            color: var(--primary-blue);
            font-size: 1.2rem;
            font-weight: 700;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            color: var(--text-dark);
            padding: 12px 1.5rem;
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(102, 204, 255, 0.1);
            color: var(--primary-blue);
            border-right-color: var(--accent-blue);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            transition: margin-right 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* Header */
        .main-header {
            background: var(--primary-white);
            box-shadow: 0 2px 10px var(--shadow-light);
            padding: 1rem 2rem;
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--text-dark);
            cursor: pointer;
            display: none;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid var(--accent-blue);
        }

        /* Dashboard Cards */
        .dashboard-section {
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px var(--shadow-light);
            border: 1px solid var(--border-light);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--primary-white);
            margin-bottom: 1rem;
        }

        .stat-icon.blue {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
        }

        .stat-icon.green {
            background: linear-gradient(45deg, var(--success-color), #20c997);
        }

        .stat-icon.orange {
            background: linear-gradient(45deg, var(--warning-color), #fd7e14);
        }

        .stat-icon.red {
            background: linear-gradient(45deg, var(--danger-color), #e83e8c);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Content Cards */
        .content-card {
            background: var(--primary-white);
            border-radius: 15px;
            box-shadow: 0 5px 15px var(--shadow-light);
            border: 1px solid var(--border-light);
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .card-title {
            color: var(--primary-blue);
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Buttons */
        .btn-primary-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .btn-secondary-custom {
            background: transparent;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            padding: 8px 18px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary-custom:hover {
            background: var(--primary-blue);
            color: var(--primary-white);
        }

        /* Loading */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
            flex-direction: column;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-light);
            border-top: 4px solid var(--accent-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .dashboard-section {
                padding: 1rem;
            }
            
            .main-header {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <img src="assets/img/logo.png" alt="فوركس الأردن" class="sidebar-logo">
            <div class="sidebar-title">لوحة تحكم المحلل</div>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>الرئيسية</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#posts" class="nav-link" data-section="posts">
                    <i class="fas fa-edit"></i>
                    <span>منشوراتي</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#subscribers" class="nav-link" data-section="subscribers">
                    <i class="fas fa-users"></i>
                    <span>المشتركين</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#requests" class="nav-link" data-section="requests">
                    <i class="fas fa-user-plus"></i>
                    <span>طلبات الاشتراك</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#chat" class="nav-link" data-section="chat">
                    <i class="fas fa-comments"></i>
                    <span>المحادثات</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#profile" class="nav-link" data-section="profile">
                    <i class="fas fa-user-edit"></i>
                    <span>الملف الشخصي</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link" onclick="signOut()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="main-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title" id="pageTitle">لوحة التحكم</h1>
            </div>

            <div class="header-right">
                <div class="user-menu">
                    <img src="assets/img/default-avatar.png" alt="الصورة الشخصية" class="user-avatar" id="userAvatar">
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="dashboard-section">
            <!-- Dashboard Section -->
            <div id="dashboardSection" class="content-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon blue">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="stat-value" id="totalPosts">0</div>
                        <div class="stat-label">إجمالي المنشورات</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon green">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value" id="totalSubscribers">0</div>
                        <div class="stat-label">المشتركين النشطين</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon orange">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-value" id="pendingRequests">0</div>
                        <div class="stat-label">طلبات الاشتراك</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon red">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-value" id="totalViews">0</div>
                        <div class="stat-label">مشاهدات المنشورات</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">إجراءات سريعة</h3>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-3 flex-wrap">
                            <a href="#" class="btn-primary-custom" onclick="showSection('posts')">
                                <i class="fas fa-plus"></i>
                                منشور جديد
                            </a>
                            <a href="#" class="btn-secondary-custom" onclick="showSection('requests')">
                                <i class="fas fa-user-check"></i>
                                مراجعة الطلبات
                            </a>
                            <a href="#" class="btn-secondary-custom" onclick="showSection('profile')">
                                <i class="fas fa-user-edit"></i>
                                تحديث الملف الشخصي
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Posts -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">آخر المنشورات</h3>
                        <a href="#" class="btn-secondary-custom" onclick="showSection('posts')">
                            <i class="fas fa-eye"></i>
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        <div id="recentPosts" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المنشورات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Posts Section -->
            <div id="postsSection" class="content-section" style="display: none;">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">إدارة المنشورات</h3>
                        <button class="btn-primary-custom" onclick="showNewPostModal()">
                            <i class="fas fa-plus"></i>
                            منشور جديد
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="postsList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المنشورات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other sections will be loaded dynamically -->
            <div id="subscribersSection" class="content-section" style="display: none;">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">المشتركين النشطين</h3>
                    </div>
                    <div class="card-body">
                        <div id="subscribersList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المشتركين...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="requestsSection" class="content-section" style="display: none;">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">طلبات الاشتراك</h3>
                    </div>
                    <div class="card-body">
                        <div id="requestsList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل الطلبات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="chatSection" class="content-section" style="display: none;">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">المحادثات</h3>
                    </div>
                    <div class="card-body">
                        <div id="chatList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المحادثات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="profileSection" class="content-section" style="display: none;">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">الملف الشخصي</h3>
                    </div>
                    <div class="card-body">
                        <div id="profileForm" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل البيانات...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase Configuration -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "jorinvforex.firebaseapp.com",
            projectId: "jorinvforex",
            storageBucket: "jorinvforex.appspot.com",
            messagingSenderId: "your-sender-id",
            appId: "your-app-id"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();
        const storage = firebase.storage();
    </script>

    <!-- Dashboard Scripts -->
    <script src="js/app.js"></script>
    <script src="js/dashboard-analyst.js"></script>
</body>
</html>

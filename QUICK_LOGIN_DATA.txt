🔑 بيانات تسجيل الدخول السريعة - مشروع jorinvforex

═══════════════════════════════════════════════════════════

👑 حساب المدير (Admin)
البريد الإلكتروني: <EMAIL>
كلمة المرور: Admin123456

═══════════════════════════════════════════════════════════

📊 حساب المحلل المالي (Analyst)  
البريد الإلكتروني: <EMAIL>
كلمة المرور: Analyst123456

═══════════════════════════════════════════════════════════

👤 حساب المشترك (Subscriber)
البريد الإلكتروني: <EMAIL>
كلمة المرور: Subscriber123456

═══════════════════════════════════════════════════════════

🚀 روابط سريعة:
- تسجيل الدخول: login.html
- إنشاء حسابات تجريبية: create-test-accounts.html
- الصفحة الرئيسية: index-new.html

═══════════════════════════════════════════════════════════

📋 خطوات الاختبار السريع:

1. افتح create-test-accounts.html
2. اضغط "إنشاء جميع الحسابات التجريبية"
3. اذهب إلى login.html
4. جرب تسجيل الدخول بأي من الحسابات أعلاه
5. اختبر الميزات المختلفة

═══════════════════════════════════════════════════════════

⚠️ ملاحظة: هذه حسابات تجريبية للاختبار فقط!

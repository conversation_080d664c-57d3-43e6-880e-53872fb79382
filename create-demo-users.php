<?php
/**
 * إنشاء المستخدمين التجريبيين - منصة المحللين الماليين
 * jorinvforex Demo Users Creation
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// تضمين الملفات المطلوبة
require_once 'config/database.php';
require_once 'includes/auth.php';

$message = '';
$error = '';

// إنشاء المستخدمين التجريبيين
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_demo_users'])) {
    try {
        $db = getDB();
        $auth = getAuth();
        
        // المستخدمين التجريبيين
        $demoUsers = [
            [
                'name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'password' => 'Admin123456',
                'role' => 'admin',
                'bio' => 'مدير منصة المحللين الماليين'
            ],
            [
                'name' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'password' => 'Analyst123456',
                'role' => 'analyst',
                'bio' => 'محلل مالي معتمد مع خبرة 5 سنوات في أسواق الفوركس والأسهم',
                'school' => 'جامعة الأردن - كلية الاقتصاد',
                'experience_years' => 5,
                'specialization' => 'تحليل الفوركس والأسهم'
            ],
            [
                'name' => 'سارة أحمد',
                'email' => '<EMAIL>',
                'password' => 'Subscriber123456',
                'role' => 'subscriber',
                'bio' => 'مستثمرة مهتمة بأسواق المال والعملات'
            ]
        ];
        
        $created = 0;
        $skipped = 0;
        
        foreach ($demoUsers as $userData) {
            // التحقق من وجود المستخدم
            $existingUser = $db->selectOne("SELECT id FROM users WHERE email = ?", [$userData['email']]);
            
            if ($existingUser) {
                $skipped++;
                continue;
            }
            
            // إنشاء المستخدم
            $result = $auth->register($userData);
            if ($result['success']) {
                $created++;
            }
        }
        
        $message = "تم إنشاء {$created} مستخدم جديد. تم تخطي {$skipped} مستخدم موجود بالفعل.";
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء المستخدمين التجريبيين | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .container-custom {
            max-width: 800px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .user-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--accent-blue);
        }

        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <div class="header">
            <h1><i class="fas fa-users"></i> إنشاء المستخدمين التجريبيين</h1>
            <p>إنشاء حسابات تجريبية لاختبار المنصة</p>
        </div>

        <div class="content">
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <h3>المستخدمين التجريبيين:</h3>

            <div class="user-card">
                <h5><i class="fas fa-crown text-warning"></i> مدير النظام</h5>
                <p><strong>البريد:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> Admin123456</p>
                <p><strong>الصلاحيات:</strong> إدارة كاملة للنظام</p>
            </div>

            <div class="user-card">
                <h5><i class="fas fa-chart-line text-primary"></i> محلل مالي</h5>
                <p><strong>البريد:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> Analyst123456</p>
                <p><strong>الصلاحيات:</strong> إنشاء تحليلات، إدارة مشتركين</p>
            </div>

            <div class="user-card">
                <h5><i class="fas fa-user text-info"></i> مشترك</h5>
                <p><strong>البريد:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> Subscriber123456</p>
                <p><strong>الصلاحيات:</strong> تصفح التحليلات، طلب اشتراك</p>
            </div>

            <form method="POST" class="text-center mt-4">
                <button type="submit" name="create_demo_users" class="btn btn-custom btn-lg">
                    <i class="fas fa-plus"></i> إنشاء المستخدمين التجريبيين
                </button>
            </form>

            <div class="text-center mt-4">
                <a href="login-sql.php" class="btn btn-outline-primary me-2">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </a>
                <a href="index-sql.php" class="btn btn-outline-secondary">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>

            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle"></i> ملاحظات مهمة:</h6>
                <ul class="mb-0">
                    <li>هذه حسابات تجريبية للاختبار فقط</li>
                    <li>يمكن حذفها بعد انتهاء الاختبار</li>
                    <li>لا تستخدمها في البيئة الحقيقية</li>
                    <li>غيّر كلمات المرور في الإنتاج</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

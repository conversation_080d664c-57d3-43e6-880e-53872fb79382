<?php
/**
 * صفحة التسجيل - منصة المحللين الماليين
 * jorinvforex Registration Page with SQL Database
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// تضمين الملفات المطلوبة
require_once 'config/database.php';
require_once 'includes/auth.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    $user = getCurrentUser();
    header("Location: dashboard-{$user['role']}.php");
    exit;
}

$error = '';
$success = '';

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'name' => sanitize($_POST['name'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? '',
        'role' => sanitize($_POST['role'] ?? 'subscriber'),
        'phone' => sanitize($_POST['phone'] ?? ''),
        'bio' => sanitize($_POST['bio'] ?? ''),
        'school' => sanitize($_POST['school'] ?? ''),
        'theme_color' => sanitize($_POST['theme_color'] ?? '#66CCFF')
    ];
    
    // التحقق من تطابق كلمات المرور
    if ($data['password'] !== $data['confirm_password']) {
        $error = 'كلمات المرور غير متطابقة';
    } else {
        $result = getAuth()->register($data);
        
        if ($result['success']) {
            $success = $result['message'] . ' يمكنك الآن تسجيل الدخول.';
        } else {
            $error = $result['message'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد | منصة المحللين الماليين</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .register-container {
            max-width: 600px;
            margin: 0 auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
            overflow: hidden;
        }

        .register-header {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            color: var(--primary-white);
            padding: 2rem;
            text-align: center;
        }

        .register-content {
            padding: 2rem;
        }

        .role-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .role-option {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .role-option:hover {
            border-color: var(--accent-blue);
            background: rgba(102, 204, 255, 0.1);
        }

        .role-option.active {
            border-color: var(--accent-blue);
            background: rgba(102, 204, 255, 0.1);
            color: var(--primary-blue);
        }

        .role-option i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .role-option h5 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .role-option p {
            font-size: 0.9rem;
            color: var(--text-light);
            margin: 0;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: var(--text-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 0.2rem rgba(102, 204, 255, 0.25);
            background: var(--primary-white);
        }

        .btn-register {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .analyst-fields {
            display: none;
            background: rgba(102, 204, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .analyst-fields.show {
            display: block;
        }

        .color-picker {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 0.5rem;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
        }

        .color-option.active {
            border-color: var(--primary-blue);
            transform: scale(1.2);
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }

        .strength-weak { color: var(--danger-color); }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: var(--success-color); }

        @media (max-width: 576px) {
            .role-selector {
                grid-template-columns: 1fr;
            }
            
            .register-container {
                margin: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h1>
            <p>انضم إلى منصة المحللين الماليين</p>
        </div>

        <div class="register-content">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <div class="mt-2">
                        <a href="login-sql.php" class="btn btn-sm btn-success">تسجيل الدخول الآن</a>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" id="registerForm">
                <!-- اختيار نوع الحساب -->
                <div class="form-group">
                    <label class="form-label">نوع الحساب</label>
                    <div class="role-selector">
                        <div class="role-option active" data-role="subscriber">
                            <i class="fas fa-users"></i>
                            <h5>مشترك</h5>
                            <p>للمستثمرين الباحثين عن التحليلات</p>
                        </div>
                        <div class="role-option" data-role="analyst">
                            <i class="fas fa-chart-line"></i>
                            <h5>محلل مالي</h5>
                            <p>للمحللين المحترفين</p>
                        </div>
                    </div>
                    <input type="hidden" name="role" id="selectedRole" value="subscriber">
                </div>

                <!-- البيانات الأساسية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name" class="form-label">الاسم الكامل *</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                   required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password" class="form-label">كلمة المرور *</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   required>
                            <div class="password-strength" id="passwordStrength"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" 
                           class="form-control" 
                           id="phone" 
                           name="phone" 
                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                </div>

                <div class="form-group">
                    <label for="bio" class="form-label">نبذة شخصية</label>
                    <textarea class="form-control" 
                              id="bio" 
                              name="bio" 
                              rows="3" 
                              placeholder="اكتب نبذة مختصرة عن نفسك..."><?php echo htmlspecialchars($_POST['bio'] ?? ''); ?></textarea>
                </div>

                <!-- حقول خاصة بالمحللين -->
                <div class="analyst-fields" id="analystFields">
                    <h6><i class="fas fa-graduation-cap"></i> معلومات إضافية للمحللين</h6>
                    
                    <div class="form-group">
                        <label for="school" class="form-label">المؤسسة التعليمية أو الشركة</label>
                        <input type="text" 
                               class="form-control" 
                               id="school" 
                               name="school" 
                               value="<?php echo htmlspecialchars($_POST['school'] ?? ''); ?>"
                               placeholder="مثال: جامعة الأردن - كلية الاقتصاد">
                    </div>
                </div>

                <!-- اختيار لون الثيم -->
                <div class="form-group">
                    <label class="form-label">لون الثيم المفضل</label>
                    <div class="color-picker">
                        <div class="color-option active" 
                             style="background: #66CCFF;" 
                             data-color="#66CCFF"></div>
                        <div class="color-option" 
                             style="background: #FF6B6B;" 
                             data-color="#FF6B6B"></div>
                        <div class="color-option" 
                             style="background: #4ECDC4;" 
                             data-color="#4ECDC4"></div>
                        <div class="color-option" 
                             style="background: #45B7D1;" 
                             data-color="#45B7D1"></div>
                        <div class="color-option" 
                             style="background: #96CEB4;" 
                             data-color="#96CEB4"></div>
                        <div class="color-option" 
                             style="background: #FFEAA7;" 
                             data-color="#FFEAA7"></div>
                    </div>
                    <input type="hidden" name="theme_color" id="selectedColor" value="#66CCFF">
                </div>

                <button type="submit" class="btn btn-register">
                    <i class="fas fa-user-plus"></i>
                    إنشاء الحساب
                </button>
            </form>
        </div>

        <div class="login-footer" style="text-align: center; padding: 1rem 2rem 2rem; border-top: 1px solid #e9ecef;">
            <p>لديك حساب بالفعل؟ <a href="login-sql.php" style="color: var(--accent-blue); text-decoration: none; font-weight: 600;">تسجيل الدخول</a></p>
            <p><a href="index-sql.php" style="color: var(--accent-blue); text-decoration: none; font-weight: 600;">العودة للصفحة الرئيسية</a></p>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // اختيار نوع الحساب
        document.querySelectorAll('.role-option').forEach(option => {
            option.addEventListener('click', function() {
                // إزالة التحديد من جميع الخيارات
                document.querySelectorAll('.role-option').forEach(opt => opt.classList.remove('active'));
                
                // تحديد الخيار المختار
                this.classList.add('active');
                
                // تحديث القيمة المخفية
                const role = this.dataset.role;
                document.getElementById('selectedRole').value = role;
                
                // إظهار/إخفاء حقول المحللين
                const analystFields = document.getElementById('analystFields');
                if (role === 'analyst') {
                    analystFields.classList.add('show');
                } else {
                    analystFields.classList.remove('show');
                }
            });
        });
        
        // اختيار لون الثيم
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', function() {
                // إزالة التحديد من جميع الألوان
                document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('active'));
                
                // تحديد اللون المختار
                this.classList.add('active');
                
                // تحديث القيمة المخفية
                document.getElementById('selectedColor').value = this.dataset.color;
            });
        });
        
        // فحص قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            let strength = 0;
            let message = '';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    message = '<span class="strength-weak"><i class="fas fa-times"></i> ضعيفة جداً</span>';
                    break;
                case 2:
                    message = '<span class="strength-weak"><i class="fas fa-exclamation"></i> ضعيفة</span>';
                    break;
                case 3:
                    message = '<span class="strength-medium"><i class="fas fa-check"></i> متوسطة</span>';
                    break;
                case 4:
                case 5:
                    message = '<span class="strength-strong"><i class="fas fa-check-circle"></i> قوية</span>';
                    break;
            }
            
            strengthDiv.innerHTML = message;
        });
        
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
                this.style.borderColor = 'var(--danger-color)';
            } else {
                this.setCustomValidity('');
                this.style.borderColor = '';
            }
        });
        
        // معالجة إرسال النموذج
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                return false;
            }
            
            if (password.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return false;
            }
        });
        
        // التركيز على حقل الاسم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('name').focus();
        });
    </script>
</body>
</html>

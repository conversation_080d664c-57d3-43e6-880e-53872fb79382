// ===== إعداد Firebase لمشروع jorinvforex =====

// معلومات المشروع
const PROJECT_INFO = {
    name: "jorinvforex",
    id: "jorinvforex", 
    number: "862703765675",
    region: "us-central1" // المنطقة الافتراضية
};

// إعداد Firebase (يحتاج تحديث API Key و App ID)
const firebaseConfig = {
    apiKey: "YOUR_WEB_API_KEY_HERE", // احصل عليه من Firebase Console
    authDomain: "jorinvforex.firebaseapp.com",
    projectId: "jorinvforex",
    storageBucket: "jorinvforex.appspot.com", 
    messagingSenderId: "862703765675",
    appId: "1:862703765675:web:YOUR_APP_ID_HERE" // احصل عليه من Firebase Console
};

// تهيئة Firebase
function initializeFirebase() {
    try {
        // التحقق من وجود Firebase
        if (typeof firebase === 'undefined') {
            throw new Error('Firebase SDK غير محمل');
        }

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);
        
        // تهيئة الخدمات
        window.auth = firebase.auth();
        window.db = firebase.firestore();
        window.storage = firebase.storage();
        
        // إعداد المنطقة الزمنية
        if (window.db) {
            window.db.settings({
                timestampsInSnapshots: true
            });
        }
        
        console.log('✅ تم تهيئة Firebase بنجاح لمشروع jorinvforex');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة Firebase:', error);
        return false;
    }
}

// دالة للحصول على إعدادات Firebase
function getFirebaseConfig() {
    return firebaseConfig;
}

// دالة للتحقق من حالة Firebase
function checkFirebaseStatus() {
    const status = {
        initialized: false,
        auth: false,
        firestore: false,
        storage: false,
        errors: []
    };
    
    try {
        if (firebase.apps.length > 0) {
            status.initialized = true;
            
            if (firebase.auth()) {
                status.auth = true;
            }
            
            if (firebase.firestore()) {
                status.firestore = true;
            }
            
            if (firebase.storage()) {
                status.storage = true;
            }
        }
    } catch (error) {
        status.errors.push(error.message);
    }
    
    return status;
}

// دالة لإنشاء مستخدم أدمن أول
async function createFirstAdmin(email, password, name) {
    try {
        // إنشاء المستخدم
        const userCredential = await auth.createUserWithEmailAndPassword(email, password);
        const user = userCredential.user;
        
        // تحديث الملف الشخصي
        await user.updateProfile({
            displayName: name
        });
        
        // إنشاء وثيقة المستخدم في Firestore
        await db.collection('users').doc(user.uid).set({
            name: name,
            email: email,
            role: 'admin',
            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
            profilePicURL: '',
            coverPicURL: '',
            themeColor: '#66CCFF'
        });
        
        console.log('✅ تم إنشاء مستخدم الأدمن الأول بنجاح');
        return { success: true, user: user };
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء مستخدم الأدمن:', error);
        return { success: false, error: error.message };
    }
}

// دالة لإنشاء بيانات تجريبية
async function createSampleData() {
    try {
        console.log('🔄 بدء إنشاء البيانات التجريبية...');
        
        // إنشاء محلل تجريبي
        const analystData = {
            name: "أحمد محمد - محلل مالي",
            email: "<EMAIL>",
            role: "analyst",
            school: "جامعة الأردن - كلية الاقتصاد",
            description: "محلل مالي معتمد مع خبرة 5 سنوات في أسواق الفوركس والأسهم",
            profilePicURL: "",
            coverPicURL: "",
            themeColor: "#66CCFF",
            acceptedSubscribers: [],
            createdAt: firebase.firestore.FieldValue.serverTimestamp()
        };
        
        const analystRef = await db.collection('users').add(analystData);
        console.log('✅ تم إنشاء محلل تجريبي');
        
        // إنشاء مشترك تجريبي
        const subscriberData = {
            name: "سارة أحمد - مستثمرة",
            email: "<EMAIL>", 
            role: "subscriber",
            profilePicURL: "",
            coverPicURL: "",
            themeColor: "#66CCFF",
            createdAt: firebase.firestore.FieldValue.serverTimestamp()
        };
        
        const subscriberRef = await db.collection('users').add(subscriberData);
        console.log('✅ تم إنشاء مشترك تجريبي');
        
        // إنشاء منشور تجريبي
        const postData = {
            analystId: analystRef.id,
            title: "تحليل زوج EUR/USD - فرصة شراء قوية",
            description: "بناءً على التحليل الفني والأساسي، نتوقع ارتفاع زوج اليورو دولار خلال الأسبوع القادم...",
            imageURL: "",
            tradingViewLink: "https://tradingview.com/chart/example",
            isPaidContent: true,
            isFreeNow: false,
            createdAt: firebase.firestore.FieldValue.serverTimestamp()
        };
        
        await db.collection('posts').add(postData);
        console.log('✅ تم إنشاء منشور تجريبي');
        
        // إنشاء طلب اشتراك تجريبي
        const subscriptionData = {
            analystId: analystRef.id,
            subscriberId: subscriberRef.id,
            subscriberName: subscriberData.name,
            subscriberEmail: subscriberData.email,
            status: "pending",
            paymentConfirmed: false,
            createdAt: firebase.firestore.FieldValue.serverTimestamp()
        };
        
        await db.collection('subscriptions').add(subscriptionData);
        console.log('✅ تم إنشاء طلب اشتراك تجريبي');
        
        console.log('🎉 تم إنشاء جميع البيانات التجريبية بنجاح!');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
        return false;
    }
}

// تصدير الدوال للاستخدام العام
if (typeof window !== 'undefined') {
    window.initializeFirebase = initializeFirebase;
    window.getFirebaseConfig = getFirebaseConfig;
    window.checkFirebaseStatus = checkFirebaseStatus;
    window.createFirstAdmin = createFirstAdmin;
    window.createSampleData = createSampleData;
    window.PROJECT_INFO = PROJECT_INFO;
}

// تهيئة تلقائية عند تحميل الملف
document.addEventListener('DOMContentLoaded', function() {
    initializeFirebase();
});

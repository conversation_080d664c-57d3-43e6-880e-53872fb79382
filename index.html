<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة المحللين الماليين | فوركس الأردن</title>
    <meta http-equiv="refresh" content="0; url=index-new.html">
    <link rel="canonical" href="index-new.html">
    <style>
        body {
            font-family: '<PERSON>jawal', Arial, sans-serif;
            background: linear-gradient(135deg, #FFFFFF 0%, #99CCFF 100%);
            color: #003366;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
        }
        .redirect-message {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            border: 1px solid rgba(102, 204, 255, 0.3);
            padding: 40px;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0, 51, 102, 0.1);
        }
        .logo {
            width: 100px;
            height: 100px;
            margin-bottom: 20px;
            border-radius: 50%;
        }
        h1 {
            color: #003366;
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: 700;
        }
        p {
            color: #6c757d;
            margin-bottom: 20px;
            font-size: 1.1rem;
        }
        .loading {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 4px solid rgba(102, 204, 255, 0.3);
            border-radius: 50%;
            border-top-color: #66CCFF;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        a {
            color: #003366;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 20px;
            background: linear-gradient(45deg, #003366, #66CCFF);
            color: white;
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            transition: all 0.3s ease;
        }
        a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
        }
        .subtitle {
            color: #66CCFF;
            font-size: 1rem;
            margin-bottom: 20px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="redirect-message">
        <img src="assets/img/logo.png" alt="فوركس الأردن" class="logo">
        <h1>منصة المحللين الماليين</h1>
        <p class="subtitle">فوركس الأردن</p>
        <p>جاري تحويلك إلى المنصة الجديدة...</p>
        <div class="loading"></div>
        <p style="margin-top: 20px; font-size: 0.9rem; color: #6c757d;">
            إذا لم يتم التحويل تلقائياً،
            <a href="index-new.html">ادخل للمنصة</a>
        </p>
    </div>

    <script>
        // التحويل التلقائي بعد ثانيتين
        setTimeout(function() {
            window.location.href = 'index-new.html';
        }, 2000);
    </script>
</body>
</html>

// ===== تحسينات الأداء والتوافق =====

// فحص دعم المتصفح للميزات الحديثة
const browserSupport = {
    intersectionObserver: 'IntersectionObserver' in window,
    requestAnimationFrame: 'requestAnimationFrame' in window,
    localStorage: typeof(Storage) !== "undefined",
    touchEvents: 'ontouchstart' in window,
    reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
};

// تحسين الأداء للأجهزة الضعيفة
const devicePerformance = {
    isLowEnd: navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2,
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
    isTouch: browserSupport.touchEvents
};

// إعدادات الانيميشن حسب الجهاز
const animationSettings = {
    typewriterSpeed: devicePerformance.isLowEnd ? 50 : 20,
    transitionDuration: devicePerformance.isLowEnd ? '0.2s' : '0.4s',
    enableComplexAnimations: !devicePerformance.isLowEnd && !browserSupport.reducedMotion
};

// النص الأساسي
const aboutText = "في فوركس الأردن، نمتلك أكثر من 15 سنة خبرة في الأسواق المالية. نقدّم إشارات تداول دقيقة وتحليلات يومية لأزواج العملات، الكريبتو، المعادن، والمؤشرات. فريقنا يعمل 24/7 لرصد أفضل الفرص مع خطط إدارة رأس مال ذكية لتقليل المخاطر وزيادة الأرباح. انضم لآلاف المتداولين اللي حققوا أرباح مستدامة معنا. ابدأ تداولك بثقة مع فوركس الأردن.";

// عرض النص العادي بدون تأثير الآلة الكاتبة
function displayNormalText() {
    const element = document.getElementById('typed-about');
    if (!element) {
        if (document.readyState !== 'complete') {
            setTimeout(displayNormalText, 100);
            return;
        }
        console.warn('Typed element not found');
        return;
    }

    // إعداد العنصر وعرض النص مباشرة
    element.style.cssText = `
        text-align: right;
        direction: rtl;
        color: #ccd6f6;
        line-height: 1.8;
        font-size: 1.15rem;
        min-height: 120px;
        font-family: 'Tajawal', Arial, sans-serif;
        opacity: 1;
        transform: translateY(0);
    `;

    // عرض النص مباشرة
    element.innerHTML = aboutText;
    console.log('Normal text displayed successfully');
}

// تشغيل عرض النص العادي
function initNormalText() {
    // عرض فوري للنص
    displayNormalText();
    // تشغيل إضافي للتأكد
    setTimeout(displayNormalText, 100);
    setTimeout(displayNormalText, 500);
}

// تشغيل فوري عند تحميل الملف
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initNormalText);
} else {
    initNormalText();
}

// ===== تهيئة الصفحة الرئيسية =====
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM Content Loaded - Starting optimized main.js");

    // تشغيل عرض النص العادي
    initNormalText();

    // إعداد شاشة البداية المحسنة
    initSplashScreen();

    // إعداد مراقب التمرير المحسن
    initScrollObserver();

    // إعداد التفاعلات المحسنة
    initInteractions();

    // إعداد التنقل
    setTimeout(setupNavigation, 100);

    // إعداد تحسينات الأداء
    initPerformanceOptimizations();
});

// ===== إعداد شاشة البداية =====
function initSplashScreen() {
    const splashScreen = document.getElementById('splashScreen');
    const closeSplash = document.getElementById('closeSplash');

    if (!splashScreen) return;

    // دالة إغلاق محسنة
    function closeSplashScreen() {
        splashScreen.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        splashScreen.style.opacity = '0';
        splashScreen.style.transform = 'scale(0.95)';

        setTimeout(() => {
            splashScreen.style.display = 'none';
            // تحرير الذاكرة
            splashScreen.remove();
        }, 600);
    }

    // إغلاق يدوي
    if (closeSplash) {
        closeSplash.addEventListener('click', closeSplashScreen, { once: true });
    }

    // إغلاق تلقائي محسن
    const autoCloseDelay = devicePerformance.isMobile ? 8000 : 12000;
    setTimeout(() => {
        if (splashScreen && splashScreen.style.display !== 'none') {
            closeSplashScreen();
        }
    }, autoCloseDelay);

    // إغلاق عند النقر خارج المحتوى
    splashScreen.addEventListener('click', function(e) {
        if (e.target === splashScreen) {
            closeSplashScreen();
        }
    }, { once: true });
}

// ===== مراقب التمرير المحسن =====
function initScrollObserver() {
    if (!browserSupport.intersectionObserver) {
        // fallback للمتصفحات القديمة
        document.querySelectorAll('.service-card, .stat-item, .glass-card').forEach(el => {
            if (!el.closest('.about-image')) {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }
        });
        return;
    }

    const observerOptions = {
        threshold: devicePerformance.isMobile ? 0.05 : 0.1,
        rootMargin: devicePerformance.isMobile ? '0px 0px -30px 0px' : '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;

                // تطبيق الانيميشن حسب نوع الجهاز
                if (animationSettings.enableComplexAnimations) {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                } else {
                    element.style.opacity = '1';
                    element.style.transform = 'none';
                }

                observer.unobserve(element);
            }
        });
    }, observerOptions);

    // تطبيق المراقب على العناصر
    document.querySelectorAll('.service-card, .stat-item, .glass-card').forEach(el => {
        if (!el.closest('.about-image')) {
            el.style.opacity = '0';

            if (animationSettings.enableComplexAnimations) {
                el.style.transform = 'translateY(30px)';
                el.style.transition = `all ${animationSettings.transitionDuration} ease-out`;
            }

            observer.observe(el);
        }
    });
}

// ===== التفاعلات المحسنة =====
function initInteractions() {
    // تحسين تفاعلات الأزرار
    const buttons = document.querySelectorAll('.splash-btn, .hero-section a, .3d-plan-btn');

    buttons.forEach(btn => {
        // استخدام passive listeners للأداء
        if (devicePerformance.isTouch) {
            btn.addEventListener('touchstart', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                }
            }, { passive: true });

            btn.addEventListener('touchend', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            }, { passive: true });
        } else {
            btn.addEventListener('mouseenter', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(-5px) scale(1.05)';
                }
            });

            btn.addEventListener('mouseleave', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            });
        }
    });

    // تحسين تفاعلات البطاقات
    const cards = document.querySelectorAll('.service-card, .feature-card');

    cards.forEach(card => {
        if (devicePerformance.isTouch) {
            card.addEventListener('touchstart', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(-5px) scale(1.01)';
                }
            }, { passive: true });

            card.addEventListener('touchend', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            }, { passive: true });
        } else {
            card.addEventListener('mouseenter', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                }
            });

            card.addEventListener('mouseleave', function() {
                if (animationSettings.enableComplexAnimations) {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            });
        }
    });
}

console.log("About text loaded:", aboutText.substring(0, 50) + "...");

// تشغيل إضافي للتأكد من عرض النص
setTimeout(() => {
    const element = document.getElementById('typed-about');
    if (element && (!element.innerHTML || element.innerHTML.trim() === '')) {
        console.log("Fallback: Displaying normal text");
        element.innerHTML = aboutText;
        element.style.cssText = `
            text-align: right;
            direction: rtl;
            color: #ccd6f6;
            line-height: 1.8;
            font-size: 1.15rem;
            font-family: 'Tajawal', Arial, sans-serif;
            opacity: 1;
        `;
    }
}, 500);

// ===== تحسينات الأداء =====
function initPerformanceOptimizations() {
    // تحسين الخطوط
    if ('fonts' in document) {
        document.fonts.ready.then(() => {
            console.log('Fonts loaded');
        });
    }

    // تحسين الصور
    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }

    // تنظيف الذاكرة
    window.addEventListener('beforeunload', () => {
        // تنظيف المراقبين والمتغيرات
        console.log('Cleaning up resources');
    });
}

    // إضافة الأنماط المحسنة
    addOptimizedStyles();


// ===== إضافة الأنماط المحسنة =====
function addOptimizedStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* أنماط الآلة الكاتبة */
        #typed-about {
            min-height: 100px;
            max-width: 85%;
            width: 85%;
            line-height: 1.8;
            font-size: 1.1rem;
            color: #ccd6f6;
            text-align: right;
            direction: rtl;
            font-family: 'Tajawal', Arial, sans-serif;
            padding: 10px 0;
            position: relative;
            margin: 10px auto;
            display: block;
        }

        /* تم إزالة تأثيرات الآلة الكاتبة */

        /* تحسينات الأداء */
        .about-image,
        .about-image img,
        .about-image * {
            transform: none !important;
            transition: none !important;
            animation: none !important;
            opacity: 1 !important;
        }

        /* تأثيرات محسنة للأجهزة الضعيفة */
        @media (max-width: 768px) {
            .service-card:hover,
            .feature-card:hover {
                transform: translateY(-5px) scale(1.01) !important;
            }
        }
    `;
    document.head.appendChild(style);
}

// دالة إعداد التنقل
function setupNavigation() {
    // تحديث الـ active link في الـ navigation
    function updateActiveLink() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');

        let current = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            if (window.pageYOffset >= (sectionTop - 150)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    }

    // تشغيل التحديث عند التمرير
    window.addEventListener('scroll', updateActiveLink);

    // تشغيل التحديث عند تحميل الصفحة
    updateActiveLink();

    // إضافة تأثير smooth scroll للروابط
    const navLinks = document.querySelectorAll('a[href^="#"]');

    navLinks.forEach((anchor) => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');

            // تجاهل الروابط الفارغة أو التي تحتوي على # فقط
            if (!href || href === '#' || href === '#!') {
                return;
            }

            e.preventDefault();
            const target = document.querySelector(href);

            if (target) {
                // إغلاق الـ mobile menu إذا كان مفتوح
                const navbarCollapse = document.querySelector('.navbar-collapse');
                if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                    // استخدام Bootstrap API
                    const bsCollapse = bootstrap.Collapse.getInstance(navbarCollapse) || new bootstrap.Collapse(navbarCollapse);
                    bsCollapse.hide();
                }

                const offsetTop = target.offsetTop - 80; // تعديل للـ navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // تحسين تأثيرات الـ navbar عند التمرير ومؤشر التقدم
    const navbar = document.querySelector('.navbar');
    const scrollProgress = document.getElementById('scrollProgress');

    window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // تغيير شفافية الـ navbar حسب التمرير
        if (scrollTop > 100) {
            navbar.style.background = 'rgba(10, 25, 47, 0.95)';
            navbar.style.backdropFilter = 'blur(15px)';
        } else {
            navbar.style.background = 'rgba(10, 25, 47, 0.85)';
            navbar.style.backdropFilter = 'blur(10px)';
        }

        // تحديث مؤشر تقدم التمرير
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / scrollHeight) * 100;
        scrollProgress.style.width = scrollPercent + '%';
    });

    // إضافة تأثيرات تحميل سلسة
    window.addEventListener('load', function() {
        document.body.style.opacity = '1';
        document.body.style.transition = 'opacity 0.5s ease-in-out';
    });

    // تحسين الأداء للانيميشن
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    if (prefersReducedMotion.matches) {
        // تقليل الانيميشن للمستخدمين الذين يفضلون ذلك
        document.querySelectorAll('*').forEach(el => {
            el.style.animationDuration = '0.01ms !important';
            el.style.animationIterationCount = '1 !important';
            el.style.transitionDuration = '0.01ms !important';
        });
    }
}
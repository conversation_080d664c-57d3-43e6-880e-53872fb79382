RewriteEngine On
DirectoryIndex index.html

# إزالة امتداد .html من الروابط
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.html [L]

# إعادة توجيه الروابط التي تحتوي على .html إلى بدون امتداد
RewriteCond %{THE_REQUEST} /([^.]+)\.html [NC]
RewriteRule ^ /%1? [NC,L,R=301]

# التعامل مع مجلد tools
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^tools/([^\.]+)$ tools/$1.html [L]

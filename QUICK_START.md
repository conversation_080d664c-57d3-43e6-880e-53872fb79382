# 🚀 دليل البدء السريع - مشروع jorinvforex

## ⚡ 5 دقائق للتشغيل!

### الخطوة 1: الحصول على Web API Key (دقيقتان)

1. **اذهب إلى Firebase Console:**
   👉 [https://console.firebase.google.com/u/0/project/jorinvforex/settings/general](https://console.firebase.google.com/u/0/project/jorinvforex/settings/general)

2. **أضف Web App:**
   - اضغط على أيقونة `</>` في قسم "Your apps"
   - اسم التطبيق: `منصة المحللين الماليين`
   - ✅ فعّل Firebase Hosting (اختياري)
   - اضغط "Register app"

3. **انسخ الإعدادات:**
   ```javascript
   const firebaseConfig = {
     apiKey: "AIzaSy...", // انسخ هذا
     authDomain: "jorinvforex.firebaseapp.com",
     projectId: "jorinvforex",
     storageBucket: "jorinvforex.appspot.com", 
     messagingSenderId: "862703765675",
     appId: "1:862703765675:web:..." // وهذا
   };
   ```

### الخطوة 2: تفعيل الخدمات (دقيقة واحدة)

1. **Authentication:**
   👉 [https://console.firebase.google.com/u/0/project/jorinvforex/authentication/providers](https://console.firebase.google.com/u/0/project/jorinvforex/authentication/providers)
   - اضغط "Get started"
   - فعّل "Email/Password"

2. **Firestore:**
   👉 [https://console.firebase.google.com/u/0/project/jorinvforex/firestore](https://console.firebase.google.com/u/0/project/jorinvforex/firestore)
   - اضغط "Create database"
   - اختر "Start in production mode"
   - المنطقة: us-central1

3. **Storage:**
   👉 [https://console.firebase.google.com/u/0/project/jorinvforex/storage](https://console.firebase.google.com/u/0/project/jorinvforex/storage)
   - اضغط "Get started"
   - "Start in production mode"

### الخطوة 3: تحديث الإعدادات (دقيقتان)

1. **افتح setup-project.html في المتصفح**
2. **اتبع التعليمات لتحديث الإعدادات**
3. **أنشئ مستخدم أدمن أول**
4. **أنشئ بيانات تجريبية**

---

## 🎯 اختبار سريع

### 1. تسجيل الدخول كأدمن
- افتح `login.html`
- استخدم بيانات الأدمن التي أنشأتها

### 2. إنشاء حساب محلل
- افتح `register.html`
- اختر "محلل مالي"
- أكمل البيانات

### 3. إنشاء حساب مشترك
- افتح `register.html` في تبويب جديد
- اختر "مشترك"
- أكمل البيانات

### 4. اختبار طلب الاشتراك
- من حساب المشترك: ابحث عن المحلل وأرسل طلب اشتراك
- من حساب المحلل: اقبل طلب الاشتراك

---

## 📁 الملفات الرئيسية

| الملف | الوصف | الرابط |
|-------|--------|--------|
| `setup-project.html` | صفحة الإعداد الشاملة | [افتح](setup-project.html) |
| `index-new.html` | الصفحة الرئيسية | [افتح](index-new.html) |
| `login.html` | تسجيل الدخول | [افتح](login.html) |
| `register.html` | إنشاء حساب | [افتح](register.html) |

---

## 🔧 أدوات مساعدة

### Firebase CLI (للمطورين)
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# ربط المشروع
firebase use jorinvforex

# نشر القواعد
firebase deploy --only firestore:rules,storage
```

### تشغيل محلي
```bash
# خادم بسيط
python -m http.server 8000

# أو
npx serve .

# ثم افتح: http://localhost:8000/setup-project.html
```

---

## ❓ مشاكل شائعة

### "Firebase configuration not found"
**الحل:** تأكد من تحديث `apiKey` و `appId` في ملفات HTML

### "Missing or insufficient permissions"  
**الحل:** تأكد من تفعيل Firestore في Firebase Console

### "Storage bucket not found"
**الحل:** تأكد من تفعيل Storage في Firebase Console

---

## 📞 الدعم

- **الوثائق الكاملة:** [README.md](README.md)
- **دليل الإعداد:** [SETUP.md](SETUP.md)
- **الحصول على API Key:** [GET_API_KEY.md](GET_API_KEY.md)

---

## ✅ قائمة مراجعة سريعة

- [ ] حصلت على Web API Key من Firebase Console
- [ ] فعّلت Authentication, Firestore, Storage
- [ ] حدّثت الإعدادات في ملفات HTML
- [ ] أنشأت مستخدم أدمن أول
- [ ] اختبرت تسجيل الدخول
- [ ] أنشأت حسابات تجريبية (محلل + مشترك)
- [ ] اختبرت طلب الاشتراك
- [ ] المنصة تعمل بنجاح! 🎉

---

**🎊 مبروك! منصة المحللين الماليين جاهزة للاستخدام!**

للمساعدة أو الاستفسارات، راجع الوثائق أو تواصل مع فريق التطوير.

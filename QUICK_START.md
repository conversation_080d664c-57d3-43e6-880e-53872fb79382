# 🚀 التشغيل السريع - منصة jorinvforex

## ⚡ 5 خطوات فقط للتشغيل

### 1️⃣ تثبيت XAMPP
- حمّل من: https://www.apachefriends.org
- ثبّت وشغّل **Apache + MySQL**

### 2️⃣ نسخ الملفات
- انسخ ملفات المشروع إلى: `C:\xampp\htdocs\jorinvforex\`

### 3️⃣ إعداد قاعدة البيانات
- افتح: http://localhost/jorinvforex/setup-database.php
- اتبع الخطوات الخمس

### 4️⃣ إنشاء مستخدمين تجريبيين
- افتح: http://localhost/jorinvforex/create-demo-users.php
- اضغط "إنشاء المستخدمين التجريبيين"

### 5️⃣ اختبار المنصة
- افتح: http://localhost/jorinvforex/index-sql.php
- سجل دخول بأحد الحسابات التجريبية

---

## 🧪 حسابات تجريبية

### 👑 مدير
```
البريد: <EMAIL>
كلمة المرور: Admin123456
```

### 📊 محلل
```
البريد: <EMAIL>
كلمة المرور: Analyst123456
```

### 👤 مشترك
```
البريد: <EMAIL>
كلمة المرور: Subscriber123456
```

---

## 🔧 روابط مفيدة

- **فحص النظام:** [system-check.php](http://localhost/jorinvforex/system-check.php)
- **إعداد قاعدة البيانات:** [setup-database.php](http://localhost/jorinvforex/setup-database.php)
- **إنشاء مستخدمين:** [create-demo-users.php](http://localhost/jorinvforex/create-demo-users.php)
- **الصفحة الرئيسية:** [index-sql.php](http://localhost/jorinvforex/index-sql.php)

---

## ❌ حل المشاكل الشائعة

### "Database connection failed"
- تأكد من تشغيل MySQL في XAMPP
- أعد تشغيل setup-database.php

### "Page not found"
- تأكد من المسار: `C:\xampp\htdocs\jorinvforex\`
- تأكد من تشغيل Apache

### "Permission denied"
- شغّل XAMPP كمدير

---

## 🎉 تم!

**منصة jorinvforex جاهزة للاستخدام!**

استمتع بمنصة محللين ماليين قوية ومستقلة 🚀

// ===== سكريبت تحديث إعدادات Firebase لمشروع jorinvforex =====

// الإعدادات الجديدة (يجب تحديثها بالقيم الفعلية)
const NEW_FIREBASE_CONFIG = {
    apiKey: "YOUR_ACTUAL_API_KEY_HERE", // من Firebase Console
    authDomain: "jorinvforex.firebaseapp.com",
    projectId: "jorinvforex", 
    storageBucket: "jorinvforex.appspot.com",
    messagingSenderId: "862703765675",
    appId: "YOUR_ACTUAL_APP_ID_HERE" // من Firebase Console
};

// قائمة الملفات التي تحتاج تحديث
const FILES_TO_UPDATE = [
    'index-new.html',
    'login.html', 
    'register.html',
    'dashboard-analyst.html',
    'dashboard-subscriber.html'
];

// دالة لتحديث إعدادات Firebase في ملف HTML
function updateFirebaseConfigInFile(filename, newConfig) {
    console.log(`🔄 تحديث إعدادات Firebase في ${filename}...`);
    
    // هذا مثال على كيفية التحديث - يحتاج تنفيذ يدوي
    const configString = `
        const firebaseConfig = {
            apiKey: "${newConfig.apiKey}",
            authDomain: "${newConfig.authDomain}",
            projectId: "${newConfig.projectId}",
            storageBucket: "${newConfig.storageBucket}",
            messagingSenderId: "${newConfig.messagingSenderId}",
            appId: "${newConfig.appId}"
        };
    `;
    
    console.log(`✅ تم تحديث ${filename}`);
    return configString;
}

// دالة للتحقق من صحة الإعدادات
function validateFirebaseConfig(config) {
    const errors = [];
    
    if (!config.apiKey || config.apiKey === "YOUR_ACTUAL_API_KEY_HERE") {
        errors.push("❌ API Key مفقود أو لم يتم تحديثه");
    }
    
    if (!config.appId || config.appId === "YOUR_ACTUAL_APP_ID_HERE") {
        errors.push("❌ App ID مفقود أو لم يتم تحديثه");
    }
    
    if (!config.projectId || config.projectId !== "jorinvforex") {
        errors.push("❌ Project ID غير صحيح");
    }
    
    if (!config.messagingSenderId || config.messagingSenderId !== "862703765675") {
        errors.push("❌ Messaging Sender ID غير صحيح");
    }
    
    return errors;
}

// دالة لإنشاء ملف إعدادات مركزي
function generateCentralConfigFile(config) {
    return `
// ===== إعدادات Firebase المركزية لمشروع jorinvforex =====

const JORINVFOREX_CONFIG = {
    apiKey: "${config.apiKey}",
    authDomain: "${config.authDomain}",
    projectId: "${config.projectId}",
    storageBucket: "${config.storageBucket}",
    messagingSenderId: "${config.messagingSenderId}",
    appId: "${config.appId}"
};

// تهيئة Firebase
function initJorinvForexFirebase() {
    if (typeof firebase !== 'undefined') {
        firebase.initializeApp(JORINVFOREX_CONFIG);
        
        // تهيئة الخدمات
        window.auth = firebase.auth();
        window.db = firebase.firestore();
        window.storage = firebase.storage();
        
        console.log('✅ تم تهيئة Firebase لمشروع jorinvforex');
        return true;
    } else {
        console.error('❌ Firebase SDK غير محمل');
        return false;
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.JORINVFOREX_CONFIG = JORINVFOREX_CONFIG;
    window.initJorinvForexFirebase = initJorinvForexFirebase;
}

// تهيئة تلقائية
document.addEventListener('DOMContentLoaded', initJorinvForexFirebase);
`;
}

// دالة لإنشاء تعليمات التحديث
function generateUpdateInstructions() {
    return `
# تعليمات تحديث إعدادات Firebase

## الخطوة 1: الحصول على الإعدادات
1. اذهب إلى Firebase Console: https://console.firebase.google.com/u/0/project/jorinvforex/settings/general
2. في قسم "Your apps"، اضغط على أيقونة الويب </> 
3. أدخل اسم التطبيق: "منصة المحللين الماليين"
4. انسخ الإعدادات

## الخطوة 2: تحديث الملفات
استبدل الإعدادات في هذه الملفات:

${FILES_TO_UPDATE.map(file => `- ${file}`).join('\n')}

## الخطوة 3: البحث والاستبدال
ابحث عن:
\`\`\`javascript
const firebaseConfig = {
    apiKey: "your-api-key",
    // ... باقي الإعدادات
};
\`\`\`

واستبدلها بالإعدادات الجديدة من Firebase Console.

## الخطوة 4: التحقق
افتح setup-project.html واضغط "فحص حالة الإعداد"
`;
}

// دالة لإنشاء قائمة مراجعة
function generateChecklist() {
    return `
# قائمة مراجعة إعداد jorinvforex

## إعداد Firebase Console
- [ ] تم إنشاء Web App في Firebase Console
- [ ] تم تفعيل Authentication (Email/Password)
- [ ] تم إنشاء Firestore Database
- [ ] تم تفعيل Storage
- [ ] تم نسخ إعدادات Firebase

## تحديث الكود
- [ ] تم تحديث index-new.html
- [ ] تم تحديث login.html
- [ ] تم تحديث register.html
- [ ] تم تحديث dashboard-analyst.html
- [ ] تم تحديث dashboard-subscriber.html

## اختبار النظام
- [ ] تم فتح setup-project.html
- [ ] تم إنشاء مستخدم أدمن
- [ ] تم إنشاء بيانات تجريبية
- [ ] تم اختبار تسجيل الدخول
- [ ] تم اختبار إنشاء حساب محلل
- [ ] تم اختبار إنشاء حساب مشترك

## نشر القواعد (للمطورين)
- [ ] تم تثبيت Firebase CLI
- [ ] تم ربط المشروع: firebase use jorinvforex
- [ ] تم نشر قواعد Firestore
- [ ] تم نشر قواعد Storage
- [ ] تم نشر الفهارس

## الخطوات النهائية
- [ ] تم اختبار جميع الميزات
- [ ] تم إنشاء نسخة احتياطية
- [ ] تم توثيق كلمات المرور
- [ ] المنصة جاهزة للاستخدام!
`;
}

// تشغيل التحديث
function runUpdate() {
    console.log('🚀 بدء تحديث إعدادات Firebase لمشروع jorinvforex...');
    
    // التحقق من الإعدادات
    const errors = validateFirebaseConfig(NEW_FIREBASE_CONFIG);
    
    if (errors.length > 0) {
        console.log('⚠️ يرجى تحديث الإعدادات أولاً:');
        errors.forEach(error => console.log(error));
        return false;
    }
    
    // تحديث الملفات
    FILES_TO_UPDATE.forEach(file => {
        updateFirebaseConfigInFile(file, NEW_FIREBASE_CONFIG);
    });
    
    // إنشاء ملف الإعدادات المركزي
    const centralConfig = generateCentralConfigFile(NEW_FIREBASE_CONFIG);
    console.log('📄 ملف الإعدادات المركزي:');
    console.log(centralConfig);
    
    // إنشاء التعليمات
    const instructions = generateUpdateInstructions();
    console.log('📋 تعليمات التحديث:');
    console.log(instructions);
    
    // إنشاء قائمة المراجعة
    const checklist = generateChecklist();
    console.log('✅ قائمة المراجعة:');
    console.log(checklist);
    
    console.log('🎉 تم إنشاء جميع الملفات والتعليمات!');
    return true;
}

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        updateFirebaseConfigInFile,
        validateFirebaseConfig,
        generateCentralConfigFile,
        generateUpdateInstructions,
        generateChecklist,
        runUpdate,
        NEW_FIREBASE_CONFIG,
        FILES_TO_UPDATE
    };
}

// تشغيل تلقائي في المتصفح
if (typeof window !== 'undefined') {
    window.updateFirebaseConfig = {
        config: NEW_FIREBASE_CONFIG,
        files: FILES_TO_UPDATE,
        validate: validateFirebaseConfig,
        run: runUpdate
    };
    
    console.log('🔧 أدوات تحديث Firebase جاهزة!');
    console.log('استخدم: updateFirebaseConfig.run() لبدء التحديث');
}

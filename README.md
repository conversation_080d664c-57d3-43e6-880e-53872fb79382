# منصة المحللين الماليين - فوركس الأردن

منصة ويب متكاملة تربط بين المحللين الماليين المحترفين والمستثمرين الباحثين عن التحليلات الدقيقة.

## المميزات الرئيسية

### للمحللين الماليين
- إنشاء ملف شخصي مخصص مع صورة وغلاف
- نشر تحليلات مالية (مجانية ومدفوعة)
- إدارة المشتركين وطلبات الاشتراك
- نظام دردشة مباشر مع المشتركين
- لوحة تحكم شاملة مع الإحصائيات

### للمشتركين
- تصفح المحللين الماليين المتاحين
- طلب الاشتراك مع المحللين المفضلين
- الوصول للتحليلات المدفوعة بعد الموافقة
- نظام دردشة مع المحللين
- متابعة آخر التحليلات والتوصيات

### للإدارة
- إدارة المستخدمين والمحتوى
- مراقبة النشاط والإحصائيات
- إدارة الإعلانات
- نظام تحكم شامل

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, Vanilla JavaScript, Bootstrap 5
- **Backend**: Firebase (Authentication, Firestore, Storage, Cloud Functions)
- **التصميم**: تصميم متجاوب بألوان أبيض ودرجات الأزرق والنهدي
- **الخطوط**: Tajawal (خط عربي احترافي)

## هيكل المشروع

```
├── index-new.html          # الصفحة الرئيسية الجديدة
├── login.html              # صفحة تسجيل الدخول
├── register.html           # صفحة التسجيل
├── dashboard-analyst.html  # لوحة تحكم المحلل
├── dashboard-subscriber.html # لوحة تحكم المشترك
├── css/
│   ├── style.css          # التنسيقات الأساسية
│   └── responsive-enhancements.css
├── js/
│   ├── app.js             # الملف الرئيسي للتطبيق
│   ├── login.js           # منطق تسجيل الدخول
│   ├── register.js        # منطق التسجيل
│   ├── dashboard-analyst.js # منطق لوحة تحكم المحلل
│   └── dashboard-subscriber.js # منطق لوحة تحكم المشترك
├── assets/
│   ├── img/               # الصور والأيقونات
│   └── videos/            # ملفات الفيديو
└── tools/                 # أدوات التداول الموجودة
```

## إعداد Firebase

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم المشروع الموجود `jorinvforex`
3. فعّل Authentication, Firestore, Storage

### 2. إعداد Authentication
```javascript
// في Firebase Console > Authentication > Sign-in method
// فعّل Email/Password
```

### 3. إعداد Firestore Database
```javascript
// قواعد الأمان المقترحة
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null;
    }
    
    // قواعد المنشورات
    match /posts/{postId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid == resource.data.analystId;
    }
    
    // قواعد الاشتراكات
    match /subscriptions/{subscriptionId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.analystId || 
         request.auth.uid == resource.data.subscriberId);
    }
    
    // قواعد المحادثات
    match /chats/{chatId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 4. تحديث إعدادات Firebase
في جميع ملفات HTML، استبدل إعدادات Firebase:

```javascript
const firebaseConfig = {
    apiKey: "your-actual-api-key",
    authDomain: "jorinvforex.firebaseapp.com",
    projectId: "jorinvforex",
    storageBucket: "jorinvforex.appspot.com",
    messagingSenderId: "your-actual-sender-id",
    appId: "your-actual-app-id"
};
```

## هيكل قاعدة البيانات

### مجموعة Users
```javascript
{
  userId: {
    name: "اسم المستخدم",
    email: "<EMAIL>",
    role: "analyst" | "subscriber" | "admin",
    profilePicURL: "رابط الصورة الشخصية",
    coverPicURL: "رابط صورة الغلاف",
    description: "وصف المحلل",
    school: "المؤسسة التعليمية",
    themeColor: "#66CCFF",
    acceptedSubscribers: ["userId1", "userId2"], // للمحللين فقط
    createdAt: timestamp
  }
}
```

### مجموعة Posts
```javascript
{
  postId: {
    analystId: "معرف المحلل",
    title: "عنوان المنشور",
    description: "محتوى المنشور",
    imageURL: "رابط الصورة",
    tradingViewLink: "رابط TradingView",
    isPaidContent: true/false,
    isFreeNow: false, // يتحول لـ true بعد 3 ساعات
    createdAt: timestamp
  }
}
```

### مجموعة Subscriptions
```javascript
{
  subscriptionId: {
    analystId: "معرف المحلل",
    subscriberId: "معرف المشترك",
    subscriberName: "اسم المشترك",
    subscriberEmail: "بريد المشترك",
    status: "pending" | "accepted" | "rejected",
    paymentConfirmed: true/false,
    createdAt: timestamp
  }
}
```

### مجموعة Chats
```javascript
{
  chatId: { // analystId_subscriberId
    messages: [
      {
        senderId: "معرف المرسل",
        text: "نص الرسالة",
        timestamp: timestamp
      }
    ]
  }
}
```

## التشغيل والتطوير

### 1. تشغيل محلي
```bash
# استخدم خادم محلي بسيط
python -m http.server 8000
# أو
npx serve .
```

### 2. الوصول للتطبيق
- الصفحة الرئيسية: `http://localhost:8000/index-new.html`
- تسجيل الدخول: `http://localhost:8000/login.html`
- التسجيل: `http://localhost:8000/register.html`

## الميزات المستقبلية

### المرحلة الثانية
- [ ] نظام الدفع المتكامل
- [ ] إشعارات فورية
- [ ] تطبيق الجوال
- [ ] نظام التقييمات والمراجعات

### المرحلة الثالثة
- [ ] تحليلات متقدمة ولوحة معلومات
- [ ] نظام الإحالة والعمولات
- [ ] دعم متعدد اللغات
- [ ] API للمطورين

## المساهمة

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للدعم والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الموقع: [fxjordan.com](https://fxjordan.com)

---

تم تطوير هذا المشروع بواسطة فريق فوركس الأردن 🇯🇴

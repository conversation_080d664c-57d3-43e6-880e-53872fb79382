<?php
/**
 * لوحة تحكم المحلل المالي - نسخة تجريبية
 * jorinvforex Analyst Dashboard - Demo Version
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// مستخدم تجريبي (بدون نظام دخول)
$currentUser = [
    'id' => 2,
    'name' => 'أحمد محمد',
    'email' => '<EMAIL>',
    'role' => 'analyst',
    'bio' => 'محلل مالي معتمد مع خبرة 5 سنوات',
    'specialization' => 'تحليل الفوركس والأسهم',
    'profile_pic_url' => null
];

// بيانات تجريبية للإحصائيات
$stats = [
    'total_subscribers' => 156,
    'total_posts' => 23,
    'total_views' => 4567,
    'average_rating' => 4.8,
    'pending_subscriptions' => 8,
    'monthly_earnings' => 2340
];

// منشورات تجريبية
$recentPosts = [
    [
        'id' => 1,
        'title' => 'تحليل زوج EUR/USD - فرصة شراء قوية',
        'description' => 'بناءً على التحليل الفني والأساسي، نتوقع ارتفاع زوج اليورو دولار...',
        'views_count' => 234,
        'likes_count' => 45,
        'status' => 'published',
        'created_at' => '2024-01-15 10:30:00',
        'is_paid_content' => true
    ],
    [
        'id' => 2,
        'title' => 'تحليل الذهب - اتجاه صاعد متوقع',
        'description' => 'الذهب يظهر إشارات قوية للارتفاع مع كسر مستوى المقاومة...',
        'views_count' => 189,
        'likes_count' => 32,
        'status' => 'published',
        'created_at' => '2024-01-14 14:20:00',
        'is_paid_content' => false
    ],
    [
        'id' => 3,
        'title' => 'تحليل أسهم التكنولوجيا - مراجعة أسبوعية',
        'description' => 'مراجعة شاملة لأداء أسهم التكنولوجيا هذا الأسبوع...',
        'views_count' => 156,
        'likes_count' => 28,
        'status' => 'draft',
        'created_at' => '2024-01-13 09:15:00',
        'is_paid_content' => true
    ]
];

// طلبات اشتراك تجريبية
$subscriptionRequests = [
    [
        'id' => 1,
        'subscriber_name' => 'سارة أحمد',
        'subscriber_email' => '<EMAIL>',
        'status' => 'pending',
        'created_at' => '2024-01-15 16:45:00',
        'subscription_type' => 'monthly'
    ],
    [
        'id' => 2,
        'subscriber_name' => 'محمد علي',
        'subscriber_email' => '<EMAIL>',
        'status' => 'pending',
        'created_at' => '2024-01-15 14:30:00',
        'subscription_type' => 'quarterly'
    ],
    [
        'id' => 3,
        'subscriber_name' => 'فاطمة حسن',
        'subscriber_email' => '<EMAIL>',
        'status' => 'pending',
        'created_at' => '2024-01-15 11:20:00',
        'subscription_type' => 'monthly'
    ]
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المحلل | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: #f8f9fa;
            color: var(--text-dark);
        }

        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg, var(--primary-blue), var(--accent-blue));
            min-height: 100vh;
            width: 280px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: -2px 0 10px rgba(0, 51, 102, 0.1);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h4 {
            color: var(--primary-white);
            font-weight: 700;
            margin: 0;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.5rem 1rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: var(--primary-white);
            transform: translateX(-5px);
        }

        .nav-link i {
            margin-left: 10px;
            width: 20px;
        }

        /* Main Content */
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }

        /* Header */
        .page-header {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
        }

        .page-header h1 {
            color: var(--primary-blue);
            font-weight: 900;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: var(--text-light);
            margin: 0;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 51, 102, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-blue);
        }

        .stat-card.success::before { background: var(--success-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.danger::before { background: var(--danger-color); }
        .stat-card.info::before { background: var(--info-color); }

        .stat-card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .stat-card.success .stat-card-icon {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .stat-card.warning .stat-card-icon {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }

        .stat-card.danger .stat-card-icon {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }

        .stat-card.info .stat-card-icon {
            background: rgba(23, 162, 184, 0.1);
            color: var(--info-color);
        }

        .stat-card h3 {
            font-size: 2rem;
            font-weight: 900;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: var(--text-light);
            margin: 0;
            font-weight: 600;
        }

        /* Content Cards */
        .content-card {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
        }

        .content-card h4 {
            color: var(--primary-blue);
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .content-card h4 i {
            margin-left: 10px;
            color: var(--accent-blue);
        }

        /* Tables */
        .table {
            margin: 0;
        }

        .table th {
            background: #f8f9fa;
            border: none;
            color: var(--primary-blue);
            font-weight: 700;
            padding: 1rem;
        }

        .table td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr {
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* Badges */
        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .badge.bg-success { background: var(--success-color) !important; }
        .badge.bg-warning { background: var(--warning-color) !important; }
        .badge.bg-danger { background: var(--danger-color) !important; }
        .badge.bg-info { background: var(--info-color) !important; }

        /* Buttons */
        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chart-line"></i> jorinvforex</h4>
            <small style="color: rgba(255,255,255,0.7);">لوحة تحكم المحلل</small>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="#" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    منشوراتي
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-users"></i>
                    المشتركون
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-comments"></i>
                    المحادثات
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    الإحصائيات
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-dollar-sign"></i>
                    الأرباح
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </div>
            <div class="nav-item">
                <a href="index-sql.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1>مرحباً، <?php echo htmlspecialchars($currentUser['name']); ?>!</h1>
            <p>إليك نظرة سريعة على أداء حسابك كمحلل مالي</p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3><?php echo number_format($stats['total_subscribers']); ?></h3>
                <p>إجمالي المشتركين</p>
            </div>
            
            <div class="stat-card info">
                <div class="stat-card-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3><?php echo number_format($stats['total_posts']); ?></h3>
                <p>المنشورات المنشورة</p>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-card-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h3><?php echo number_format($stats['total_views']); ?></h3>
                <p>إجمالي المشاهدات</p>
            </div>
            
            <div class="stat-card success">
                <div class="stat-card-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h3><?php echo $stats['average_rating']; ?></h3>
                <p>متوسط التقييم</p>
            </div>
            
            <div class="stat-card danger">
                <div class="stat-card-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3><?php echo number_format($stats['pending_subscriptions']); ?></h3>
                <p>طلبات اشتراك معلقة</p>
            </div>
            
            <div class="stat-card success">
                <div class="stat-card-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h3>$<?php echo number_format($stats['monthly_earnings']); ?></h3>
                <p>الأرباح الشهرية</p>
            </div>
        </div>

        <!-- Recent Posts -->
        <div class="content-card">
            <h4><i class="fas fa-file-alt"></i> أحدث المنشورات</h4>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>المشاهدات</th>
                            <th>الإعجابات</th>
                            <th>الحالة</th>
                            <th>النوع</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentPosts as $post): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($post['title']); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo htmlspecialchars(substr($post['description'], 0, 60)); ?>...</small>
                                </td>
                                <td>
                                    <i class="fas fa-eye text-info"></i>
                                    <?php echo number_format($post['views_count']); ?>
                                </td>
                                <td>
                                    <i class="fas fa-heart text-danger"></i>
                                    <?php echo number_format($post['likes_count']); ?>
                                </td>
                                <td>
                                    <?php if ($post['status'] === 'published'): ?>
                                        <span class="badge bg-success">منشور</span>
                                    <?php elseif ($post['status'] === 'draft'): ?>
                                        <span class="badge bg-warning">مسودة</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">مؤرشف</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($post['is_paid_content']): ?>
                                        <span class="badge bg-info">مدفوع</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">مجاني</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo date('Y-m-d H:i', strtotime($post['created_at'])); ?></small>
                                </td>
                                <td>
                                    <a href="#" class="btn btn-custom btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="#" class="btn btn-custom btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="text-center mt-3">
                <a href="#" class="btn btn-custom">
                    <i class="fas fa-plus"></i> إنشاء منشور جديد
                </a>
                <a href="#" class="btn btn-custom">
                    <i class="fas fa-list"></i> عرض جميع المنشورات
                </a>
            </div>
        </div>

        <!-- Subscription Requests -->
        <div class="content-card">
            <h4><i class="fas fa-user-plus"></i> طلبات الاشتراك المعلقة</h4>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المشترك</th>
                            <th>البريد الإلكتروني</th>
                            <th>نوع الاشتراك</th>
                            <th>تاريخ الطلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($subscriptionRequests as $request): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <strong><?php echo htmlspecialchars($request['subscriber_name']); ?></strong>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($request['subscriber_email']); ?></td>
                                <td>
                                    <?php if ($request['subscription_type'] === 'monthly'): ?>
                                        <span class="badge bg-info">شهري</span>
                                    <?php elseif ($request['subscription_type'] === 'quarterly'): ?>
                                        <span class="badge bg-warning">ربع سنوي</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">سنوي</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?></small>
                                </td>
                                <td>
                                    <button class="btn btn-success btn-sm me-1">
                                        <i class="fas fa-check"></i> قبول
                                    </button>
                                    <button class="btn btn-danger btn-sm">
                                        <i class="fas fa-times"></i> رفض
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="text-center mt-3">
                <a href="#" class="btn btn-custom">
                    <i class="fas fa-users"></i> إدارة جميع المشتركين
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="content-card">
            <h4><i class="fas fa-bolt"></i> إجراءات سريعة</h4>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-plus fa-2x mb-2"></i>
                        <br>إنشاء منشور جديد
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <br>ربط TradingView
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-comments fa-2x mb-2"></i>
                        <br>المحادثات
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-cog fa-2x mb-2"></i>
                        <br>إعدادات الحساب
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير النقر على الأزرار
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === '#') {
                        e.preventDefault();

                        // تأثير بصري
                        this.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 150);

                        // رسالة تجريبية
                        alert('هذه نسخة تجريبية - الوظيفة ستكون متاحة في النسخة الكاملة');
                    }
                });
            });
        });
    </script>
</body>
</html>

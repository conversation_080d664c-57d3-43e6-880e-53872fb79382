<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المشترك | منصة المحللين الماليين</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --border-light: #e9ecef;
            --shadow-light: rgba(0, 51, 102, 0.1);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background-color: #f8f9fa;
            color: var(--text-dark);
            line-height: 1.6;
        }

        /* Header */
        .main-header {
            background: var(--primary-white);
            box-shadow: 0 2px 10px var(--shadow-light);
            padding: 1rem 2rem;
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid var(--accent-blue);
        }

        /* Navigation Tabs */
        .nav-tabs-custom {
            background: var(--primary-white);
            border-bottom: 1px solid var(--border-light);
            padding: 0 2rem;
        }

        .nav-tabs-custom .nav-link {
            color: var(--text-dark);
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-tabs-custom .nav-link:hover,
        .nav-tabs-custom .nav-link.active {
            color: var(--primary-blue);
            background: none;
            border-bottom: 3px solid var(--accent-blue);
        }

        /* Main Content */
        .main-content {
            padding: 2rem;
        }

        /* Cards */
        .content-card {
            background: var(--primary-white);
            border-radius: 15px;
            box-shadow: 0 5px 15px var(--shadow-light);
            border: 1px solid var(--border-light);
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .card-title {
            color: var(--primary-blue);
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Analyst Cards */
        .analyst-card {
            background: var(--primary-white);
            border: 1px solid var(--border-light);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px var(--shadow-light);
        }

        .analyst-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px var(--shadow-light);
        }

        .analyst-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .analyst-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid var(--accent-blue);
        }

        .analyst-info h5 {
            color: var(--primary-blue);
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .analyst-info p {
            color: var(--text-light);
            margin: 0;
            font-size: 0.9rem;
        }

        .analyst-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-blue);
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-light);
        }

        /* Buttons */
        .btn-primary-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .btn-secondary-custom {
            background: transparent;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            padding: 8px 18px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary-custom:hover {
            background: var(--primary-blue);
            color: var(--primary-white);
        }

        /* Post Cards */
        .post-card {
            background: var(--primary-white);
            border: 1px solid var(--border-light);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .post-card:hover {
            box-shadow: 0 5px 15px var(--shadow-light);
        }

        .post-header {
            display: flex;
            justify-content: between;
            align-items: start;
            margin-bottom: 1rem;
        }

        .post-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.9rem;
            color: var(--text-light);
        }

        .post-title {
            color: var(--primary-blue);
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .post-content {
            color: var(--text-dark);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .post-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Loading */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
            flex-direction: column;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-light);
            border-top: 4px solid var(--accent-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--text-light);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-header {
                padding: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .nav-tabs-custom {
                padding: 0 1rem;
            }
            
            .analyst-stats {
                gap: 1rem;
            }
            
            .post-header {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="header-left">
            <img src="assets/img/logo.png" alt="فوركس الأردن" class="logo">
            <h1 class="page-title">منصة المحللين الماليين</h1>
        </div>
        
        <div class="header-right">
            <div class="user-menu">
                <img src="assets/img/default-avatar.png" alt="الصورة الشخصية" class="user-avatar" id="userAvatar">
            </div>
            <button class="btn btn-outline-danger btn-sm" onclick="signOut()">
                <i class="fas fa-sign-out-alt"></i>
                خروج
            </button>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="nav-tabs-custom">
        <ul class="nav nav-tabs" id="mainTabs">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#analysts" id="analystsTab">
                    <i class="fas fa-users"></i>
                    المحللين
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#subscriptions" id="subscriptionsTab">
                    <i class="fas fa-star"></i>
                    اشتراكاتي
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#posts" id="postsTab">
                    <i class="fas fa-newspaper"></i>
                    التحليلات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#chat" id="chatTab">
                    <i class="fas fa-comments"></i>
                    المحادثات
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="tab-content" id="mainTabsContent">
            <!-- Analysts Tab -->
            <div class="tab-pane fade show active" id="analysts">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">المحللين الماليين المتاحين</h3>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control" placeholder="البحث عن محلل..." id="searchAnalysts" style="max-width: 250px;">
                            <button class="btn btn-outline-primary" onclick="searchAnalysts()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="analystsList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المحللين...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscriptions Tab -->
            <div class="tab-pane fade" id="subscriptions">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">اشتراكاتي</h3>
                    </div>
                    <div class="card-body">
                        <div id="subscriptionsList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل الاشتراكات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Posts Tab -->
            <div class="tab-pane fade" id="posts">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">آخر التحليلات المالية</h3>
                        <div class="d-flex gap-2">
                            <select class="form-select" id="filterPosts" style="max-width: 200px;">
                                <option value="all">جميع التحليلات</option>
                                <option value="free">المجانية فقط</option>
                                <option value="paid">المدفوعة فقط</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="postsList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل التحليلات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Tab -->
            <div class="tab-pane fade" id="chat">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">المحادثات</h3>
                    </div>
                    <div class="card-body">
                        <div id="chatList" class="loading-container">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المحادثات...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase Configuration -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "jorinvforex.firebaseapp.com",
            projectId: "jorinvforex",
            storageBucket: "jorinvforex.appspot.com",
            messagingSenderId: "your-sender-id",
            appId: "your-app-id"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();
    </script>

    <!-- Dashboard Scripts -->
    <script src="js/app.js"></script>
    <script src="js/dashboard-subscriber.js"></script>
</body>
</html>

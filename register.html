<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب | منصة المحللين الماليين</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --border-light: #e9ecef;
            --shadow-light: rgba(255, 255, 255, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--light-blue) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
            padding: 20px 0;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23003366" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .register-container {
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px var(--shadow-light);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            position: relative;
            z-index: 2;
            border: 1px solid var(--border-light);
        }

        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .register-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 1rem;
        }

        .register-title {
            color: var(--primary-blue);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .register-subtitle {
            color: var(--text-light);
            font-size: 1rem;
        }

        .role-selection {
            margin-bottom: 2rem;
        }

        .role-option {
            border: 2px solid var(--border-light);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--primary-white);
        }

        .role-option:hover {
            border-color: var(--accent-blue);
            background: rgba(102, 204, 255, 0.05);
        }

        .role-option.selected {
            border-color: var(--primary-blue);
            background: rgba(0, 51, 102, 0.05);
        }

        .role-option input[type="radio"] {
            display: none;
        }

        .role-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, var(--accent-blue), var(--light-blue));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            color: var(--primary-white);
        }

        .role-title {
            color: var(--primary-blue);
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .role-description {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: var(--text-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 2px solid var(--border-light);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--primary-white);
        }

        .form-control:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 0.2rem rgba(102, 204, 255, 0.25);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            z-index: 3;
        }

        .form-control.with-icon {
            padding-right: 45px;
        }

        .btn-register {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 51, 102, 0.3);
        }

        .btn-register:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-link {
            text-align: center;
            margin-top: 1.5rem;
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            color: var(--accent-blue);
            text-decoration: underline;
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 1.1rem;
            z-index: 3;
        }

        .back-link:hover {
            color: var(--accent-blue);
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary-white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .success-message {
            background: #efe;
            border: 1px solid #cfc;
            color: #363;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .register-container {
                margin: 20px;
                padding: 2rem;
            }
            
            .register-title {
                font-size: 1.5rem;
            }
            
            .back-link {
                position: relative;
                top: auto;
                left: auto;
                display: block;
                text-align: center;
                margin-bottom: 1rem;
            }
            
            .role-option {
                padding: 0.8rem;
            }
            
            .role-icon {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <a href="index-new.html" class="back-link">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </a>

    <div class="register-container">
        <div class="register-header">
            <img src="assets/img/logo.png" alt="فوركس الأردن" class="register-logo">
            <h1 class="register-title">إنشاء حساب جديد</h1>
            <p class="register-subtitle">انضم إلى منصة المحللين الماليين</p>
        </div>

        <div id="errorMessage" class="error-message" style="display: none;"></div>
        <div id="successMessage" class="success-message" style="display: none;"></div>

        <form id="registerForm">
            <!-- اختيار نوع الحساب -->
            <div class="role-selection">
                <h3 style="color: var(--primary-blue); margin-bottom: 1rem; font-size: 1.2rem;">اختر نوع حسابك</h3>
                
                <div class="role-option" data-role="analyst">
                    <input type="radio" name="userRole" value="analyst" id="analyst">
                    <div class="d-flex align-items-center">
                        <div class="role-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="role-title">محلل مالي</div>
                            <div class="role-description">قدم تحليلاتك المالية واكسب من خبرتك</div>
                        </div>
                    </div>
                </div>
                
                <div class="role-option" data-role="subscriber">
                    <input type="radio" name="userRole" value="subscriber" id="subscriber">
                    <div class="d-flex align-items-center">
                        <div class="role-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="role-title">مشترك</div>
                            <div class="role-description">احصل على تحليلات مالية من خبراء معتمدين</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- بيانات المستخدم -->
            <div class="form-group">
                <label for="fullName" class="form-label">الاسم الكامل</label>
                <div class="input-group">
                    <input type="text" class="form-control with-icon" id="fullName" required>
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <div class="input-group">
                    <input type="email" class="form-control with-icon" id="email" required>
                    <span class="input-group-text">
                        <i class="fas fa-envelope"></i>
                    </span>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <input type="password" class="form-control with-icon" id="password" required>
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                </div>
                <small class="text-muted">يجب أن تكون 6 أحرف على الأقل</small>
            </div>

            <div class="form-group">
                <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                <div class="input-group">
                    <input type="password" class="form-control with-icon" id="confirmPassword" required>
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                </div>
            </div>

            <!-- حقول إضافية للمحلل -->
            <div id="analystFields" style="display: none;">
                <div class="form-group">
                    <label for="school" class="form-label">المؤسسة التعليمية أو الشركة</label>
                    <div class="input-group">
                        <input type="text" class="form-control with-icon" id="school">
                        <span class="input-group-text">
                            <i class="fas fa-building"></i>
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">وصف مختصر عن خبرتك</label>
                    <textarea class="form-control" id="description" rows="3"
                              placeholder="اكتب وصفاً مختصراً عن خبرتك في التحليل المالي..."></textarea>
                </div>
            </div>

            <button type="submit" class="btn btn-register" id="registerBtn">
                <span id="registerBtnText">إنشاء الحساب</span>
                <span id="registerSpinner" class="loading-spinner" style="display: none;"></span>
            </button>
        </form>

        <div class="login-link">
            <p>لديك حساب بالفعل؟ <a href="login.html">تسجيل الدخول</a></p>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase Configuration -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "jorinvforex.firebaseapp.com",
            projectId: "jorinvforex",
            storageBucket: "jorinvforex.appspot.com",
            messagingSenderId: "your-sender-id",
            appId: "your-app-id"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();
    </script>

    <!-- Register Script -->
    <script src="js/register.js"></script>
</body>
</html>

<?php
/**
 * لوحة تحكم المشترك - نسخة تجريبية
 * jorinvforex Subscriber Dashboard - Demo Version
 */

// تعريف التطبيق
define('JORINVFOREX_APP', true);

// مستخدم تجريبي (بدون نظام دخول)
$currentUser = [
    'id' => 3,
    'name' => 'سارة أحمد',
    'email' => '<EMAIL>',
    'role' => 'subscriber',
    'bio' => 'مستثمرة مهتمة بأسواق المال والعملات',
    'profile_pic_url' => null
];

// بيانات تجريبية للإحصائيات
$stats = [
    'active_subscriptions' => 3,
    'total_posts_viewed' => 45,
    'favorite_analysts' => 2,
    'unread_notifications' => 7,
    'total_spent' => 150,
    'this_month_spent' => 45
];

// اشتراكات تجريبية
$subscriptions = [
    [
        'id' => 1,
        'analyst_name' => 'أحمد محمد',
        'analyst_email' => '<EMAIL>',
        'analyst_specialization' => 'تحليل الفوركس والأسهم',
        'status' => 'active',
        'subscription_type' => 'monthly',
        'expires_at' => '2024-02-15',
        'posts_count' => 12,
        'rating' => 4.8
    ],
    [
        'id' => 2,
        'analyst_name' => 'فاطمة حسن',
        'analyst_email' => '<EMAIL>',
        'analyst_specialization' => 'تحليل الذهب والمعادن',
        'status' => 'active',
        'subscription_type' => 'quarterly',
        'expires_at' => '2024-04-15',
        'posts_count' => 8,
        'rating' => 4.9
    ],
    [
        'id' => 3,
        'analyst_name' => 'محمد علي',
        'analyst_email' => '<EMAIL>',
        'analyst_specialization' => 'تحليل العملات الرقمية',
        'status' => 'pending',
        'subscription_type' => 'monthly',
        'expires_at' => null,
        'posts_count' => 0,
        'rating' => 4.7
    ]
];

// أحدث المنشورات
$recentPosts = [
    [
        'id' => 1,
        'title' => 'تحليل زوج EUR/USD - فرصة شراء قوية',
        'analyst_name' => 'أحمد محمد',
        'description' => 'بناءً على التحليل الفني والأساسي، نتوقع ارتفاع زوج اليورو دولار...',
        'created_at' => '2024-01-15 10:30:00',
        'is_paid_content' => true,
        'is_read' => false
    ],
    [
        'id' => 2,
        'title' => 'تحليل الذهب - اتجاه صاعد متوقع',
        'analyst_name' => 'فاطمة حسن',
        'description' => 'الذهب يظهر إشارات قوية للارتفاع مع كسر مستوى المقاومة...',
        'created_at' => '2024-01-14 14:20:00',
        'is_paid_content' => true,
        'is_read' => true
    ],
    [
        'id' => 3,
        'title' => 'تحليل البيتكوين - مراجعة أسبوعية',
        'analyst_name' => 'محمد علي',
        'description' => 'مراجعة شاملة لأداء البيتكوين والعملات الرقمية هذا الأسبوع...',
        'created_at' => '2024-01-13 09:15:00',
        'is_paid_content' => false,
        'is_read' => true
    ]
];

// إشعارات تجريبية
$notifications = [
    [
        'id' => 1,
        'title' => 'منشور جديد من أحمد محمد',
        'message' => 'تم نشر تحليل جديد لزوج EUR/USD',
        'type' => 'new_post',
        'is_read' => false,
        'created_at' => '2024-01-15 10:30:00'
    ],
    [
        'id' => 2,
        'title' => 'تم قبول طلب الاشتراك',
        'message' => 'تم قبول طلب اشتراكك مع المحلل فاطمة حسن',
        'type' => 'subscription_accepted',
        'is_read' => false,
        'created_at' => '2024-01-14 16:45:00'
    ],
    [
        'id' => 3,
        'title' => 'تذكير: انتهاء الاشتراك قريباً',
        'message' => 'سينتهي اشتراكك مع أحمد محمد خلال 5 أيام',
        'type' => 'subscription_expiring',
        'is_read' => true,
        'created_at' => '2024-01-13 12:00:00'
    ]
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المشترك | jorinvforex</title>
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-white: #FFFFFF;
            --primary-blue: #003366;
            --accent-blue: #66CCFF;
            --light-blue: #99CCFF;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: #f8f9fa;
            color: var(--text-dark);
        }

        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg, var(--primary-blue), var(--accent-blue));
            min-height: 100vh;
            width: 280px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: -2px 0 10px rgba(0, 51, 102, 0.1);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h4 {
            color: var(--primary-white);
            font-weight: 700;
            margin: 0;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.5rem 1rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: var(--primary-white);
            transform: translateX(-5px);
        }

        .nav-link i {
            margin-left: 10px;
            width: 20px;
        }

        /* Main Content */
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }

        /* Header */
        .page-header {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
        }

        .page-header h1 {
            color: var(--primary-blue);
            font-weight: 900;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: var(--text-light);
            margin: 0;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 51, 102, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-blue);
        }

        .stat-card.success::before { background: var(--success-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.danger::before { background: var(--danger-color); }
        .stat-card.info::before { background: var(--info-color); }

        .stat-card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .stat-card.success .stat-card-icon {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .stat-card.warning .stat-card-icon {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
        }

        .stat-card.danger .stat-card-icon {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }

        .stat-card.info .stat-card-icon {
            background: rgba(23, 162, 184, 0.1);
            color: var(--info-color);
        }

        .stat-card h3 {
            font-size: 2rem;
            font-weight: 900;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: var(--text-light);
            margin: 0;
            font-weight: 600;
        }

        /* Content Cards */
        .content-card {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
        }

        .content-card h4 {
            color: var(--primary-blue);
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .content-card h4 i {
            margin-left: 10px;
            color: var(--accent-blue);
        }

        /* Tables */
        .table {
            margin: 0;
        }

        .table th {
            background: #f8f9fa;
            border: none;
            color: var(--primary-blue);
            font-weight: 700;
            padding: 1rem;
        }

        .table td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr {
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* Badges */
        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .badge.bg-success { background: var(--success-color) !important; }
        .badge.bg-warning { background: var(--warning-color) !important; }
        .badge.bg-danger { background: var(--danger-color) !important; }
        .badge.bg-info { background: var(--info-color) !important; }

        /* Buttons */
        .btn-custom {
            background: linear-gradient(45deg, var(--primary-blue), var(--accent-blue));
            border: none;
            color: var(--primary-white);
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
            color: var(--primary-white);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        /* Post Cards */
        .post-card {
            background: var(--primary-white);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--accent-blue);
            transition: all 0.3s ease;
        }

        .post-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
        }

        .post-card.unread {
            border-left-color: var(--warning-color);
            background: rgba(255, 193, 7, 0.05);
        }

        /* Notifications */
        .notification-item {
            background: var(--primary-white);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-right: 4px solid var(--info-color);
            transition: all 0.3s ease;
        }

        .notification-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1);
        }

        .notification-item.unread {
            border-right-color: var(--warning-color);
            background: rgba(255, 193, 7, 0.05);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chart-line"></i> jorinvforex</h4>
            <small style="color: rgba(255,255,255,0.7);">لوحة تحكم المشترك</small>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="#" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-users"></i>
                    اشتراكاتي
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    المنشورات
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-search"></i>
                    البحث عن محللين
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-comments"></i>
                    المحادثات
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-bell"></i>
                    الإشعارات
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-heart"></i>
                    المفضلة
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </div>
            <div class="nav-item">
                <a href="index-sql.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1>مرحباً، <?php echo htmlspecialchars($currentUser['name']); ?>!</h1>
            <p>إليك نظرة سريعة على اشتراكاتك والمحتوى الجديد</p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-card-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3><?php echo number_format($stats['active_subscriptions']); ?></h3>
                <p>الاشتراكات النشطة</p>
            </div>

            <div class="stat-card info">
                <div class="stat-card-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h3><?php echo number_format($stats['total_posts_viewed']); ?></h3>
                <p>المنشورات المقروءة</p>
            </div>

            <div class="stat-card warning">
                <div class="stat-card-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h3><?php echo number_format($stats['favorite_analysts']); ?></h3>
                <p>المحللون المفضلون</p>
            </div>

            <div class="stat-card danger">
                <div class="stat-card-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <h3><?php echo number_format($stats['unread_notifications']); ?></h3>
                <p>إشعارات غير مقروءة</p>
            </div>

            <div class="stat-card success">
                <div class="stat-card-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h3>$<?php echo number_format($stats['total_spent']); ?></h3>
                <p>إجمالي المصروفات</p>
            </div>

            <div class="stat-card info">
                <div class="stat-card-icon">
                    <i class="fas fa-calendar"></i>
                </div>
                <h3>$<?php echo number_format($stats['this_month_spent']); ?></h3>
                <p>مصروفات هذا الشهر</p>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="content-card">
            <h4><i class="fas fa-users"></i> اشتراكاتي النشطة</h4>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المحلل</th>
                            <th>التخصص</th>
                            <th>نوع الاشتراك</th>
                            <th>الحالة</th>
                            <th>ينتهي في</th>
                            <th>التقييم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($subscriptions as $subscription): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($subscription['analyst_name']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($subscription['analyst_email']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($subscription['analyst_specialization']); ?></td>
                                <td>
                                    <?php if ($subscription['subscription_type'] === 'monthly'): ?>
                                        <span class="badge bg-info">شهري</span>
                                    <?php elseif ($subscription['subscription_type'] === 'quarterly'): ?>
                                        <span class="badge bg-warning">ربع سنوي</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">سنوي</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($subscription['status'] === 'active'): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php elseif ($subscription['status'] === 'pending'): ?>
                                        <span class="badge bg-warning">معلق</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">منتهي</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($subscription['expires_at']): ?>
                                        <small><?php echo date('Y-m-d', strtotime($subscription['expires_at'])); ?></small>
                                    <?php else: ?>
                                        <small class="text-muted">غير محدد</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-warning">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo $i <= $subscription['rating'] ? '' : ' text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                        <small class="ms-1"><?php echo $subscription['rating']; ?></small>
                                    </div>
                                </td>
                                <td>
                                    <a href="#" class="btn btn-custom btn-sm">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="#" class="btn btn-custom btn-sm">
                                        <i class="fas fa-comments"></i> محادثة
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="text-center mt-3">
                <a href="#" class="btn btn-custom">
                    <i class="fas fa-search"></i> البحث عن محللين جدد
                </a>
            </div>
        </div>

        <!-- Recent Posts -->
        <div class="content-card">
            <h4><i class="fas fa-file-alt"></i> أحدث المنشورات</h4>

            <div class="row">
                <?php foreach ($recentPosts as $post): ?>
                    <div class="col-md-6 mb-3">
                        <div class="post-card <?php echo !$post['is_read'] ? 'unread' : ''; ?>">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-1"><?php echo htmlspecialchars($post['title']); ?></h6>
                                <?php if (!$post['is_read']): ?>
                                    <span class="badge bg-warning">جديد</span>
                                <?php endif; ?>
                            </div>

                            <p class="text-muted small mb-2"><?php echo htmlspecialchars($post['description']); ?></p>

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-user"></i> <?php echo htmlspecialchars($post['analyst_name']); ?>
                                </small>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d H:i', strtotime($post['created_at'])); ?>
                                </small>
                            </div>

                            <div class="mt-2">
                                <?php if ($post['is_paid_content']): ?>
                                    <span class="badge bg-info me-1">مدفوع</span>
                                <?php else: ?>
                                    <span class="badge bg-success me-1">مجاني</span>
                                <?php endif; ?>

                                <a href="#" class="btn btn-custom btn-sm float-end">
                                    <i class="fas fa-eye"></i> قراءة
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="text-center mt-3">
                <a href="#" class="btn btn-custom">
                    <i class="fas fa-list"></i> عرض جميع المنشورات
                </a>
            </div>
        </div>

        <!-- Notifications -->
        <div class="content-card">
            <h4><i class="fas fa-bell"></i> الإشعارات الأخيرة</h4>

            <?php foreach (array_slice($notifications, 0, 5) as $notification): ?>
                <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?>">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1"><?php echo htmlspecialchars($notification['title']); ?></h6>
                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($notification['message']); ?></p>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                            </small>
                        </div>

                        <div>
                            <?php if (!$notification['is_read']): ?>
                                <span class="badge bg-warning">جديد</span>
                            <?php endif; ?>

                            <?php if ($notification['type'] === 'new_post'): ?>
                                <i class="fas fa-file-alt text-info"></i>
                            <?php elseif ($notification['type'] === 'subscription_accepted'): ?>
                                <i class="fas fa-check-circle text-success"></i>
                            <?php elseif ($notification['type'] === 'subscription_expiring'): ?>
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <div class="text-center mt-3">
                <a href="#" class="btn btn-custom">
                    <i class="fas fa-bell"></i> عرض جميع الإشعارات
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="content-card">
            <h4><i class="fas fa-bolt"></i> إجراءات سريعة</h4>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <br>البحث عن محللين
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-comments fa-2x mb-2"></i>
                        <br>المحادثات
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-heart fa-2x mb-2"></i>
                        <br>المفضلة
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-custom w-100 p-3">
                        <i class="fas fa-cog fa-2x mb-2"></i>
                        <br>إعدادات الحساب
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            document.querySelectorAll('.stat-card, .post-card, .notification-item').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.01)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير النقر على الأزرار
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === '#') {
                        e.preventDefault();

                        // تأثير بصري
                        this.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 150);

                        // رسالة تجريبية
                        alert('هذه نسخة تجريبية - الوظيفة ستكون متاحة في النسخة الكاملة');
                    }
                });
            });

            // تحديث عداد الإشعارات
            function updateNotificationCount() {
                const unreadCount = <?php echo count(array_filter($notifications, function($n) { return !$n['is_read']; })); ?>;
                const notificationBadge = document.querySelector('.nav-link[href="#"] .badge');

                if (unreadCount > 0 && !notificationBadge) {
                    const notificationLink = document.querySelector('.nav-link:has(.fa-bell)');
                    if (notificationLink) {
                        const badge = document.createElement('span');
                        badge.className = 'badge bg-danger ms-auto';
                        badge.textContent = unreadCount;
                        notificationLink.appendChild(badge);
                    }
                }
            }

            updateNotificationCount();
        });

        // تبديل الشريط الجانبي في الجوال
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // إضافة زر تبديل للجوال
        if (window.innerWidth <= 768) {
            const toggleBtn = document.createElement('button');
            toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
            toggleBtn.className = 'btn btn-custom position-fixed';
            toggleBtn.style.top = '20px';
            toggleBtn.style.right = '20px';
            toggleBtn.style.zIndex = '1001';
            toggleBtn.onclick = toggleSidebar;
            document.body.appendChild(toggleBtn);
        }
    </script>
</body>
</html>

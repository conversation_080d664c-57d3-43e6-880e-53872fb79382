// ===== لوحة تحكم المحلل =====

let currentAnalyst = null;
let currentSection = 'dashboard';

// ===== تهيئة لوحة التحكم =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل لوحة تحكم المحلل');
    
    // التحقق من المصادقة والدور
    auth.onAuthStateChanged(async (user) => {
        if (user) {
            // التحقق من دور المستخدم
            const userDoc = await db.collection('users').doc(user.uid).get();
            if (userDoc.exists && userDoc.data().role === 'analyst') {
                currentAnalyst = { uid: user.uid, ...userDoc.data() };
                initDashboard();
            } else {
                // توجيه المستخدم إذا لم يكن محلل
                window.location.href = 'login.html';
            }
        } else {
            // توجيه إلى صفحة تسجيل الدخول
            window.location.href = 'login.html';
        }
    });
    
    // إعداد التنقل
    setupNavigation();
    
    // إعداد الشريط الجانبي للهواتف
    setupMobileSidebar();
});

// ===== تهيئة لوحة التحكم =====
async function initDashboard() {
    try {
        // تحديث معلومات المستخدم في الواجهة
        updateUserInfo();
        
        // تحميل الإحصائيات
        await loadDashboardStats();
        
        // تحميل آخر المنشورات
        await loadRecentPosts();
        
        console.log('تم تهيئة لوحة التحكم بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة لوحة التحكم:', error);
        showNotification('حدث خطأ في تحميل البيانات', 'error');
    }
}

// ===== تحديث معلومات المستخدم =====
function updateUserInfo() {
    // تحديث الصورة الشخصية
    const userAvatar = document.getElementById('userAvatar');
    if (currentAnalyst.profilePicURL) {
        userAvatar.src = currentAnalyst.profilePicURL;
    }
    
    // تحديث العنوان
    document.title = `لوحة تحكم ${currentAnalyst.name} | منصة المحللين الماليين`;
}

// ===== تحميل إحصائيات لوحة التحكم =====
async function loadDashboardStats() {
    try {
        // تحميل عدد المنشورات
        const postsSnapshot = await db.collection('posts')
            .where('analystId', '==', currentAnalyst.uid)
            .get();
        document.getElementById('totalPosts').textContent = postsSnapshot.size;
        
        // تحميل عدد المشتركين
        const subscribersCount = currentAnalyst.acceptedSubscribers ? currentAnalyst.acceptedSubscribers.length : 0;
        document.getElementById('totalSubscribers').textContent = subscribersCount;
        
        // تحميل طلبات الاشتراك المعلقة
        const requestsSnapshot = await db.collection('subscriptions')
            .where('analystId', '==', currentAnalyst.uid)
            .where('status', '==', 'pending')
            .get();
        document.getElementById('pendingRequests').textContent = requestsSnapshot.size;
        
        // حساب مشاهدات المنشورات (مؤقت)
        document.getElementById('totalViews').textContent = postsSnapshot.size * 15; // متوسط تقديري
        
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
    }
}

// ===== تحميل آخر المنشورات =====
async function loadRecentPosts() {
    const recentPostsContainer = document.getElementById('recentPosts');
    
    try {
        const postsSnapshot = await db.collection('posts')
            .where('analystId', '==', currentAnalyst.uid)
            .orderBy('createdAt', 'desc')
            .limit(5)
            .get();
        
        if (postsSnapshot.empty) {
            recentPostsContainer.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-edit fa-3x text-muted mb-3"></i>
                    <h5>لا توجد منشورات بعد</h5>
                    <p class="text-muted">ابدأ بإنشاء منشورك الأول</p>
                    <button class="btn-primary-custom" onclick="showSection('posts')">
                        <i class="fas fa-plus"></i>
                        إنشاء منشور
                    </button>
                </div>
            `;
            return;
        }
        
        let postsHTML = '';
        postsSnapshot.forEach(doc => {
            const post = doc.data();
            const postDate = post.createdAt ? post.createdAt.toDate().toLocaleDateString('ar-SA') : 'غير محدد';
            
            postsHTML += `
                <div class="d-flex justify-content-between align-items-center border-bottom py-3">
                    <div>
                        <h6 class="mb-1">${post.title || 'بدون عنوان'}</h6>
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> ${postDate}
                            <span class="mx-2">|</span>
                            <i class="fas fa-${post.isPaidContent ? 'lock' : 'unlock'}"></i>
                            ${post.isPaidContent ? 'مدفوع' : 'مجاني'}
                        </small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="editPost('${doc.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        
        recentPostsContainer.innerHTML = postsHTML;
        
    } catch (error) {
        console.error('خطأ في تحميل المنشورات:', error);
        recentPostsContainer.innerHTML = `
            <div class="text-center py-4 text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <p>حدث خطأ في تحميل المنشورات</p>
            </div>
        `;
    }
}

// ===== إعداد التنقل =====
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[data-section]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.dataset.section;
            showSection(section);
        });
    });
}

// ===== عرض قسم معين =====
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    // إزالة الفئة النشطة من جميع الروابط
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // إظهار القسم المطلوب
    const targetSection = document.getElementById(sectionName + 'Section');
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // تفعيل الرابط المناسب
    const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    // تحديث عنوان الصفحة
    updatePageTitle(sectionName);
    
    // تحميل محتوى القسم
    loadSectionContent(sectionName);
    
    currentSection = sectionName;
}

// ===== تحديث عنوان الصفحة =====
function updatePageTitle(sectionName) {
    const titles = {
        'dashboard': 'لوحة التحكم',
        'posts': 'منشوراتي',
        'subscribers': 'المشتركين',
        'requests': 'طلبات الاشتراك',
        'chat': 'المحادثات',
        'profile': 'الملف الشخصي'
    };
    
    const pageTitle = document.getElementById('pageTitle');
    pageTitle.textContent = titles[sectionName] || 'لوحة التحكم';
}

// ===== تحميل محتوى القسم =====
async function loadSectionContent(sectionName) {
    switch (sectionName) {
        case 'posts':
            await loadPostsSection();
            break;
        case 'subscribers':
            await loadSubscribersSection();
            break;
        case 'requests':
            await loadRequestsSection();
            break;
        case 'chat':
            await loadChatSection();
            break;
        case 'profile':
            await loadProfileSection();
            break;
    }
}

// ===== تحميل قسم المنشورات =====
async function loadPostsSection() {
    const postsListContainer = document.getElementById('postsList');
    
    try {
        const postsSnapshot = await db.collection('posts')
            .where('analystId', '==', currentAnalyst.uid)
            .orderBy('createdAt', 'desc')
            .get();
        
        if (postsSnapshot.empty) {
            postsListContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-edit fa-4x text-muted mb-4"></i>
                    <h4>لا توجد منشورات</h4>
                    <p class="text-muted mb-4">ابدأ بإنشاء منشورك الأول لمشاركة تحليلاتك المالية</p>
                    <button class="btn-primary-custom" onclick="showNewPostModal()">
                        <i class="fas fa-plus"></i>
                        إنشاء منشور جديد
                    </button>
                </div>
            `;
            return;
        }
        
        let postsHTML = '<div class="row g-4">';
        postsSnapshot.forEach(doc => {
            const post = doc.data();
            const postDate = post.createdAt ? post.createdAt.toDate().toLocaleDateString('ar-SA') : 'غير محدد';
            
            postsHTML += `
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <span class="badge ${post.isPaidContent ? 'bg-warning' : 'bg-success'}">
                                    <i class="fas fa-${post.isPaidContent ? 'lock' : 'unlock'}"></i>
                                    ${post.isPaidContent ? 'مدفوع' : 'مجاني'}
                                </span>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="editPost('${doc.id}')">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deletePost('${doc.id}')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                            <h5 class="card-title">${post.title || 'بدون عنوان'}</h5>
                            <p class="card-text text-muted">${(post.description || '').substring(0, 100)}...</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> ${postDate}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
        postsHTML += '</div>';
        
        postsListContainer.innerHTML = postsHTML;
        
    } catch (error) {
        console.error('خطأ في تحميل المنشورات:', error);
        postsListContainer.innerHTML = `
            <div class="text-center py-4 text-danger">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5>حدث خطأ في تحميل المنشورات</h5>
                <button class="btn btn-outline-primary" onclick="loadPostsSection()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// ===== إعداد الشريط الجانبي للهواتف =====
function setupMobileSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    sidebarToggle.addEventListener('click', () => {
        sidebar.classList.toggle('show');
    });
    
    // إغلاق الشريط الجانبي عند النقر خارجه
    document.addEventListener('click', (e) => {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        }
    });
}

// ===== دوال مساعدة =====

// إظهار نافذة منشور جديد
function showNewPostModal() {
    // سيتم تنفيذها لاحقاً
    showNotification('ميزة إنشاء المنشورات ستكون متاحة قريباً', 'info');
}

// تعديل منشور
function editPost(postId) {
    // سيتم تنفيذها لاحقاً
    showNotification('ميزة تعديل المنشورات ستكون متاحة قريباً', 'info');
}

// حذف منشور
function deletePost(postId) {
    // سيتم تنفيذها لاحقاً
    if (confirm('هل أنت متأكد من حذف هذا المنشور؟')) {
        showNotification('ميزة حذف المنشورات ستكون متاحة قريباً', 'info');
    }
}

// تحميل أقسام أخرى (ستكون متاحة في التحديثات القادمة)
async function loadSubscribersSection() {
    document.getElementById('subscribersList').innerHTML = '<p class="text-center">قسم المشتركين قيد التطوير</p>';
}

async function loadRequestsSection() {
    document.getElementById('requestsList').innerHTML = '<p class="text-center">قسم طلبات الاشتراك قيد التطوير</p>';
}

async function loadChatSection() {
    document.getElementById('chatList').innerHTML = '<p class="text-center">قسم المحادثات قيد التطوير</p>';
}

async function loadProfileSection() {
    document.getElementById('profileForm').innerHTML = '<p class="text-center">قسم الملف الشخصي قيد التطوير</p>';
}

// تصدير الدوال للاستخدام العام
window.showSection = showSection;
window.showNewPostModal = showNewPostModal;
window.editPost = editPost;
window.deletePost = deletePost;

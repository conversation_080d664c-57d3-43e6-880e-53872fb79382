# دليل إعداد منصة المحللين الماليين

## خطوات الإعداد السريع

### 1. إعداد Firebase

#### أ. إنشاء/تحديث المشروع
```bash
# إذا لم يكن لديك Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع (إذا لم يكن موجود)
firebase init

# أو ربط المشروع الموجود
firebase use jorinvforex
```

#### ب. تحديث إعدادات Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/u/0/project/jorinvforex/overview)
2. انسخ إعدادات المشروع من Project Settings > General > Your apps
3. استبدل الإعدادات في جميع ملفات HTML:

```javascript
const firebaseConfig = {
    apiKey: "your-actual-api-key",
    authDomain: "jorinvforex.firebaseapp.com", 
    projectId: "jorinvforex",
    storageBucket: "jorinvforex.appspot.com",
    messagingSenderId: "your-actual-sender-id",
    appId: "your-actual-app-id"
};
```

### 2. إعداد Authentication

1. في Firebase Console > Authentication > Sign-in method
2. فعّل **Email/Password**
3. أضف النطاقات المصرح بها إذا لزم الأمر

### 3. إعداد Firestore Database

#### أ. إنشاء قاعدة البيانات
1. اذهب إلى Firestore Database
2. أنشئ قاعدة بيانات في وضع الإنتاج
3. اختر المنطقة الأقرب (مثل europe-west1)

#### ب. تطبيق قواعد الأمان
```bash
firebase deploy --only firestore:rules
```

#### ج. تطبيق الفهارس
```bash
firebase deploy --only firestore:indexes
```

### 4. إعداد Storage

#### أ. تفعيل Storage
1. اذهب إلى Storage في Firebase Console
2. ابدأ في وضع الإنتاج
3. اختر المنطقة نفسها

#### ب. تطبيق قواعد Storage
```bash
firebase deploy --only storage
```

### 5. إعداد Cloud Functions

#### أ. تثبيت التبعيات
```bash
cd functions
npm install
```

#### ب. نشر Functions
```bash
firebase deploy --only functions
```

### 6. إعداد Hosting (اختياري)

```bash
firebase deploy --only hosting
```

## إنشاء المستخدم الأول (Admin)

### الطريقة 1: عبر الكود
1. سجل حساب جديد عبر صفحة التسجيل
2. اذهب إلى Firestore Console
3. ابحث عن المستخدم في مجموعة `users`
4. غيّر حقل `role` من `subscriber` إلى `admin`

### الطريقة 2: عبر Firebase Console
1. اذهب إلى Authentication > Users
2. أضف مستخدم جديد
3. انسخ UID المستخدم
4. اذهب إلى Firestore > users
5. أنشئ وثيقة جديدة بـ UID كمعرف:

```json
{
  "name": "المدير العام",
  "email": "<EMAIL>",
  "role": "admin",
  "createdAt": "2024-01-01T00:00:00Z",
  "profilePicURL": "",
  "coverPicURL": "",
  "themeColor": "#66CCFF"
}
```

## اختبار النظام

### 1. إنشاء محلل تجريبي
1. سجل حساب جديد كمحلل
2. أكمل الملف الشخصي
3. أنشئ منشور تجريبي

### 2. إنشاء مشترك تجريبي  
1. سجل حساب جديد كمشترك
2. ابحث عن المحلل
3. أرسل طلب اشتراك
4. اقبل الطلب من حساب المحلل

### 3. اختبار الميزات
- [ ] تسجيل الدخول/الخروج
- [ ] إنشاء المنشورات
- [ ] طلبات الاشتراك
- [ ] تحويل المنشورات المدفوعة لمجانية
- [ ] النشر والتحديث

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في إعدادات Firebase
```
Error: Firebase configuration not found
```
**الحل:** تأكد من تحديث إعدادات Firebase في جميع ملفات HTML

#### 2. خطأ في قواعد Firestore
```
Error: Missing or insufficient permissions
```
**الحل:** تأكد من نشر قواعد Firestore وأن المستخدم لديه الدور المناسب

#### 3. خطأ في Cloud Functions
```
Error: Function not found
```
**الحل:** تأكد من نشر Functions بنجاح

### تشغيل محلي للاختبار

```bash
# تشغيل جميع المحاكيات
firebase emulators:start

# تشغيل محاكيات محددة
firebase emulators:start --only auth,firestore,functions
```

## مراقبة الأداء

### 1. Firebase Console
- راقب الاستخدام في Overview
- تحقق من الأخطاء في Functions logs
- راقب قاعدة البيانات في Firestore

### 2. Google Analytics (اختياري)
```javascript
// أضف في head كل صفحة HTML
gtag('config', 'GA_MEASUREMENT_ID');
```

## النسخ الاحتياطي

### 1. تصدير Firestore
```bash
gcloud firestore export gs://jorinvforex.appspot.com/backups/$(date +%Y%m%d)
```

### 2. نسخ احتياطي للكود
```bash
git add .
git commit -m "Backup $(date)"
git push origin main
```

## التحديثات المستقبلية

### إضافة ميزات جديدة
1. أنشئ فرع جديد
2. طور الميزة
3. اختبر محلياً
4. انشر للإنتاج

### تحديث التبعيات
```bash
# تحديث Firebase
npm update firebase

# تحديث Functions
cd functions && npm update
```

## الأمان

### نصائح مهمة
- [ ] لا تشارك مفاتيح API الخاصة
- [ ] راجع قواعد الأمان بانتظام
- [ ] فعّل التحقق بخطوتين للحسابات المهمة
- [ ] راقب الاستخدام غير المعتاد

### مراجعة دورية
- مراجعة شهرية لقواعد الأمان
- مراجعة أسبوعية للوجات Functions
- نسخ احتياطي أسبوعي

---

## الدعم

للمساعدة:
- راجع [وثائق Firebase](https://firebase.google.com/docs)
- تحقق من [GitHub Issues](https://github.com/your-repo/issues)
- تواصل مع فريق التطوير

**ملاحظة:** هذا الدليل يفترض استخدام Firebase CLI والوصول لـ Firebase Console.

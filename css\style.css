/* ===== تحسينات عامة للموقع ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
    background-color: #0a192f;
    color: #e6f1ff;
    overflow-x: hidden;
    line-height: 1.6;
}

/* ===== تحسينات التفاعل والحركة ===== */
.service-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

.service-card:hover {
    transform: translateY(-10px) scale(1.02) !important;
    border-color: rgba(100, 255, 218, 0.5) !important;
    box-shadow: 0 25px 50px rgba(100, 255, 218, 0.25) !important;
    background: rgba(10, 25, 47, 0.8) !important;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 0 15px 35px rgba(100, 255, 218, 0.4) !important;
}

.stat-item {
    transition: all 0.3s ease;
    will-change: transform;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.05) !important;
    background: rgba(100, 255, 218, 0.1) !important;
    border-color: rgba(100, 255, 218, 0.4) !important;
    box-shadow: 0 10px 25px rgba(100, 255, 218, 0.2) !important;
}

/* ===== الانيميشن العام ===== */
@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-15px) rotate(2deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* ===== تحسينات الأداء ===== */
.hero-section,
.service-card,
.three-d-plan-card,
.contact-card-3d {
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== تحسينات الصور ===== */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* ===== تحسينات الأزرار ===== */
.btn, .hero-btn-primary, .hero-btn-secondary, .splash-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    cursor: pointer;
    border: none;
    outline: none;
}

.btn:hover, .hero-btn-primary:hover, .hero-btn-secondary:hover, .splash-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(100, 255, 218, 0.3);
}

.btn:active, .hero-btn-primary:active, .hero-btn-secondary:active, .splash-btn:active {
    transform: translateY(0);
}

/* ===== تحسينات النصوص ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* ===== تحسينات الروابط ===== */
a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    text-decoration: none;
}

/* ===== تحسينات الشاشات الكبيرة (Desktop) ===== */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .hero-section h1 {
        font-size: 4.5rem;
    }

    .hero-section p {
        font-size: 1.6rem;
    }

    .service-card {
        padding: 2.5rem;
    }

    .three-d-plan-card {
        padding: 2.5rem;
    }
}

/* ===== تحسينات الشاشات الكبيرة (Large Desktop) ===== */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }

    .hero-section h1 {
        font-size: 4rem;
    }

    .hero-section p {
        font-size: 1.4rem;
    }
}

/* ===== تحسينات الأجهزة اللوحية الكبيرة ===== */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }

    .hero-section h1 {
        font-size: 3.5rem;
    }

    .hero-section p {
        font-size: 1.3rem;
    }

    .service-card {
        padding: 2rem;
        margin-bottom: 25px;
    }

    .three-d-plan-card {
        padding: 2rem;
        margin-bottom: 25px;
    }
}

/* ===== تحسينات الأجهزة اللوحية المتوسطة ===== */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
    }

    .col-lg-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .three-d-elements {
        min-height: 300px;
        margin-top: 50px;
    }

    .contact-card-3d {
        padding: 30px;
    }

    .three-d-plan-card.featured {
        transform: translateY(0) translateZ(30px);
    }

    .three-d-plan-card {
        margin-bottom: 30px;
        padding: 1.8rem;
    }

    #3d-plans {
        padding: 80px 0;
    }

    .hero-section h1 {
        font-size: 3rem;
    }

    .hero-section p {
        font-size: 1.2rem;
    }

    .service-card {
        padding: 1.8rem;
        margin-bottom: 20px;
    }

    .navbar-nav {
        text-align: center;
        padding: 15px 0;
    }

    .nav-link {
        padding: 12px 20px !important;
        margin: 5px 0;
        border-radius: 8px;
    }
}

/* ===== تحسينات الهواتف الكبيرة والأجهزة اللوحية الصغيرة ===== */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 15px;
    }

    .splash-content {
        padding: 25px !important;
        max-width: 95% !important;
        border-radius: 15px !important;
    }

    .splash-content h2 {
        font-size: 1.6rem !important;
        margin-bottom: 15px !important;
    }

    .feature-card {
        margin-bottom: 12px;
        padding: 12px !important;
        border-radius: 10px !important;
    }

    .hero-section {
        padding: 80px 0 !important;
    }

    .hero-section h1 {
        font-size: 2.5rem !important;
        line-height: 1.2 !important;
        margin-bottom: 1.5rem !important;
    }

    .hero-section p {
        font-size: 1.1rem !important;
        margin-bottom: 2rem !important;
    }

    .hero-section .hero-buttons {
        flex-direction: column !important;
        align-items: center !important;
        gap: 15px !important;
    }

    .hero-btn-primary,
    .hero-btn-secondary {
        width: 100% !important;
        max-width: 300px !important;
        padding: 15px 30px !important;
        font-size: 1.1rem !important;
    }

    .glass-card {
        padding: 1.8rem !important;
        margin-bottom: 20px !important;
        border-radius: 15px !important;
    }

    .service-card {
        margin-bottom: 20px;
        padding: 1.8rem !important;
        border-radius: 15px !important;
    }

    .service-icon {
        width: 70px !important;
        height: 70px !important;
        margin-bottom: 1.2rem !important;
    }

    .service-title {
        font-size: 1.4rem !important;
        margin-bottom: 1rem !important;
    }

    .service-text {
        font-size: 1rem !important;
        line-height: 1.6 !important;
    }

    .navbar {
        padding: 0.8rem 1rem !important;
    }

    .navbar-brand img {
        height: 38px !important;
    }

    .navbar-nav {
        text-align: center;
        padding: 15px 0;
    }

    .nav-link {
        padding: 12px 18px !important;
        font-size: 1rem !important;
        margin: 3px 0;
        border-radius: 8px;
    }

    .three-d-plan-card {
        margin-bottom: 25px !important;
        padding: 2rem !important;
        transform: none !important;
    }

    .contact-card-3d {
        padding: 2rem !important;
        margin-bottom: 20px !important;
        transform: none !important;
    }

    .section-title {
        font-size: 2.2rem !important;
        margin-bottom: 1.5rem !important;
    }

    .stats {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 15px !important;
        justify-content: center !important;
    }

    .stat-item {
        flex: 1 1 calc(50% - 15px) !important;
        min-width: 200px !important;
        padding: 20px !important;
        border-radius: 12px !important;
    }

    .stat-item h3 {
        font-size: 2.5rem !important;
        margin-bottom: 8px !important;
    }

    .stat-item p {
        font-size: 1rem !important;
    }
}

/* ===== تحسينات الهواتف المتوسطة ===== */
@media (max-width: 575px) {
    html {
        font-size: 14px;
    }

    body {
        font-size: 14px;
        line-height: 1.5;
    }

    .container {
        padding: 0 12px !important;
        max-width: 100% !important;
    }

    /* تحسين شاشة البداية */
    .splash-content {
        padding: 18px !important;
        margin: 8px !important;
        max-width: 96% !important;
        border-radius: 12px !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
    }

    .splash-content h2 {
        font-size: 1.4rem !important;
        margin-bottom: 12px !important;
        line-height: 1.3 !important;
    }

    .splash-content p {
        font-size: 0.85rem !important;
        margin-bottom: 8px !important;
        line-height: 1.4 !important;
    }

    .splash-content h3 {
        font-size: 0.9rem !important;
        margin-bottom: 8px !important;
    }

    .feature-card {
        padding: 8px !important;
        margin-bottom: 6px !important;
        border-radius: 8px !important;
        min-width: 70px !important;
    }

    .feature-card p {
        font-size: 0.7rem !important;
        margin: 0 !important;
    }

    .splash-btn {
        padding: 10px 20px !important;
        font-size: 0.8rem !important;
        margin: 4px !important;
        width: 100% !important;
        max-width: 200px !important;
        border-radius: 8px !important;
        display: block !important;
    }

    /* تحسين قسم الهيرو */
    .hero-section {
        padding: 50px 0 !important;
        min-height: 80vh !important;
    }

    .hero-section h1 {
        font-size: 2rem !important;
        line-height: 1.1 !important;
        margin-bottom: 1rem !important;
        padding: 0 10px !important;
    }

    .hero-section .hero-subtitle {
        font-size: 0.6em !important;
        display: block !important;
        margin-top: 0.5rem !important;
    }

    .hero-section p {
        font-size: 0.95rem !important;
        margin-bottom: 1.5rem !important;
        padding: 0 10px !important;
        max-width: 95% !important;
    }

    .hero-section .hero-buttons {
        flex-direction: column !important;
        align-items: center !important;
        gap: 12px !important;
        padding: 0 15px !important;
    }

    .hero-btn-primary,
    .hero-btn-secondary {
        padding: 12px 25px !important;
        font-size: 0.9rem !important;
        width: 100% !important;
        max-width: 280px !important;
        text-align: center !important;
        border-radius: 12px !important;
    }

    /* تحسين شريط التنقل */
    .navbar {
        padding: 0.6rem 1rem !important;
    }

    .navbar-brand {
        font-size: 1.3rem !important;
    }

    .navbar-brand img {
        height: 32px !important;
        margin-left: 8px !important;
    }

    .navbar-toggler {
        border: none !important;
        padding: 4px 8px !important;
        font-size: 1.1rem !important;
    }

    .navbar-nav {
        text-align: center;
        padding: 12px 0;
        background: rgba(10, 25, 47, 0.95) !important;
        border-radius: 10px !important;
        margin-top: 10px !important;
    }

    .nav-link {
        padding: 10px 15px !important;
        font-size: 0.9rem !important;
        margin: 2px 5px !important;
        border-radius: 8px !important;
    }

    .dropdown-menu {
        position: static !important;
        transform: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        background: rgba(10, 25, 47, 0.98) !important;
        border-radius: 8px !important;
        margin: 5px 0 !important;
        box-shadow: none !important;
    }

    .dropdown-item {
        padding: 10px 15px !important;
        font-size: 0.85rem !important;
        border-radius: 6px !important;
        margin: 2px 5px !important;
    }

    /* تحسين بطاقات الخدمات */
    .service-card {
        margin-bottom: 18px;
        padding: 1.3rem !important;
        border-radius: 15px !important;
    }

    .service-icon {
        width: 55px !important;
        height: 55px !important;
        margin-bottom: 1rem !important;
        font-size: 1.5rem !important;
    }

    .service-title {
        font-size: 1.2rem !important;
        margin-bottom: 0.8rem !important;
        line-height: 1.3 !important;
    }

    .service-text {
        font-size: 0.85rem !important;
        line-height: 1.5 !important;
    }

    /* تحسين قسم من نحن */
    .glass-card {
        padding: 1.3rem !important;
        margin-bottom: 18px !important;
        border-radius: 15px !important;
    }

    .section-title {
        font-size: 1.7rem !important;
        margin-bottom: 1rem !important;
        line-height: 1.2 !important;
        padding: 0 10px !important;
    }

    #typed-about {
        font-size: 0.9rem !important;
        line-height: 1.6 !important;
    }

    /* تحسين الإحصائيات */
    .stats {
        flex-direction: column !important;
        gap: 12px !important;
        margin-top: 1.5rem !important;
    }

    .stat-item {
        margin: 0 !important;
        flex: none !important;
        padding: 18px !important;
        border-radius: 12px !important;
        text-align: center !important;
    }

    .stat-item h3 {
        font-size: 2rem !important;
        margin-bottom: 6px !important;
    }

    .stat-item p {
        font-size: 0.85rem !important;
        margin: 0 !important;
    }
}

    /* تحسين الباقات */
    .three-d-plan-card {
        margin-bottom: 20px !important;
        padding: 1.3rem !important;
        transform: none !important;
        border-radius: 15px !important;
    }

    .plan-header h3 {
        font-size: 1.3rem !important;
        margin-bottom: 8px !important;
    }

    .plan-header div {
        font-size: 1.8rem !important;
        margin-bottom: 6px !important;
    }

    .plan-features {
        margin: 15px 0 !important;
    }

    .plan-features li {
        font-size: 0.85rem !important;
        padding: 6px 0 !important;
        line-height: 1.4 !important;
    }

    .plan-btn {
        padding: 12px 25px !important;
        font-size: 0.9rem !important;
        width: 100% !important;
        border-radius: 10px !important;
    }

    /* تحسين قسم التواصل */
    .contact-card-3d {
        padding: 1.3rem !important;
        margin-bottom: 18px !important;
        transform: none !important;
        border-radius: 15px !important;
    }

    .contact-card-3d h4 {
        font-size: 1.2rem !important;
        margin-bottom: 10px !important;
    }

    .contact-card-3d p {
        font-size: 0.85rem !important;
        line-height: 1.5 !important;
    }

    .contact-card-3d a {
        font-size: 0.9rem !important;
        padding: 10px 20px !important;
    }

    /* تحسين الفوتر */
    .footer-about {
        text-align: center;
        margin-bottom: 20px;
        padding: 0 10px;
    }

    .footer-about h5 {
        font-size: 1.2rem !important;
        margin-bottom: 10px !important;
    }

    .footer-about p {
        font-size: 0.85rem !important;
        line-height: 1.5 !important;
    }

    .social-links {
        justify-content: center;
        margin-top: 15px;
        gap: 10px !important;
    }

    .social-links a {
        margin: 0 !important;
        width: 38px !important;
        height: 38px !important;
        font-size: 1rem !important;
        border-radius: 50% !important;
    }

    /* تحسين العناوين العامة */
    h1 {
        font-size: 1.8rem !important;
        line-height: 1.2 !important;
    }

    h2 {
        font-size: 1.5rem !important;
        line-height: 1.2 !important;
    }

    h3 {
        font-size: 1.2rem !important;
        line-height: 1.3 !important;
    }

    h4 {
        font-size: 1rem !important;
        line-height: 1.3 !important;
    }

    h5 {
        font-size: 0.9rem !important;
    }

    h6 {
        font-size: 0.8rem !important;
    }

    /* تحسين المسافات العامة */
    section {
        padding: 40px 0 !important;
        scroll-margin-top: 80px !important;
    }

    .mb-4 {
        margin-bottom: 1rem !important;
    }

    .mb-5 {
        margin-bottom: 1.5rem !important;
    }

    .py-5 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }
}

/* ===== تحسينات الهواتف الصغيرة جداً ===== */
@media (max-width: 400px) {
    html {
        font-size: 13px;
    }

    .container {
        padding: 0 8px !important;
    }

    /* تحسين شاشة البداية للهواتف الصغيرة جداً */
    .splash-content {
        padding: 12px !important;
        margin: 5px !important;
        max-width: 98% !important;
        border-radius: 10px !important;
    }

    .splash-content h2 {
        font-size: 1.2rem !important;
        margin-bottom: 10px !important;
    }

    .splash-content p {
        font-size: 0.75rem !important;
        margin-bottom: 6px !important;
    }

    .splash-content h3 {
        font-size: 0.8rem !important;
        margin-bottom: 6px !important;
    }

    .feature-card {
        padding: 6px !important;
        margin-bottom: 4px !important;
        min-width: 60px !important;
    }

    .feature-card p {
        font-size: 0.65rem !important;
    }

    .splash-btn {
        padding: 8px 16px !important;
        font-size: 0.75rem !important;
        max-width: 180px !important;
        border-radius: 6px !important;
    }

    /* تحسين قسم الهيرو للهواتف الصغيرة جداً */
    .hero-section {
        padding: 40px 0 !important;
        min-height: 70vh !important;
    }

    .hero-section h1 {
        font-size: 1.6rem !important;
        line-height: 1.1 !important;
        margin-bottom: 0.8rem !important;
        padding: 0 8px !important;
    }

    .hero-section .hero-subtitle {
        font-size: 0.55em !important;
    }

    .hero-section p {
        font-size: 0.85rem !important;
        margin-bottom: 1.2rem !important;
        padding: 0 8px !important;
    }

    .hero-btn-primary,
    .hero-btn-secondary {
        padding: 10px 20px !important;
        font-size: 0.8rem !important;
        max-width: 240px !important;
        border-radius: 10px !important;
    }

    /* تحسين شريط التنقل للهواتف الصغيرة جداً */
    .navbar-brand {
        font-size: 1.1rem !important;
    }

    .navbar-brand img {
        height: 28px !important;
        margin-left: 6px !important;
    }

    .nav-link {
        padding: 8px 12px !important;
        font-size: 0.8rem !important;
    }

    /* تحسين بطاقات الخدمات للهواتف الصغيرة جداً */
    .service-card {
        padding: 1rem !important;
        margin-bottom: 15px;
    }

    .service-icon {
        width: 45px !important;
        height: 45px !important;
        font-size: 1.3rem !important;
    }

    .service-title {
        font-size: 1rem !important;
        margin-bottom: 0.6rem !important;
    }

    .service-text {
        font-size: 0.75rem !important;
        line-height: 1.4 !important;
    }

    /* تحسين الإحصائيات للهواتف الصغيرة جداً */
    .stat-item {
        padding: 15px !important;
    }

    .stat-item h3 {
        font-size: 1.6rem !important;
        margin-bottom: 4px !important;
    }

    .stat-item p {
        font-size: 0.75rem !important;
    }

    /* تحسين العناوين للهواتف الصغيرة جداً */
    h1 {
        font-size: 1.5rem !important;
    }

    h2 {
        font-size: 1.3rem !important;
    }

    h3 {
        font-size: 1.1rem !important;
    }

    h4 {
        font-size: 0.95rem !important;
    }

    /* تحسين المسافات للهواتف الصغيرة جداً */
    section {
        padding: 30px 0 !important;
    }

    .section-title {
        font-size: 1.4rem !important;
        padding: 0 8px !important;
    }

    .glass-card {
        padding: 1rem !important;
        margin-bottom: 15px !important;
    }

    .three-d-plan-card {
        padding: 1rem !important;
        margin-bottom: 15px !important;
    }

    .contact-card-3d {
        padding: 1rem !important;
        margin-bottom: 15px !important;
    }
}

/* ===== تحسينات خاصة للأداء والتحسين ===== */

/* تحسين الخطوط والنصوص */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسين الصور للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .navbar-brand img,
    .service-icon img,
    .splash-content img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تحسين الطباعة */
@media print {
    .navbar,
    .splash-screen,
    .hero-section video,
    .scroll-progress,
    #market-sessions {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
        font-size: 12pt !important;
        line-height: 1.4 !important;
    }

    .container {
        max-width: 100% !important;
        padding: 0 !important;
    }

    .service-card,
    .three-d-plan-card,
    .contact-card-3d {
        border: 1px solid #ccc !important;
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    h1, h2, h3, h4, h5, h6 {
        color: black !important;
        page-break-after: avoid;
    }

    a {
        color: black !important;
        text-decoration: underline !important;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1600px) {
    .container {
        max-width: 1400px;
    }

    .hero-section h1 {
        font-size: 5rem;
    }

    .hero-section p {
        font-size: 1.8rem;
    }

    .service-card {
        padding: 3rem;
    }

    .three-d-plan-card {
        padding: 3rem;
    }

    .section-title {
        font-size: 3.5rem;
    }
}

/* تحسينات للشاشات فائقة العرض */
@media (min-width: 2000px) {
    .container {
        max-width: 1600px;
    }

    .hero-section h1 {
        font-size: 6rem;
    }

    .hero-section p {
        font-size: 2rem;
    }
}

/* تحسينات للوضع الأفقي على الهواتف */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        padding: 30px 0 !important;
        min-height: 60vh !important;
    }

    .hero-section h1 {
        font-size: 1.8rem !important;
        margin-bottom: 0.8rem !important;
    }

    .hero-section p {
        font-size: 0.9rem !important;
        margin-bottom: 1rem !important;
    }

    .hero-section .hero-buttons {
        gap: 10px !important;
        margin-top: 1rem !important;
    }

    .hero-btn-primary,
    .hero-btn-secondary {
        padding: 8px 20px !important;
        font-size: 0.8rem !important;
        max-width: 200px !important;
    }

    .splash-content {
        max-height: 85vh !important;
        padding: 15px !important;
    }

    .navbar {
        padding: 0.4rem 1rem !important;
    }

    section {
        padding: 30px 0 !important;
    }
}

/* تحسينات للوضع العمودي على الأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
    .hero-section h1 {
        font-size: 3.2rem !important;
    }

    .hero-section p {
        font-size: 1.3rem !important;
    }

    .service-card {
        padding: 2rem !important;
    }

    .three-d-plan-card {
        padding: 2rem !important;
    }
}

/* تحسينات خاصة للتمرير السلس */
@supports (scroll-behavior: smooth) {
    html {
        scroll-behavior: smooth;
    }
}

/* تحسينات للمتصفحات القديمة */
@supports not (backdrop-filter: blur(10px)) {
    .navbar,
    .splash-content,
    .glass-card {
        background: rgba(10, 25, 47, 0.95) !important;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    /* الموقع مصمم للوضع المظلم بالفعل، لكن يمكن إضافة تحسينات إضافية */
    body {
        background-color: #0a192f;
    }
}

/* تحسينات للوضع الفاتح (في حالة الحاجة) */
@media (prefers-color-scheme: light) {
    /* يمكن إضافة تحسينات للوضع الفاتح إذا لزم الأمر */
}

/* تحسينات لتوفير البيانات */
@media (prefers-reduced-data: reduce) {
    .hero-section video {
        display: none !important;
    }

    .luxury-bg-effects,
    .three-d-elements::before,
    .three-d-elements::after {
        display: none !important;
    }

    * {
        animation: none !important;
        transition: none !important;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    .service-card,
    .three-d-plan-card,
    .contact-card-3d,
    .glass-card {
        border: 2px solid #64ffda !important;
        background: rgba(10, 25, 47, 0.9) !important;
    }

    .nav-link,
    .btn,
    .hero-btn-primary,
    .hero-btn-secondary {
        border: 1px solid #64ffda !important;
    }

    h1, h2, h3, h4, h5, h6 {
        color: #ffffff !important;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
    }
}
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة اللوت للذهب (XAUUSD) - فوركس الأردن</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-bg: #0a192f;
            --secondary-bg: #112240;
            --accent-color: #64ffda;
            --main-text: #e6f1ff;
            --secondary-text: #8892b0;
            --card-bg: rgba(17, 34, 64, 0.6);
            --border-color: rgba(100, 255, 218, 0.2);
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0a192f 0%, #112240 50%, #1e3a8a 100%);
            color: var(--main-text);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* تحسين التنقل السلس */
        a[href^="#"] {
            scroll-behavior: smooth;
        }

        /* تصميم الهيدر الموحد */
        .navbar {
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(100, 255, 218, 0.2);
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 900;
            color: #64ffda !important;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 10px rgba(100, 255, 218, 0.5));
        }

        .nav-link {
            color: #ccd6f6 !important;
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 12px 18px !important;
            border-radius: 8px;
            margin: 0 5px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            transition: all 0.4s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 85%;
        }

        .nav-link:hover {
            color: #64ffda !important;
            transform: translateY(-3px);
            background: rgba(100, 255, 218, 0.1);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.2);
        }

        .nav-link.active {
            color: #64ffda !important;
        }

        /* تصميم القائمة المنسدلة للأدوات */
        .dropdown-menu {
            border: none !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6) !important;
            background: rgba(10, 25, 47, 0.95) !important;
            backdrop-filter: blur(15px) !important;
            border-radius: 10px !important;
            border: 1px solid rgba(100, 255, 218, 0.3) !important;
        }

        .dropdown-item {
            border: none !important;
            background: transparent !important;
            color: #ccd6f6 !important;
            transition: all 0.3s ease !important;
            border-radius: 6px !important;
            margin: 2px 8px !important;
        }

        .dropdown-item:hover {
            background: rgba(100, 255, 218, 0.1) !important;
            color: #64ffda !important;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.1) !important;
        }

        .dropdown-item:hover i {
            color: #64ffda !important;
            transform: scale(1.1);
            filter: drop-shadow(0 0 5px rgba(100, 255, 218, 0.5));
        }

        .dropdown-toggle::after {
            border-top: 4px solid;
            border-right: 3px solid transparent;
            border-left: 3px solid transparent;
            margin-right: 3px;
        }

        /* تأثيرات انتقالية للقائمة المنسدلة */
        .dropdown-menu {
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            display: block !important;
            visibility: hidden;
        }

        .dropdown:hover .dropdown-menu,
        .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* تحسينات للهواتف - الهيدر */
        @media (max-width: 768px) {
            .dropdown-menu {
                position: static !important;
                transform: none !important;
                opacity: 1 !important;
                visibility: visible !important;
                background: rgba(10, 25, 47, 0.98) !important;
                border-radius: 8px !important;
                margin: 5px 0 !important;
            }
            
            .dropdown-item {
                padding: 12px 20px !important;
                font-size: 0.9rem !important;
            }
        }

        /* تصميم البطاقات */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 10;
        }

        .card:hover {
            border-color: rgba(100, 255, 218, 0.5);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
            transform: translateY(-5px);
        }

        .card-header {
            background: rgba(100, 255, 218, 0.1);
            border-bottom: 1px solid var(--border-color);
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }

        .card-body {
            padding: 30px;
        }

        /* تصميم النماذج */
        .form-control,
        .form-select {
            background: rgba(10, 25, 47, 0.7);
            border: 1px solid var(--border-color);
            color: var(--main-text);
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus,
        .form-select:focus {
            background: rgba(10, 25, 47, 0.9);
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(100, 255, 218, 0.25);
            color: var(--main-text);
        }

        .form-control:hover,
        .form-select:hover {
            border-color: rgba(100, 255, 218, 0.4);
            box-shadow: 0 2px 8px rgba(100, 255, 218, 0.1);
        }

        .form-label {
            color: var(--main-text);
            font-weight: 600;
            margin-bottom: 8px;
        }

        /* تصميم الأزرار */
        .btn-primary {
            background: linear-gradient(45deg, var(--accent-color), #57cbff);
            border: none;
            color: var(--primary-bg);
            font-weight: 700;
            padding: 12px 30px;
            border-radius: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 255, 218, 0.4);
            background: linear-gradient(45deg, #57cbff, var(--accent-color));
        }

        /* تصميم النتائج */
        .result-card {
            background: linear-gradient(135deg, rgba(100, 255, 218, 0.1), rgba(87, 203, 255, 0.05));
            border: 2px solid var(--accent-color);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 15px;
        }

        .result-value {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--main-text);
            margin: 15px 0;
        }

        .result-details {
            font-size: 1.1rem;
            color: var(--secondary-text);
            line-height: 1.6;
        }

        /* تصميم التحذيرات */
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            margin-top: 15px;
        }

        .alert-warning {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #ffc107;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }

        .alert-info {
            background: rgba(13, 202, 240, 0.1);
            border: 1px solid rgba(13, 202, 240, 0.3);
            color: #0dcaf0;
        }

        /* العلامة المائية المتحركة */
        .watermark {
            position: fixed;
            top: 0;
            left: 0;
            font-size: 8rem;
            font-weight: 900;
            color: rgba(100, 255, 218, 0.04);
            z-index: 0;
            pointer-events: none;
            user-select: none;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            white-space: nowrap;
            animation: moveAround 25s ease-in-out infinite, shimmer 6s ease-in-out infinite;
            -webkit-text-stroke: 1px rgba(100, 255, 218, 0.02);
            filter: drop-shadow(0 0 20px rgba(100, 255, 218, 0.1));
            transform-origin: center;
        }

        /* تأثير الوميض/الشيمر */
        @keyframes shimmer {
            0% {
                opacity: 0.02;
                text-shadow: 0 0 10px rgba(100, 255, 218, 0.1);
            }
            25% {
                opacity: 0.05;
                text-shadow: 0 0 20px rgba(100, 255, 218, 0.15);
            }
            50% {
                opacity: 0.08;
                text-shadow: 0 0 30px rgba(100, 255, 218, 0.2);
            }
            75% {
                opacity: 0.05;
                text-shadow: 0 0 20px rgba(100, 255, 218, 0.15);
            }
            100% {
                opacity: 0.02;
                text-shadow: 0 0 10px rgba(100, 255, 218, 0.1);
            }
        }

        /* تأثير إضافي للعلامة المائية */
        .watermark::before {
            content: 'فوركس الأردن';
            position: absolute;
            top: 0;
            left: 0;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(100, 255, 218, 0.05) 50%,
                transparent 70%
            );
            background-size: 200% 200%;
            -webkit-background-clip: text;
            background-clip: text;
            animation: wave 3s ease-in-out infinite;
        }

        @keyframes wave {
            0% {
                background-position: -200% -200%;
            }
            50% {
                background-position: 200% 200%;
            }
            100% {
                background-position: -200% -200%;
            }
        }

        /* حركة العلامة المائية في جميع أنحاء الشاشة */
        @keyframes moveAround {
            0% {
                transform: translate(10vw, 10vh) rotate(-45deg) scale(0.8);
                opacity: 0.03;
            }
            12.5% {
                transform: translate(80vw, 15vh) rotate(-30deg) scale(1);
                opacity: 0.05;
            }
            25% {
                transform: translate(85vw, 70vh) rotate(-15deg) scale(1.1);
                opacity: 0.04;
            }
            37.5% {
                transform: translate(70vw, 85vh) rotate(0deg) scale(0.9);
                opacity: 0.06;
            }
            50% {
                transform: translate(15vw, 80vh) rotate(15deg) scale(1.2);
                opacity: 0.03;
            }
            62.5% {
                transform: translate(5vw, 60vh) rotate(30deg) scale(0.7);
                opacity: 0.05;
            }
            75% {
                transform: translate(20vw, 20vh) rotate(45deg) scale(1);
                opacity: 0.04;
            }
            87.5% {
                transform: translate(60vw, 10vh) rotate(60deg) scale(0.9);
                opacity: 0.06;
            }
            100% {
                transform: translate(10vw, 10vh) rotate(-45deg) scale(0.8);
                opacity: 0.03;
            }
        }

        /* تحسين للهواتف */
        @media (max-width: 768px) {
            .watermark {
                font-size: 4rem;
                animation: moveAroundMobile 15s linear infinite, shimmer 6s ease-in-out infinite;
            }

            @keyframes moveAroundMobile {
                0% {
                    transform: translate(5vw, 15vh) rotate(-45deg);
                }
                25% {
                    transform: translate(60vw, 20vh) rotate(-15deg);
                }
                50% {
                    transform: translate(70vw, 70vh) rotate(15deg);
                }
                75% {
                    transform: translate(10vw, 75vh) rotate(45deg);
                }
                100% {
                    transform: translate(5vw, 15vh) rotate(-45deg);
                }
            }
        }

        @media (max-width: 480px) {
            .watermark {
                font-size: 2.5rem;
                animation: moveAroundSmall 12s linear infinite, shimmer 6s ease-in-out infinite;
            }

            @keyframes moveAroundSmall {
                0% {
                    transform: translate(5vw, 20vh) rotate(-30deg);
                }
                33% {
                    transform: translate(50vw, 25vh) rotate(0deg);
                }
                66% {
                    transform: translate(60vw, 65vh) rotate(30deg);
                }
                100% {
                    transform: translate(5vw, 20vh) rotate(-30deg);
                }
            }
        }
    </style>
</head>

<body>
    <!-- العلامة المائية المتحركة -->
    <div class="watermark">فوركس الأردن</div>

    <!-- علامات مائية إضافية للتأثير -->
    <div class="watermark" style="animation-delay: -5s; opacity: 0.02;">فوركس الأردن</div>
    <div class="watermark" style="animation-delay: -10s; opacity: 0.015;">فوركس الأردن</div>

    <!-- الهيدر الموحد -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                فوركس الأردن
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                style="border: 1px solid rgba(100, 255, 218, 0.3); padding: 8px 12px;">
                <i class="fas fa-bars" style="color: #64ffda; font-size: 1.2rem;"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" style="font-weight: 500;">
                    <li class="nav-item">
                        <a class="nav-link" href="../#home"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#about"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">من
                            نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#services"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            خدماتنا</a>
                    </li>

                    <!-- قائمة الأدوات المنسدلة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                            style="color: #64ffda; margin: 0 10px; position: relative; padding: 8px 15px !important; display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-tools" style="font-size: 0.9rem;"></i>
                            الأدوات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                            <li>
                                <a class="dropdown-item" href="Risk-management.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-chart-pie" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    خطط إدارة رأس المال
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Lotcalculator.html"
                                    style="color: #64ffda; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px; background: rgba(100, 255, 218, 0.1);">
                                    <i class="fas fa-calculator" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    حاسبة اللوت
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="Economic-news.html"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calendar-alt" style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    التقويم الاقتصادي
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="../#plans"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">باقات
                            الاشتراك</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#partners"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الشركاء</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../#contact"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            تواصل معنا
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container" style="padding-top: 120px; padding-bottom: 50px; position: relative; z-index: 5;">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- بطاقة حاسبة اللوت -->
                <div class="card">
                    <div class="card-header text-center">
                        <h2 class="mb-0" style="color: var(--accent-color); font-weight: 700;">
                            <i class="fas fa-coins" style="margin-left: 10px;"></i>
                            حاسبة اللوت للذهب (XAUUSD)
                        </h2>
                        <p class="mb-0 mt-2" style="color: #ccd6f6; font-size: 0.95rem;">
                            احسب حجم اللوت المناسب لتداول الذهب بناءً على رأس المال ونسبة المخاطرة
                        </p>
                        <div class="alert alert-info mt-2 mb-0" style="font-size: 0.85rem; padding: 10px 15px;">
                            <i class="fas fa-info-circle" style="margin-left: 5px;"></i>
                            <strong>مهم:</strong> في الذهب (XAUUSD)، قيمة النقطة للوت 1.00 = $10 (وليس $1 كما في العملات)
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- نموذج الإدخال -->
                        <form id="lotCalculatorForm">
                            <div class="row">
                                <!-- رأس المال -->
                                <div class="col-md-6 mb-3">
                                    <label for="capital" class="form-label">
                                        <i class="fas fa-dollar-sign" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        رأس المال ($)
                                    </label>
                                    <input type="number" class="form-control" id="capital"
                                           placeholder="مثال: 1000" min="50" step="0.01" required>
                                    <small class="text-muted">الحد الأدنى: $50</small>
                                </div>

                                <!-- نسبة المخاطرة -->
                                <div class="col-md-6 mb-3">
                                    <label for="riskPercentage" class="form-label">
                                        <i class="fas fa-percentage" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        نسبة المخاطرة (%)
                                    </label>
                                    <input type="number" class="form-control" id="riskPercentage"
                                           placeholder="مثال: 2" min="0.1" max="10" step="0.1" required>
                                    <small class="text-muted">يُنصح بـ 1-3% للمبتدئين</small>
                                </div>

                                <!-- عدد نقاط وقف الخسارة -->
                                <div class="col-12 mb-4">
                                    <label for="stopLossPips" class="form-label">
                                        <i class="fas fa-stop-circle" style="color: var(--accent-color); margin-left: 5px;"></i>
                                        عدد نقاط وقف الخسارة
                                    </label>
                                    <input type="number" class="form-control" id="stopLossPips"
                                           placeholder="مثال: 75" min="1" max="1000" step="1" required>
                                    <small class="text-muted">في الذهب: 1 نقطة = 0.1 في حركة السعر (مثال: من 2000.0 إلى 2000.1)</small>
                                </div>

                                <!-- زر الحساب -->
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calculator" style="margin-left: 8px;"></i>
                                        احسب اللوت المناسب
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- منطقة النتائج -->
                        <div id="resultSection" style="display: none;">
                            <div class="result-card">
                                <div class="result-title">
                                    <i class="fas fa-trophy" style="margin-left: 8px;"></i>
                                    اللوت المناسب لك
                                </div>
                                <div class="result-value" id="lotResult">0.00 لوت</div>
                                <div class="result-details" id="resultDetails">
                                    <!-- تفاصيل النتيجة ستظهر هنا -->
                                </div>
                            </div>
                        </div>

                        <!-- منطقة التحذيرات -->
                        <div id="warningSection"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم المعلومات التوضيحية -->
        <div class="row justify-content-center mt-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0" style="color: var(--accent-color);">
                            <i class="fas fa-info-circle" style="margin-left: 8px;"></i>
                            معلومات مهمة عن تداول الذهب
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-section">
                                    <h6 class="info-title">
                                        <i class="fas fa-coins" style="margin-left: 5px;"></i>
                                        قيمة النقطة في الذهب:
                                    </h6>
                                    <ul style="color: var(--secondary-text); margin: 0; padding-right: 20px;">
                                        <li>1.00 لوت = $10.00 لكل نقطة</li>
                                        <li>0.10 لوت = $1.00 لكل نقطة</li>
                                        <li>0.01 لوت = $0.10 لكل نقطة</li>
                                        <li><strong style="color: var(--accent-color);">مثال:</strong> 0.10 لوت × 100 نقطة = $100</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-section">
                                    <h6 class="info-title">
                                        <i class="fas fa-chart-line" style="margin-left: 5px;"></i>
                                        حركة النقاط في الذهب:
                                    </h6>
                                    <ul style="color: var(--secondary-text); margin: 0; padding-right: 20px;">
                                        <li>1 نقطة = 0.1 في السعر</li>
                                        <li>10 نقاط = 1.0 في السعر</li>
                                        <li>100 نقطة = 10.0 في السعر</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="info-section">
                                    <h6 class="info-title">
                                        <i class="fas fa-shield-alt" style="margin-left: 5px;"></i>
                                        حدود اللوت الآمنة:
                                    </h6>
                                    <ul style="color: var(--secondary-text); margin: 0; padding-right: 20px;">
                                        <li>الحد الأدنى: 0.01 لوت</li>
                                        <li>الحد الأقصى: 2.00 لوت</li>
                                        <li>يُنصح بعدم تجاوز 1% مخاطرة للمبتدئين</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-section">
                                    <h6 class="info-title">
                                        <i class="fas fa-lightbulb" style="margin-left: 5px;"></i>
                                        مثال تطبيقي:
                                    </h6>
                                    <div style="color: var(--secondary-text); font-size: 0.9rem;">
                                        <strong style="color: var(--accent-color);">رأس المال:</strong> $1000<br>
                                        <strong style="color: var(--accent-color);">مخاطرة:</strong> 2% = $20<br>
                                        <strong style="color: var(--accent-color);">ستوب:</strong> 100 نقطة<br>
                                        <strong style="color: var(--accent-color);">النتيجة:</strong> 0.02 لوت = $20 خسارة
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تحذير مهم -->
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>
                            <strong>تحذير مهم:</strong> هذه الحاسبة للأغراض التعليمية فقط. تأكد من فهم المخاطر قبل التداول الفعلي.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('lotCalculatorForm');
            const resultSection = document.getElementById('resultSection');
            const warningSection = document.getElementById('warningSection');
            const lotResult = document.getElementById('lotResult');
            const resultDetails = document.getElementById('resultDetails');

            // إعداد القائمة المنسدلة للأدوات
            const toolsDropdown = document.getElementById('toolsDropdown');
            const dropdownMenu = toolsDropdown?.nextElementSibling;

            if (toolsDropdown && dropdownMenu) {
                // إظهار القائمة عند hover
                toolsDropdown.parentElement.addEventListener('mouseenter', function() {
                    dropdownMenu.classList.add('show');
                });

                // إخفاء القائمة عند مغادرة المنطقة
                toolsDropdown.parentElement.addEventListener('mouseleave', function() {
                    dropdownMenu.classList.remove('show');
                });

                // منع إغلاق القائمة عند النقر عليها
                dropdownMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // معالج إرسال النموذج
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                calculateLotSize();
            });

            // تحديث النتائج عند تغيير القيم
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (resultSection.style.display !== 'none') {
                        calculateLotSize();
                    }
                });
            });

            /**
             * دالة حساب حجم اللوت للذهب
             */
            function calculateLotSize() {
                // الحصول على القيم
                const capital = parseFloat(document.getElementById('capital').value) || 0;
                const riskPercentage = parseFloat(document.getElementById('riskPercentage').value) || 0;
                const stopLossPips = parseFloat(document.getElementById('stopLossPips').value) || 0;

                // مسح التحذيرات السابقة
                warningSection.innerHTML = '';

                // التحقق من صحة البيانات
                if (!validateInputs(capital, riskPercentage, stopLossPips)) {
                    resultSection.style.display = 'none';
                    return;
                }

                // حساب المخاطرة بالدولار
                const riskAmount = capital * (riskPercentage / 100);

                // حساب حجم اللوت للذهب (XAUUSD)
                // في الذهب: قيمة النقطة للوت 1.00 = $10 لكل نقطة
                // المعادلة: اللوت المناسب = المخاطرة بالدولار / (عدد النقاط × قيمة النقطة للوت 1.00)
                // قيمة النقطة للوت 1.00 في الذهب = $10
                let lotSize = riskAmount / (stopLossPips * 10);

                // تطبيق الحدود
                if (lotSize < 0.01) {
                    lotSize = 0.01;
                    showWarning('تم تعديل اللوت للحد الأدنى (0.01)', 'warning');
                } else if (lotSize > 2.00) {
                    lotSize = 2.00;
                    showWarning('تم تعديل اللوت للحد الأقصى (2.00) للحماية', 'warning');
                }

                // تقريب اللوت لأقرب 0.01
                lotSize = Math.round(lotSize * 100) / 100;

                // حساب الخسارة الفعلية للذهب (XAUUSD)
                // الخسارة = اللوت × عدد النقاط × قيمة النقطة للوت 1.00
                // قيمة النقطة للوت 1.00 في الذهب = $10
                const actualLoss = lotSize * stopLossPips * 10;

                // عرض النتائج
                displayResults(lotSize, actualLoss, riskAmount, stopLossPips, capital, riskPercentage);
            }

            /**
             * دالة التحقق من صحة المدخلات
             */
            function validateInputs(capital, riskPercentage, stopLossPips) {
                if (capital <= 0) {
                    showWarning('يرجى إدخال رأس مال صحيح', 'danger');
                    return false;
                }

                if (capital < 50) {
                    showWarning('رأس المال أقل من الحد الأدنى المنصوح به ($50)', 'warning');
                }

                if (riskPercentage <= 0 || riskPercentage > 10) {
                    showWarning('نسبة المخاطرة يجب أن تكون بين 0.1% و 10%', 'danger');
                    return false;
                }

                if (riskPercentage > 5) {
                    showWarning('نسبة مخاطرة عالية! يُنصح بـ 1-3% للمبتدئين', 'warning');
                }

                if (stopLossPips <= 0 || stopLossPips > 1000) {
                    showWarning('عدد نقاط وقف الخسارة يجب أن يكون بين 1 و 1000', 'danger');
                    return false;
                }

                return true;
            }

            /**
             * دالة عرض النتائج
             */
            function displayResults(lotSize, actualLoss, riskAmount, stopLossPips, capital, riskPercentage) {
                // عرض اللوت
                lotResult.textContent = `${lotSize.toFixed(2)} لوت`;

                // عرض التفاصيل
                resultDetails.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <strong style="color: var(--accent-color);">ستخسر ${actualLoss.toFixed(2)}$ لو ضرب الستوب (${stopLossPips} نقطة)</strong>
                    </div>
                    <div style="font-size: 0.95rem; line-height: 1.8;">
                        <div>💰 رأس المال: $${capital.toFixed(2)}</div>
                        <div>⚠️ نسبة المخاطرة: ${riskPercentage}% = $${riskAmount.toFixed(2)}</div>
                        <div>📉 وقف الخسارة: ${stopLossPips} نقطة</div>
                        <div>📊 قيمة النقطة: $${(lotSize * 10).toFixed(2)} (للوت ${lotSize.toFixed(2)})</div>
                    </div>
                `;

                // إظهار النتائج
                resultSection.style.display = 'block';

                // تحذيرات إضافية
                if (actualLoss > riskAmount * 1.1) {
                    showWarning('الخسارة الفعلية أعلى من المخاطرة المحددة بسبب حدود اللوت', 'info');
                }
            }

            /**
             * دالة عرض التحذيرات
             */
            function showWarning(message, type = 'warning') {
                const alertClass = `alert-${type}`;
                const icon = type === 'danger' ? 'fas fa-exclamation-circle' :
                           type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

                const warningHtml = `
                    <div class="alert ${alertClass}">
                        <i class="${icon}" style="margin-left: 8px;"></i>
                        ${message}
                    </div>
                `;

                warningSection.innerHTML += warningHtml;
            }

            /**
             * دالة تنسيق الأرقام
             */
            function formatNumber(num) {
                return num.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }
        });
    </script>
</body>
</html>
